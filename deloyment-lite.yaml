# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-pms-lite-client
  namespace: ape-pms-lite
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-pms-lite-client
  template:
    metadata:
      labels:
        app: ape-pms-lite-client
    spec:
      containers:
        - name: ape-pms-lite-client
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-pms-lite-client:latest
          ports:
            - containerPort: 80
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh
---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-pms-lite-client
  namespace: ape-pms-lite
  labels:
    run: ape-pms-lite-client
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-pms-lite-client
