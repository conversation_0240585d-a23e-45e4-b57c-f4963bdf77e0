import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { HomeComponent } from './pages/home/<USER>'
import { DetailComponent } from './pages/detail/detail.component'
import { SupplierRegistrationComponent } from './pages/supplier-registration/supplier-registration.component'
import { ChangePasswordComponent } from './pages/change-password/change-password.component'
import { ForgotPasswordComponent } from './pages/forgot-password/forgot-password.component'
import { SupplierInfoComponent } from './pages/supplier-info/supplier-info.component'
import { EvaluationComponent } from './pages/evaluation/evaluation.component'
import { WelcomeNewUserComponent } from './pages/welcome-new-user/welcome-new-user.component'
import { BiddingHistoryComponent } from './pages/bidding-history/bidding-history.component'
import { ChangeUsernameComponent } from './pages/change-username/change-username.component'
import { CommonLayoutComponent } from './layouts/common-layout/common-layout.component'
import { PageNotFoundComponent } from './pages/page-not-found/page-not-found.component'
import { PageUnauthorizedComponent } from './pages/page-unauthorized/page-unauthorized.component'
import { EmptyLayoutComponent } from './layouts/empty-layout/empty-layout.component'
import { BidDealComponent } from './pages/bid-deal/bid-deal.component'
import { SupplierAdditionalCapacityComponent } from './pages/supplier-additional-capacity/supplier-additional-capacity.component'
import { LoginComponent } from './pages/login/login.component'
import { FaqCategoryComponent } from './pages/faq-category/faq-category.component'
import { FaqComponent } from './pages/faq/faq.component'
import { OpenLetterComponent } from './pages/open-letter/open-letter.compoment'
import { BidResetPriceComponent } from './pages/bid-reset-price/bid-reset-price.component'
import { BiddingComponent } from './pages/bidding/bidding.component'
import { PurchaseOrderComponent } from './pages/purchase-order/purchase-order.component'
import { BidAuctionComponent } from './pages/bid-auction/bid-auction.component'
import { AuthGuard } from './_helpers/auth.guard'
import { BaseGuard } from './_helpers/base.guard'
import { AuctionComponent } from './pages/auction/auction.component'
import { PriceQuoteComponent } from './pages/price-quote/price-quote.component'
import { BillComponent } from './pages/bill/bill.component'
import { PaymentComponent } from './pages/payment/payment.component'
import { AddOrEditPaymentComponent } from './pages/payment/add-or-edit-payment/add-or-edit-payment.component'
import { PaymentDetailComponent } from './pages/payment/payment-detail/payment-detail.component'
import { PriceQuoteDetailComponent } from './pages/price-quote/price-quote-detail/price-quote-detail.component'
import { OfferItemComponent } from './pages/price-quote/price-quote-detail/bidding-item/offer-item.component'
import { OfferDealComponent } from './pages/offer-deal/offer-deal.component'
import { InboundComponent } from './pages/inbound/inbound.component'
import { AddOrEditInboundComponent } from './pages/inbound/add-or-edit-inbound/add-or-edit-inbound.component'
import { InboundDetailComponent } from './pages/inbound/inbound-detail/inbound-detail.component'
const routes: Routes = [
  {
    path: '',
    component: CommonLayoutComponent,
    canActivate: [BaseGuard],
    children: [
      { path: '', pathMatch: 'full', redirectTo: '/home-page' },
      { path: 'home', component: HomeComponent },
      { path: 'home-page', component: HomeComponent },
      { path: 'forgot-password', component: ForgotPasswordComponent },
      { path: 'login', component: LoginComponent },
      { path: 'faq-category', component: FaqCategoryComponent },
      { path: 'faq', component: FaqComponent },
      { path: 'price-quote', component: PriceQuoteComponent },
      { path: 'price-quote/detail/:id', component: PriceQuoteDetailComponent },
      { path: 'price-quote/add-new', component: OfferItemComponent },
    ],
  },
  {
    path: '',
    component: EmptyLayoutComponent,
    canActivate: [BaseGuard],
    children: [
      { path: 'supplier-registration', component: SupplierRegistrationComponent },
      { path: 'open-letter', component: OpenLetterComponent },
    ],
  },
  {
    path: '',
    component: EmptyLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      { path: 'detail', component: DetailComponent },
      { path: 'bidding', component: BiddingComponent },
      { path: 'bid-deal', component: BidDealComponent },
      { path: 'offer-deal', component: OfferDealComponent },
      { path: 'bid-auction', component: BidAuctionComponent },
      { path: 'bid-price', component: BidResetPriceComponent },
      { path: 'supplier-additional-capacity', component: SupplierAdditionalCapacityComponent },
      { path: 'evaluation/:supplierExpertiseId', component: EvaluationComponent },
      { path: 'auction', component: AuctionComponent },
    ],
  },
  {
    path: '',
    component: CommonLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      { path: 'change-password', component: ChangePasswordComponent },
      { path: 'change-username', component: ChangeUsernameComponent },
      { path: 'supplier-info', component: SupplierInfoComponent },
      { path: 'bidding-history', component: BiddingHistoryComponent },
      { path: 'purchase-order', component: PurchaseOrderComponent },
      { path: 'bill', component: BillComponent },
      { path: 'payment', component: PaymentComponent },
      { path: 'payment/add', component: AddOrEditPaymentComponent },
      { path: 'payment/edit/:id', component: AddOrEditPaymentComponent },
      { path: 'payment/detail/:id', component: PaymentDetailComponent },
      { path: 'inbound', component: InboundComponent },
      { path: 'inbound/add', component: AddOrEditInboundComponent },
      { path: 'inbound/edit', component: AddOrEditInboundComponent },
      { path: 'inbound/detail', component: InboundDetailComponent },
    ],
  },
  { path: 'welcome-new-user', component: WelcomeNewUserComponent },
  { path: 'page-unauthorized', component: PageUnauthorizedComponent },
  { path: '**', component: PageNotFoundComponent, pathMatch: 'full' },
]

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
