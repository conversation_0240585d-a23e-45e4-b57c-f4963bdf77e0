import { Injectable } from '@angular/core';
import { Subscription } from 'rxjs';
import { CoreService } from './core.service';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {

  subscriptions: Subscription = new Subscription()

  constructor(
    private readonly storageService: StorageService,
    private readonly coreService: CoreService,
  ) { }

  public getData() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) {
        this.getLang()
      }
    })
  }

  public getLang() {
    return this.coreService.getLanguageV2()
  }
}