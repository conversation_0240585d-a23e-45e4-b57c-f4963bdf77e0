import { Injectable } from '@angular/core'
import { enumData } from '../base/enumData'
import { create, all } from 'mathjs'
const config = {}
const math = create(all, config)

// import * as moment from 'moment'
declare var $: any
declare var Object: any
@Injectable()
export class CoreService {
  constructor() {}

  public isEqual2Obj(objA: any, objB: any) {
    // Tạo các mảng chứa tên các property
    const aProps = Object.getOwnPropertyNames(objA)
    const bProps = Object.getOwnPropertyNames(objB)
    // Nếu độ dài của mảng không bằng nhau,
    // thì 2 objects đó không bằnh nhau.
    if (aProps.length !== bProps.length) {
      return false
    }

    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < aProps.length; i++) {
      const propName = aProps[i]
      // Nếu giá trị của cùng một property mà không bằng nhau,
      // thì 2 objects không bằng nhau.
      if (objA[propName] !== objB[propName]) {
        return false
      }
    }
    // Nếu code chạy đến đây,
    // tức là 2 objects được tính lằ bằng nhau.
    return true
  }

  public sumArray(arr: string | any[], prop: string | number) {
    let total = 0
    const len = arr.length
    for (let i = 0; i < len; i++) {
      total += arr[i][prop]
    }
    return total
  }

  public groupByArray(data: any[], key: any) {
    const groupedObj = data.reduce((prev: any, cur: any) => {
      if (!prev[cur[key]]) {
        prev[cur[key]] = [cur]
      } else {
        prev[cur[key]].push(cur)
      }
      return prev
    }, {})
    return Object.keys(groupedObj).map((Heading: any) => ({ heading: Heading, list: groupedObj[Heading] }))
  }

  /** Sort từ nhỏ tới lớn */
  // .sort((a, b) => parseFloat(a.sort) - parseFloat(b.sort))
  public dynamicSort(data: any[], key: any) {
    return data.sort(this.sortArray(key))
  }

  public sortArray(key: any) {
    let sortOrder = 1
    if (key[0] === '-') {
      sortOrder = -1
      key = key.substr(1)
    }
    return (a: any, b: any) => {
      const result = a[key] < b[key] ? -1 : a[key] > b[key] ? 1 : 0
      return result * sortOrder
    }
  }

  /** Sort từ lớn tới nhỏ */
  public sortBy(array: Array<any>, args: any) {
    if (array !== undefined) {
      array.sort((a: any, b: any) => {
        if (a[args] > b[args]) {
          return -1
        } else if (a[args] < b[args]) {
          return 1
        } else {
          return 0
        }
      })
    }
    return array
  }

  public convertObjToArray(obj: any) {
    const arr: any[] = []
    // tslint:disable-next-line:forin
    for (const key in obj) {
      const value = obj[key]
      arr.push(value)
    }
    return arr
  }

  public convertObjToParam(obj: any) {
    let str = ''
    // tslint:disable-next-line:forin
    for (const key in obj) {
      if (str !== '') {
        str += '&'
      }
      str += key + '=' + encodeURIComponent(obj[key])
    }
    return str
  }

  // public converDateToString(data) {
  //   return moment(data).format('YYYY-MM-DD')
  // }

  // public converDateTimeToString(data) {
  //   return moment(data).format('YYYY-MM-DD HH:mm:ss')
  // }

  // tslint:disable-next-line:no-shadowed-variable
  public getEnumName(code: any, enumData: object) {
    const data = this.convertObjToArray(enumData)
    const item = data.find((p) => p.code === code)
    return item ? item.name : ''
  }

  // public getSupplierServiceStatusName(code) {
  //   return this.getEnumName(code, enumData.SupplierServiceStatus)
  // }

  public getBidSupplierStatusName(code: any) {
    return this.getEnumName(code, enumData.BidSupplierStatus)
  }

  calFomular(fomular: string, lstField: any[], item: any) {
    let value = null
    const lstCol = lstField.filter((c) => c.type === enumData.DataType.Number.code)
    let tempFomular = fomular
    for (const col of lstCol) {
      tempFomular = tempFomular.replace('[' + col.code + ']', item[col.id])
    }
    // [qty] là trường "Số lượng" tĩnh của hạng mục
    tempFomular = tempFomular.replace(`[qty]`, item.number)

    try {
      value = math.evaluate(tempFomular)
      if (typeof value !== 'number') value = null
    } catch {
      return null
    } finally {
      return value
    }
  }

  getLanguage() {
    let rs: any[] = []
    if (localStorage.getItem('Language')) {
      let lang = localStorage.getItem('LanguageConfig')
      if (lang && lang.length > 0) {
        let value = JSON.parse(lang ? lang : '')
        if (value && value.length > 0) {
          var object = value.reduce((obj: any, item: any) => Object.assign(obj, { [item.key]: item.value }), {})
          rs = object
        }
      }
    }
    return rs
  }

  getLanguageByComponent(component: any) {
    let lang = localStorage.getItem('LanguageConfig')
    if (lang) {
      let value = JSON.parse(lang ? lang : '')
      if (value && value.length > 0) {
        var lstLangInCom = value.filter((s: any) => s.component === component)
        if (lstLangInCom.length > 0) {
          var object = lstLangInCom.reduce((obj: any, item: any) => Object.assign(obj, { [item.key]: item.value }), {})
          return object
        }
      }
    }
    return []
  }

  public getEnumElementName(enumData: object, value: string) {
    return this.getEnumElement(enumData, value, 'code', 'name')
  }

  public getEnumElement(enumData: object, value: string, keyIn: string, keyOut: string) {
    const data = this.convertObjToArray(enumData)
    const item = data.find((p) => p[keyIn] === value)
    return item && item[keyOut] ? item[keyOut] : ''
  }

  getLanguageV2() {
    if (localStorage.getItem('lang')) {
      let lang = localStorage.getItem('language')
      if (lang) {
        let value = JSON.parse(lang ? lang : '')
        let object = value.reduce((obj: any, item: any) => Object.assign(obj, { [item.key]: item.value }), {})
        return object
      }
    }
  }
}
