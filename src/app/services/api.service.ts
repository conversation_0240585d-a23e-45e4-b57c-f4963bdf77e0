import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { environment } from 'src/environments/environment'
@Injectable()
export class ApiService {
  host = environment.backEnd + '/'

  constructor(private http: HttpClient) {}

  //#region API Construct
  UploadUrl = `${this.host}uploadFiles/upload_single`
  CLIENT_WEB = {
    GET_FAQ: (id: any) => `client_web/get_faq/${id}`,
    GET_FAQ_CATEGORY: 'client_web/get_faq_category',

    CHECK_IS_YOUR_EXPERTISE: 'client_web/check_is_your_expertise',
    GET_DATA_SUGGEST: 'client_web/get_data_suggest',
    SUPPLIER_ACCEPT_CHANGE_DATA: 'client_web/supplier_accept_change_data',

    GET_BID_HISTORY_PRICE: (bidId: string) => {
      return `client_web/get_bid_history_price_client/${bidId}`
    },

    PAGINATION_HOMEPAGE: 'client_web/paginationHomePage',
    PAGINATION_HOMEPAGE_HAD_TOKEN: 'client_web/paginationHomePageHadToken',
    LOAD_DATA_BIDDING: 'client_web/loadDataBidding',
    BID_DETAIL_HAD_TOKEN: 'client_web/bidDetailHadToken',
    IS_DISPLAY_BTN_ACCEPT_BID: 'client_web/isDisplayBtnAcceptBid',
    IS_DISPLAY_BTN_BID: 'client_web/isDisplayBtnBid',
    ACCEPT_BID: 'client_web/acceptBid',
    REJECT_BID: 'client_web/rejectBid',
    CREATE_BID_SUPPLIER: 'client_web/createBidSupplier',
    CHECK_PERMISSION_LOAD_DATA_BID: 'client_web/checkPermissionLoadDataBid',
    LOAD_DATA_BID_TECH: 'client_web/loadDataBidTech',
    LOAD_DATA_BID_TRADE: 'client_web/loadDataBidTrade',
    LOAD_DATA_BID_PRICE: 'client_web/loadDataBidPrice',
    LOAD_DATA_BID_CUSTOMPRICE: 'client_web/loadDataBidCustomPrice',

    PAGINATION_BID_HISTORY: 'client_web/paginationBidHistory',

    CHECK_PERMISSION_JOIN_RESET_PRICE: 'client_web/checkPermissionJoinResetPrice',
    SAVE_RESET_PRICE: 'client_web/supplierSaveResetPrice',
    CREATE_OFFER_SUPPLIER: 'client_web/createOfferSupplier',
    LOAD_DATA_OFFER_TRADE: 'client_web/loadDataOfferTrade',
    LOAD_DATA_OFFER_TRADE_VIEW: 'client_web/loadDataOfferTradeView',

    LOAD_DATA_OFFER_PRICE: 'client_web/loadDataOfferPrice',
    LOAD_DATA_OFFER_PRICE_VIEW: 'client_web/loadDataOfferPriceView',

    LOAD_DATA_OFFER_CUSTOMPRICE: 'client_web/loadDataOfferCustomPrice',

    LOAD_DATA_OFFER_CUSTOMPRICE_VIEW: 'client_web/loadDataOfferCustomPriceView',
    GET_OFFER_DEAL: (bidDealId: string) => `client_web/get_offer_deal_supplier/${bidDealId}`,

    REJECT_OFFER: 'client_web/reject_offer_deal_supplier',

    ACCEPT_OFFER: `client_web/accept_offer_deal_supplier`,
    GET_BID_DEAL: (bidDealId: string) => `client_web/get_bid_deal_supplier/${bidDealId}`,
    CHECK_RESULT_MESSAGE_OFFER: (bidDealId: string) => {
      return `client_web/check_result_message_offer/${bidDealId}`
    },
    CHECK_RESULT_MESSAGE: (bidDealId: string) => {
      return `client_web/check_result_message/${bidDealId}`
    },
    ACCEPT: `client_web/accept_bid_deal_supplier`,
    REJECT: 'client_web/reject_bid_deal_supplier',

    GET_PERMISSION_BTN_SAVE_PRICE_BY_BIDID: (bidId: string) => {
      return `client_web/get_permission_btn_save_price_by_bidid/${bidId}`
    },
    LOAD_DATA_BID_PRICE2: 'client_web/load_data_bid_price',
    SUPPLIER_SAVE_PRICE: 'client_web/supplier_save_price',
    CURRENT_RANK: (bidId: string) => {
      return `client_web/get_current_rank/${bidId}`
    },

    GET_BID_AUCTION: (bidAuctionId: string) => `client_web/get_bid_auction_supplier/${bidAuctionId}`,
    SUPPLIER_SAVE_AUCTION: 'client_web/supplier_save_auction',
  }

  AUCTION = {
    GET_DETAIL: `auction/get_detail`,
    SUBMIT: 'auction/submit_data',
  }

  HOMEPAGE = {
    GET_BANNER: 'homePages/get_banner',
    GET_LINK: 'homePages/get_link',
    GET_SETTING_STRING: 'homePages/get_setting_string',
    GET_SERVICES: 'homePages/get_services',
    GET_NOTIFYS: (num: any) => `homePages/get_notifys/${num}`,
    GET_NOTIFY: (id: any) => `homePages/get_notify/${id}`,
  }

  SUPPLIER_REGISTRATION = {
    GET_SERVICES: 'supplierRegistrations/get_services',
    GET_SERVICES_CAN_ADD: 'supplierRegistrations/get_services_can_add',
    GET_SERVICE_CAPACITIES: 'supplierRegistrations/get_service_capacities',
    SUPPLIER_REGISTRATION: 'supplierRegistrations/supplier_registration',
    GET_SUPPLIER_INFO: 'supplierRegistrations/get_supplier_info',
    SUPPLIER_UPDATE_LAW: 'supplierRegistrations/supplier_update_law',
    SUPPLIER_UPDATE_CAPACITY: 'supplierRegistrations/supplier_update_capacity',
    SUPPLIER_ADD_CAPACITY: 'supplierRegistrations/supplier_add_capacity',
  }

  UPLOAD_FILE = {
    UPLOAD_SINGLE: 'uploadFiles/upload_single',
  }

  OFFER = {
    FIND: 'offer/detail_client',
    FIND_DETAIL_OFFER_SUPPLIER: 'offer/detail_offer_client',

    SEND_OFFER: 'offer/send_offer',
    ITEMTECHLISTDETAIL_CREATE: 'offer/list_detail_create_data',
    ITEMTECHLISTDETAIL_UPDATE: 'offer/list-detail_update_data',
    ITEMTECHLISTDETAIL_DELETE: 'offer/list-detail_delete_data',
    FIND_ALL: 'offer/find_all',
    GET_PRICE: 'offer/config_price_pagination',
    PAGINATION: 'offer/pagination',
    PAGINATION_CLIENT: 'offer/pagination_client',
    PAGINATION_CLIENT_NO_TOKEN: 'offer_client/pagination_client_no_token',
    FIND_NO_TOKEN: 'offer_client/detail_client',

    CREATE_CLIENT: 'offer/create_data_client',
    GET_OFFER_DEAL_ID: 'offer/get_offer_deal_id',

    UPDATE: 'offer/update_data',
    DELETE: 'offer/update_active',
    FIND_DETAIL: 'offer/delete_data',
    ITEMTECHLISTDETAIL_LIST: (itemTechId: string) => {
      return `offer/listdetail_list/${itemTechId}`
    },
  }

  SETTINGSTRING = { FIND: 'settingStrings/find' }

  LANGUAGE = {
    FIND: 'language/find',
    FIND_LANGUAGE_CONFIG: 'languageConfig/find_language_config',
  }

  LANGUAGE_KEY = {
    LOAD_DATA: `language_key/load_data`,
    UPDATE: `language_key/update_data`,
    REFRESH_DATA: `language_key/refresh_data`,
    IMPORT: `language_key/import_data`,
    LOAD_DATA_BY_TENANT: 'language_key/load_data_by_tenant',
  }

  PURCHASE_ORDER = {
    UPDATE_STATUS: 'client_web/update_status_order',
    UPDATE_ORDER: 'client_web/update_order_file',
    CREATE_INVOICE: 'client_web/create_data',
    ORDER_FILE_PAGINATION: 'client_web/order_file_pagination',
    UPDATE_DELIVERY_DATE: 'client_web/update_delivery_date',
    PAGINATION: 'client_web/paginationSupplier',
    UPDATE_DELIVERY: 'client_web/update_status_po_delivery',
    UPDATE_REFUSE: 'client_web/update_status_po_refuse',
    FIND_PAYMENT: 'paymentProgress/find',
    FIND_DETAIL: 'client_web/find_detail_po',
    FIND_DETAIL_FILE: 'client_web/pagination_invoice',
    UPDATE_CONFIRM: 'client_web/update_status_po_confirm',
    FIND: 'client_web/find_po',
    LOAD_PO_PRODUCT: 'client_web/load_po_product',
    FIND_CONTRACT_PO: 'client_web/find_contract_po',
    FIND_LIST_PO_CONTRACT: 'client_web/load_list_po_contract',
  }

  BILL = {
    FIND: `bill/find`,
    PAGINATION: 'bill/pagination',
    CREATE: 'bill/create_data',
    UPDATE: 'bill/update_data',
    UPDATE_ACTIVE: 'bill/update_active',
    IMPORT: 'bill/import_data',
    LOAD_PO: 'po/load_po_by_supplier',
    LOAD_CONTACT: 'contract/load_contract_by_supplier',
    LOAD_CURRENCY: 'currency/data_select_box',
    CANCEL: 'bill/update_cancel',
    SEND_BILL: 'bill/update_send',
    APPROVED: 'bill/update_approved',
    FIND_DETAIL: 'bill/find_detail',
    FIND_PO: 'bill/find_po',
    FIND_CONTRACT: 'bill/find_contract',
    FIND_BILL: 'bill/find_bill_by_supplier',
    GET_COMPANY: 'bill/load_companies_from_bizzi',
  }
  BILL_LOOKUP = {
    FIND: `bill_lookup/find`,
  }

  CONTRACT = {
    FIND: 'client_web/find_contract',
    FIND_MIRROR: 'client_web/find_mirror',
    UPDATE: 'client_web/update_data',
    LOAD_ITEM_CONTRACT: 'client_web/load_list_item',
  }

  PAYMENT = {
    PAGINATION: 'payment/pagination',
    CREATE: 'payment/create_data',
    FIND_DETAIL: 'payment/find_detail',
    CHECKING: 'payment/update_checking',
    FIND: `payment/find`,
    UPDATE: 'payment/update_data',
    UPDATE_ACTIVE: 'payment/update_active',
    IMPORT: 'payment/import_data',
    LOAD_PO: 'po/load_po_by_supplier',
    LOAD_CONTACT: 'contract/load_contract_by_supplier',
    LOAD_CURRENCY: 'currency/data_select_box',
    CANCEL: 'payment/update_cancel',
    SEND: 'payment/update_send',
    APPROVED: 'payment/update_approved',
    FIND_PO: 'payment/find_po',
    FIND_CONTRACT: 'payment/find_contract',
  }

  INBOUND_CLIENT = {
    LOAD_DETAIL: `inbound_client/load_detail`,
    FIND: `inbound_client/find`,
    PAGINATION: 'inbound_client/pagination',
    CREATE_DATA: 'inbound_client/create_data',
    UPDATE_DATA: 'inbound_client/update_data',
    UPDATE_STATUS: 'inbound_client/update_status',
    UPDATE_ACTIVE: 'inbound_client/update_active',
  }
  //#endregion

  //#region Handle

  post(url: string, data: any = {}) {
    // const token = this.authService.currentUserValue.accessToken
    // console.log(token)

    // const headers = new HttpHeaders()
    // headers.set('Content-Type', `application/json`)
    // headers.set('Authorization', `Bearer ${token}`)
    return (
      this.http
        .post(this.host + url, data) // .pipe(
        // map(result => {
        //   return result
        // })
        .toPromise()
        // tslint:disable-next-line: no-shadowed-variable
        .then((data) => {
          return data as any
        })
      // .catch(err => {
      //   // Kiểm tra có phải lỗi do server trả về hay không
      //   this.notifyService.showError(err)
      //   if (err.json) {
      //     // Kiểm tra lỗi server trả về có message hay không
      //     if (err.json() && err.json().message) {
      //       const message = err.json().message
      //       // Nếu message là TOKEN_EXPIRED, INVALID_ACCESS hay TOKEN_REQUIRED thì trở về trang Login
      //       if (message === 'TOKEN_EXPIRED' || message === 'INVALID_ACCESS' || message === 'TOKEN_REQUIRED') {
      //         this.router.navigate(['login'])
      //       }
      //     } else {
      //       console.log(err.json())
      //     }
      //   }
      // })
    )
  }

  objToQueryString = (obj: any) =>
    Object.keys(obj)
      .map((k) => {
        if (Array.isArray(obj[k])) {
          return `${k}=${JSON.stringify(obj[k])}`
        }
        return `${k}=${obj[k]}`
      })
      .join('&')

  get(url: string, data: any) {
    const query = this.objToQueryString(data)
    const newUrl = `${this.host + url}?${query}`
    return (
      this.http
        .get(newUrl)
        .toPromise()
        // tslint:disable-next-line: no-shadowed-variable
        .then((data) => {
          return data as any
        })
    )
  }
  //#endregion
}
