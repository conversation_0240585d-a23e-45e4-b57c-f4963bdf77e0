import { Injectable } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { BehaviorSubject, Observable } from 'rxjs'
import { map } from 'rxjs/operators'
import { User } from '../models/user.model'
import { environment } from 'src/environments/environment'
declare var $: any
@Injectable({ providedIn: 'root' })
export class AuthenticationService {
  private currentUserSubject: BehaviorSubject<User>
  public currentUser: Observable<User>
  host = environment.backEnd
  eventLogin = new BehaviorSubject<boolean>(false)
  constructor(private http: HttpClient) {
    let temp: any = null
    this.currentUserSubject = new BehaviorSubject<User>(JSON.parse(localStorage.getItem('currentUser') || temp))
    this.currentUser = this.currentUserSubject.asObservable()
  }

  public get currentUserValue(): User {
    return this.currentUserSubject.value
  }

  login(username: any, password: any) {
    return this.http.post(`${this.host}/auth/login-client`, { username, password }).pipe(
      map((user) => {
        // store user details and jwt token in local storage to keep user logged in between page refreshes
        localStorage.setItem('currentUser', JSON.stringify({ ...user, username }))
        this.currentUserSubject.next({ ...user, username } as User)
        return user
      })
    )
  }

  updatePassword(currentPassword: any, newPassword: any, confirmNewPassword: any) {
    return this.http.post(`${this.host}/auth/update-password`, { currentPassword, newPassword, confirmNewPassword }).pipe(
      map((data: any) => {
        return data
      })
    )
  }

  authorization() {
    return new Promise<User | void>((resolve, reject) => {
      this.http
        .post(`${this.host}/auth/authorization-client`, {})
        .toPromise()
        .then((user) => {
          let res = this.currentUserValue as User
          res = { ...res, ...user }
          localStorage.setItem('currentUser', JSON.stringify(res))
          this.currentUserSubject.next(res)
          if ($('#custom-header')) {
            $('#custom-header').trigger('click')
          }
          resolve(res)
        })
        .catch((err) => {
          // localStorage.removeItem('currentUser')
          // this.currentUserSubject.next(null)
          resolve()
        })
    })
  }

  updateUserName(currentPassword: any, newUsername: any) {
    return this.http.post(`${this.host}/auth/update-username`, { currentPassword, newUsername }).pipe(
      map((data) => {
        return data
      })
    )
  }

  sendConfirmCode(email: any) {
    return this.http.post(`${this.host}/auth/send-confirm-code`, { email }).pipe(
      map((data) => {
        return data
      })
    )
  }

  forgotPassword(email: any, confirmCode: any, newPassword: any, confirmNewPassword: any) {
    return this.http.post(`${this.host}/auth/forgot-password`, { email, confirmCode, newPassword, confirmNewPassword }).pipe(
      map((data) => {
        return data
      })
    )
  }

  logout() {
    localStorage.removeItem('Language')
    localStorage.removeItem('LanguageConfig')
    localStorage.removeItem('currentUser')
    let temp: any = null
    this.currentUserSubject.next(temp)
  }
}
