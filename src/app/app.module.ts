import { NgModule } from '@angular/core'

import { AppRoutingModule } from './app-routing.module'
import { AppComponent } from './app.component'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http'
import { BrowserModule, provideClientHydration } from '@angular/platform-browser'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { CommonModule, registerLocaleData } from '@angular/common'
import vi from '@angular/common/locales/vi'
import { PageHeaderComponent } from './pages/page-header/page-header.component'
import { PageFooterComponent } from './pages/page-footer/page-footer.component'
import { PageSiderLeftComponent } from './pages/page-sider-left/page-sider-left.component'
import { HomeComponent } from './pages/home/<USER>'
import { DetailComponent } from './pages/detail/detail.component'
import { SupplierRegistrationComponent } from './pages/supplier-registration/supplier-registration.component'
import { BasicAuthInterceptor } from './_helpers/basic-auth.interceptor'
import { ErrorInterceptor } from './_helpers/error.interceptor'
import { ApiService, CoreService, NotifyService, StorageService } from './services'
import { ChangePasswordComponent } from './pages/change-password/change-password.component'
import { ForgotPasswordComponent } from './pages/forgot-password/forgot-password.component'
import { SupplierInfoComponent } from './pages/supplier-info/supplier-info.component'
import { EvaluationComponent } from './pages/evaluation/evaluation.component'
import { BiddingItemComponent } from './pages/bidding/bidding-item/bidding-item.component'
import { BiddingTechComponent } from './pages/bidding/bidding-item/bidding-tech/bidding-tech.component'
import { BiddingTradeComponent } from './pages/bidding/bidding-item/bidding-trade/bidding-trade.component'
import { BiddingPriceComponent } from './pages/bidding/bidding-item/bidding-price/bidding-price.component'
import { BiddingCustomPriceComponent } from './pages/bidding/bidding-item/bidding-custom-price/bidding-custom-price.component'
import { WelcomeNewUserComponent } from './pages/welcome-new-user/welcome-new-user.component'
import { BiddingHistoryComponent } from './pages/bidding-history/bidding-history.component'
import { ChangeUsernameComponent } from './pages/change-username/change-username.component'
import { CommonLayoutComponent } from './layouts/common-layout/common-layout.component'
import { PageNotFoundComponent } from './pages/page-not-found/page-not-found.component'
import { PageUnauthorizedComponent } from './pages/page-unauthorized/page-unauthorized.component'
import { EmptyLayoutComponent } from './layouts/empty-layout/empty-layout.component'
import { BidDealComponent } from './pages/bid-deal/bid-deal.component'
import { SupplierAdditionalCapacityComponent } from './pages/supplier-additional-capacity/supplier-additional-capacity.component'
import { AddBiddingPriceModalComponent } from './pages/modal/add-bidding-price-model/add-bidding-price-modal.component'
import { LoginComponent } from './pages/login/login.component'
import { BiddingPriceListDetailModalComponent } from './pages/modal/bidding-price-list-detail-modal/bidding-price-list-detail-modal.component'
import { SupplierNotifyComponent } from './pages/supplier-notify/supplier-notify.component'
import { FaqCategoryComponent } from './pages/faq-category/faq-category.component'
import { FaqComponent } from './pages/faq/faq.component'
import { OpenLetterComponent } from './pages/open-letter/open-letter.compoment'
import { BidResetPriceComponent } from './pages/bid-reset-price/bid-reset-price.component'
import { CurrencyMaskConfig, CurrencyMaskModule, CURRENCY_MASK_CONFIG } from 'ng2-currency-mask'
import { MatDialogModule } from '@angular/material/dialog'
import { MatSnackBarModule } from '@angular/material/snack-bar'
import { NZ_I18N, vi_VN } from 'ng-zorro-antd/i18n'
import { NzCascaderModule } from 'ng-zorro-antd/cascader'
import { NzBadgeModule } from 'ng-zorro-antd/badge'
import { PdfViewerModule } from 'ng2-pdf-viewer'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzNotificationModule } from 'ng-zorro-antd/notification'
import { NzResultModule } from 'ng-zorro-antd/result'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzCollapseModule } from 'ng-zorro-antd/collapse'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzMenuModule } from 'ng-zorro-antd/menu'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzListModule } from 'ng-zorro-antd/list'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions'
import { NzCardModule } from 'ng-zorro-antd/card'
import { NzTabsModule } from 'ng-zorro-antd/tabs'
import { NzStepsModule } from 'ng-zorro-antd/steps'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { NzLayoutModule } from 'ng-zorro-antd/layout'
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm'
import { BidHistoryPriceComponent } from './pages/bidding-history/bid-history-price/bid-history-price.component'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { IconDefinition } from '@ant-design/icons-angular'
import * as AllIcons from '@ant-design/icons-angular/icons'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { BiddingComponent } from './pages/bidding/bidding.component'
import { PurchaseOrderComponent } from './pages/purchase-order/purchase-order.component'
import { BidAuctionComponent } from './pages/bid-auction/bid-auction.component'
const antDesignIcons = AllIcons as { [key: string]: IconDefinition }
const icons: IconDefinition[] = Object.keys(antDesignIcons).map((key: string) => antDesignIcons[key])
import { NzTagModule } from 'ng-zorro-antd/tag'
import { PurchaseOrderDetailComponent } from './pages/purchase-order/purchase-order-detail/purchase-order-detail.component'
import { AuctionComponent } from './pages/auction/auction.component'
import { OverlayContainer } from '@angular/cdk/overlay'
import { InAppRootOverlayContainer } from './in-app-root-overlay-container'
import { UpdateOrderComponent } from './pages/purchase-order/purchase-order-detail/update-order/update-order.component'
import { PriceQuoteComponent } from './pages/price-quote/price-quote.component'
import { PriceQuoteDetailComponent } from './pages/price-quote/price-quote-detail/price-quote-detail.component'
import { BillComponent } from './pages/bill/bill.component'
import { AddOrEditBillComponent } from './pages/bill/add-or-edit-bill/add-or-edit-bill.component'
import { BillDetailComponent } from './pages/bill/bill-detail/bill-detail.component'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { PaymentComponent } from './pages/payment/payment.component'
import { AddOrEditPaymentComponent } from './pages/payment/add-or-edit-payment/add-or-edit-payment.component'
import { PaymentDetailComponent } from './pages/payment/payment-detail/payment-detail.component'
import { OfferCustomPriceComponent } from './pages/price-quote/price-quote-detail/bidding-item/offer-custom-price/offer-custom-price.component'
import { OfferDealComponent } from './pages/offer-deal/offer-deal.component'
import { OfferPriceComponent } from './pages/price-quote/price-quote-detail/bidding-item/offer-price/offer-price.component'
import { OfferTradeComponent } from './pages/price-quote/price-quote-detail/bidding-item/offer-trade/offer-trade.component'
import { OfferItemComponent } from './pages/price-quote/price-quote-detail/bidding-item/offer-item.component'
import { InboundComponent } from './pages/inbound/inbound.component'
import { InboundDetailComponent } from './pages/inbound/inbound-detail/inbound-detail.component'
import { AddOrEditInboundComponent } from './pages/inbound/add-or-edit-inbound/add-or-edit-inbound.component'
export const CustomCurrencyMaskConfig: CurrencyMaskConfig = {
  align: 'left',
  allowNegative: true,
  decimal: '.',
  precision: 0,
  prefix: '',
  suffix: '',
  thousands: ',',
}

registerLocaleData(vi)

@NgModule({
  exports: [MatDialogModule, MatSnackBarModule],
  declarations: [],
})
export class MaterialModule {}

@NgModule({
  declarations: [
    AppComponent,
    PageHeaderComponent,
    PageFooterComponent,
    PageSiderLeftComponent,
    PriceQuoteComponent,
    PriceQuoteDetailComponent,
    HomeComponent,
    DetailComponent,
    SupplierRegistrationComponent,
    ChangePasswordComponent,
    ForgotPasswordComponent,
    SupplierInfoComponent,
    SupplierNotifyComponent,
    EvaluationComponent,
    BiddingComponent,
    BiddingItemComponent,
    BiddingTechComponent,
    BiddingTradeComponent,
    BiddingPriceComponent,
    BiddingCustomPriceComponent,
    WelcomeNewUserComponent,
    BiddingHistoryComponent,
    BidHistoryPriceComponent,
    ChangeUsernameComponent,
    CommonLayoutComponent,
    PageNotFoundComponent,
    PageUnauthorizedComponent,
    EmptyLayoutComponent,
    UpdateOrderComponent,
    BidDealComponent,
    SupplierAdditionalCapacityComponent,
    BidAuctionComponent,
    AddBiddingPriceModalComponent,
    BiddingPriceListDetailModalComponent,
    LoginComponent,
    FaqCategoryComponent,
    FaqComponent,
    OpenLetterComponent,
    BidResetPriceComponent,
    PurchaseOrderComponent,
    PurchaseOrderDetailComponent,
    AuctionComponent,
    BillComponent,
    AddOrEditBillComponent,
    BillDetailComponent,
    PaymentComponent,
    AddOrEditPaymentComponent,
    PaymentDetailComponent,
    OfferCustomPriceComponent,
    OfferDealComponent,
    OfferPriceComponent,
    OfferTradeComponent,
    OfferItemComponent,
    OfferDealComponent,
    OfferPriceComponent,
    InboundComponent,
    InboundDetailComponent,
    AddOrEditInboundComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    NzIconModule.forRoot(icons),
    FormsModule,
    HttpClientModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    CurrencyMaskModule,
    MaterialModule,
    NzBadgeModule,
    PdfViewerModule,
    NzCascaderModule,
    NzModalModule,
    NzUploadModule,
    NzNotificationModule,
    NzResultModule,
    NzSelectModule,
    NzToolTipModule,
    NzFormModule,
    NzInputModule,
    NzCollapseModule,
    NzPopoverModule,
    NzMenuModule,
    NzTableModule,
    NzListModule,
    NzButtonModule,
    NzPaginationModule,
    NzDescriptionsModule,
    NzCardModule,
    NzTabsModule,
    NzStepsModule,
    NzInputNumberModule,
    NzPopconfirmModule,
    NzLayoutModule,
    NzDatePickerModule,
    CommonModule,
    NzTagModule,
    NzRadioModule,
  ],
  providers: [
    { provide: NZ_I18N, useValue: vi_VN },
    { provide: HTTP_INTERCEPTORS, useClass: BasicAuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    { provide: CURRENCY_MASK_CONFIG, useValue: CustomCurrencyMaskConfig },
    { provide: OverlayContainer, useClass: InAppRootOverlayContainer },
    ApiService,
    NotifyService,
    CoreService,
    StorageService,
    provideClientHydration(),
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
