
export interface MediaModel {
  name?: string,
  href?: string,
  url?: string,
  atr?: string,
  type?: string,
  description?: string,
}

export interface LinkClient {
  name?: string,
  url?: string,
  description?: string,
}


export interface SettingStringClient {
  name?: string,
  type?: string,
  description?: string,
}

export interface Department {
  id: number,
  name?: string,
  code?: string,
  level?: number,
  description?: string,
  isLast?: string,
}

export interface Employee {
  id: number,
  name?: string,
  code?: string,
  level?: number,
  description?: string,
  isLast?: string,
}
