.app-layout {
  min-height: 100vh;
}

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 195px;
  padding: 0;
  background: #ffff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.ant-layout-header {
  line-height: 50px;
}

.app-footer {
  color: black;
  padding: 10px 0 0;
  /* background: url('../assets/img/bg-footer.png') no-repeat; */
  background: url('../../../assets/img/bg-footer.png') no-repeat;
  background-size: 100% 100%;
}

.inner-content {
  padding: 5px;
  min-height: 100%;
}
.bg-content {
  margin-top: 100px !important;
  padding: 24px;
  background: #ffff;
}

.inner-sider-left {
  padding: 5px;
}
.bg-left {
  background: #ffff;
}

.inner-sider-right {
  padding: 5px;
}
.bg-right {
  background: #ffff;
}

@media (min-width: 576px) {
  .new-line-mobile {
    display: nonel;
  }
}

.main-div {
  width: 100%;
}
