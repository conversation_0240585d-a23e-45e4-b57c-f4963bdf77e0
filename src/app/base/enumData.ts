export const enumData = {
  /** <PERSON><PERSON><PERSON> ngôn ngữ */
  LanguageType: {
    VI: { code: 'VI', name: '<PERSON><PERSON><PERSON><PERSON> Nam' },
    EN: { code: 'E<PERSON>', name: 'Tiếng An<PERSON>' },
  },
  /** <PERSON><PERSON><PERSON> thước tối đa tính bằng MB */
  maxSizeUpload: 30,
  Page: {
    pageIndex: 1,
    pageSize: 10,
    pageSizeMax: 10000,
    total: 0,
  },
  Constants: {
    Model_Edit: 'Cập nhật',
    Model_Add: 'Thêm mới',
    Message_Create_Success: 'Thêm mới thành công!',
    Message_Update_Success: 'Cập nhật thành công!',
    Message_Import_Success: 'Nhập excel thành công!',
  },
  Currency: {
    VND: { code: 'VND', name: 'Triệu', description: '' },
    USD: { code: 'USD', name: 'usd', description: '' },
  },
  UserType: {
    super: { code: 'SUPERADMI<PERSON>', name: 'SuperAdmin', description: '' },
    admin: { code: 'ADMI<PERSON>', name: 'Admin', description: '' },
    employee: { code: 'EMPL<PERSON>Y<PERSON>', name: 'Employee', description: '' },
    customer: { code: 'CUSTOMER', name: 'Customer', description: '' },
  },
  SettingStringType: {
    address: 'address',
    company: 'company',
    masterBidForm: 'masterBidForm',
    masterBidGuarantee: 'masterBidGuarantee',
    unit: 'unit',
    currency: 'currency',
  },
  SettingStringClientType: {
    BannerName: { code: 'BannerName', name: '', description: '' },
    Footer1: { code: 'Footer1', name: '', description: '' },
    Footer2: { code: 'Footer2', name: '', description: '' },
    Footer3: { code: 'Footer3', name: '', description: '' },
    Footer4: { code: 'Footer4', name: '', description: '' },
  },

  BannerClientType: {
    Advertise: { code: 'Advertise', name: '', description: '' },
    BannerName: { code: 'BannerName', name: '', description: '' },
  },

  BannerClientPosition: {
    Left: { code: 'Left', name: 'Bên trái', description: '' },
    Right: { code: 'Right', name: 'Bên phải', description: '' },
    Top: { code: 'Top', name: 'Bên trên', description: '' },
  },

  lstSettingStringClientType: [
    { code: 'BannerName', name: '', description: '' },
    { code: 'Footer1', name: '', description: '' },
    { code: 'Footer2', name: '', description: '' },
    { code: 'Footer3', name: '', description: '' },
    { code: 'Footer4', name: '', description: '' },
  ],

  lstBannerClientType: [
    { code: 'Advertise', name: '', description: '' },
    { code: 'BannerName', name: '', description: '' },
  ],

  lstBannerClientPosition: [
    { code: 'Left', name: 'Bên trái', description: '' },
    { code: 'Right', name: 'Bên phải', description: '' },
    { code: 'Top', name: 'Bên trên', description: '' },
  ],

  DataType: {
    String: { code: 'String', name: 'Free Text', description: '' },
    Number: { code: 'Number', name: 'Số', description: '' },
    File: { code: 'File', name: 'File', description: '' },
    List: { code: 'List', name: 'Danh sách', description: '' },
    Date: { code: 'Date', name: 'Ngày giờ', description: '' },
    Address: { code: 'Address', name: 'Địa chỉ', description: '' },
    Km: { code: 'Km', name: 'Khoảng cách (km)', description: '' },
    Time: { code: 'Time', name: 'Thời gian di chuyển (giờ)', description: '' },
  },

  BidStatus: {
    GoiThauTam: {
      code: 'GoiThauTam',
      name: 'Gói thầu tạm',
      description: '',
    },
    ChoDuyetGoiThauTam: {
      code: 'ChoDuyetGoiThauTam',
      name: 'Chờ duyệt gói thầu tạm',
      description: '',
    },
    DangCauHinhGoiThau: {
      code: 'DangCauHinhGoiThau',
      name: 'Mới tạo',
      description: '',
    },
    DangChonNCC: {
      code: 'DangChonNCC',
      name: 'Đang chọn nhà cung cấp',
      description: '',
    },
    TuChoiGoiThau: {
      code: 'TuChoiGoiThau',
      name: 'Từ chối gói thầu',
      description: '',
    },
    DangDuyetGoiThau: {
      code: 'DangDuyetGoiThau',
      name: 'Đang duyệt gói thầu',
      description: '',
    },
    DangNhanBaoGia: {
      code: 'DangNhanBaoGia',
      name: 'Đang mời thầu',
      description: '',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đã mở thầu',
      description: '',
    },
    DangDuyetDanhGia: {
      code: 'DangDuyetDanhGia',
      name: 'Đang duyệt đánh giá thầu',
      description: '',
    },
    HoanTatDanhGia: {
      code: 'HoanTatDanhGia',
      name: 'Hoàn tất đánh giá thầu',
      description: '',
    },
    // DangChonNCCDamPhan: {
    //   code: 'DangChonNCCDamPhan',
    //   name: 'Đang chọn NCC để đàm phán giá',
    //   description: '',
    // },
    DangDamPhanGia: {
      code: 'DangDamPhanGia',
      name: 'Đang đàm phán giá',
      description: '',
    },
    DongDamPhanGia: {
      code: 'DongDamPhanGia',
      name: 'Hoàn tất đàm phán giá',
      description: '',
    },
    // DangChonNCCDauGia: {
    //   code: 'DangChonNCCDauGia',
    //   name: 'Đang chọn NCC để đấu giá',
    //   description: '',
    // },
    DangDauGia: { code: 'DangDauGia', name: 'Đang đấu giá', description: '' },
    DongDauGia: {
      code: 'DongDauGia',
      name: 'Hoàn tất đấu giá',
      description: '',
    },
    // DangThamDinh: {
    //   code: 'DangThamDinh',
    //   name: 'Đang thẩm định',
    //   description: '',
    // },
    // DangDuyetThamDinh: {
    //   code: 'DangDuyetThamDinh',
    //   name: 'Đang duyệt thẩm định',
    //   description: '',
    // },
    DongThau: { code: 'DongThau', name: 'Đang duyệt NCC thắng thầu', description: '' },
    DuyetNCCThangThau: { code: 'DuyetNCCThangThau', name: 'Đã duyệt NCC thắng thầu', description: '' },
    DangDuyetKetThucThau: { code: 'DangDuyetKetThucThau', name: 'Đang duyệt kết thúc thầu', description: '' },
    HoanTat: { code: 'HoanTat', name: 'Hoàn tất', description: '' },
    Huy: { code: 'Huy', name: 'Huỷ', description: '' },
  },
  BidSupplierStatus: {
    DaDuocChon: {
      code: 'DaDuocChon',
      name: 'Đã được chọn tham gia gói thầu',
      description: '',
    },
    DaThongBaoMoiThau: {
      code: 'DaThongBaoMoiThau',
      name: 'Đã gửi thông báo mời thầu',
      description: '',
    },
    DaXacNhanKhongThamGiaThau: {
      code: 'DaXacNhanKhongThamGiaThau',
      name: 'Đã xác nhận không tham gia thầu',
      description: '',
    },
    DaXacNhanThamGiaThau: {
      code: 'DaXacNhanThamGiaThau',
      name: 'Đã xác nhận tham gia thầu',
      description: '',
    },
    DaHoanThanhBoSungHoSo: {
      code: 'DaHoanThanhBoSungHoSo',
      name: 'Đã hoàn thành bổ sung hồ sơ',
      description: '',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
      description: '',
    },
    DaDanhGia: {
      code: 'DaDanhGia',
      name: 'Đã đánh giá',
      description: '',
    },
  },
  BidSupplierTechStatus: {
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
      description: '',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
      description: '',
    },
    HoSoHopLe: {
      code: 'HoSoHopLe',
      name: 'Hồ sơ hợp lệ',
      description: '',
    },
    HoSoKhongHopLe: {
      code: 'HoSoKhongHopLe',
      name: 'Hồ sơ không hợp lệ',
      description: '',
    },
  },
  BidSupplierPriceStatus: {
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
      description: '',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
      description: '',
    },
    HoSoHopLe: {
      code: 'HoSoHopLe',
      name: 'Hồ sơ hợp lệ',
      description: '',
    },
    HoSoKhongHopLe: {
      code: 'HoSoKhongHopLe',
      name: 'Hồ sơ không hợp lệ',
      description: '',
    },
  },
  BidSupplierTradeStatus: {
    ChuaXacNhan: {
      code: 'ChuaXacNhan',
      name: 'Chưa xác nhận',
      description: '',
    },
    DangDanhGia: {
      code: 'DangDanhGia',
      name: 'Đang đánh giá',
      description: '',
    },
    HoSoHopLe: {
      code: 'HoSoHopLe',
      name: 'Hồ sơ hợp lệ',
      description: '',
    },
    HoSoKhongHopLe: {
      code: 'HoSoKhongHopLe',
      name: 'Hồ sơ không hợp lệ',
      description: '',
    },
  },
  ColType: {
    MPO: { code: 'MPO', name: 'Nhân viên', description: '' },
    Supplier: { code: 'Supplier', name: 'Nhà cung cấp', description: '' },
  },
  SupplierServiceStatus: {
    ChuaDangKy: {
      code: 'ChuaDangKy',
      name: 'NCC chưa đăng ký đang chờ bổ sung',
      description: 'Được tạo ra khi mời thầu nhưng chưa kịp đăng ký mới',
    },
    MoiDangKy: { code: 'MoiDangKy', name: 'LVKD mới đang chờ xét duyệt', description: '' },
    CapNhatThongTin: {
      code: 'CapNhatThongTin',
      name: 'NCC điều chỉnh thông tin đang chờ xét duyệt',
      description: '',
    },
    PhuTrachDuyet: {
      code: 'PhuTrachDuyet',
      name: 'Đã duyệt bước 1',
      description: '',
    },
    PhuTrachKhongDuyet: {
      code: 'PhuTrachKhongDuyet',
      name: 'Không được duyệt bước 1',
      description: '',
    },
    DaDuyet: { code: 'DaDuyet', name: 'Đã là thành viên', description: '' },
    KhongDuyet: { code: 'KhongDuyet', name: 'Không được duyệt', description: '' },
    // DangThamDinh: {
    //   code: 'DangThamDinh',
    //   name: 'Đang thẩm định',
    //   description: '',
    // },
    // DaThamDinh: { code: 'DaThamDinh', name: 'Đã thẩm định', description: '' },
    // KhongDuyetQT2: { code: 'KhongDuyetQT2', name: 'Không duyệt NCC', description: '' },
    NgungHoatDong: { code: 'NgungHoatDong', name: 'Ngừng hoạt động', description: '' },
  },

  ContractTypePo: {
    NonContract: { code: 'NONCONTRACT', name: 'Không theo hợp đồng' },
    Contract: { code: 'CONTRACT', name: 'Theo hợp đồng' },
  },

  BillStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    CANCEL: { code: 'CANCEL', name: 'Hủy', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    PROCESS: { code: 'PROCESS', name: 'Đang xử lý', color: 'green', bgColor: '#fff1f0', borderColor: '#ffa39e' },
    WAIT_CONFIRM: { code: 'WAIT_CONFIRM', name: 'Chờ xác nhận', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CONFIRMED: { code: 'CONFIRMED', name: 'Đã xác nhận', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  BillPaymentStatus: {
    NEW: { code: 'NEW', name: 'Chưa thanh toán', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    PAID: { code: 'PAID', name: 'Đã thanh toán', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
  },

  /** Nguồn tham chiếu hóa đơn */
  referencesInvoice: {
    C: { code: 'C', name: 'Theo hợp đồng' },
    P: { code: 'P', name: 'Theo PO' },
  },
  PaymentStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0064D9', bgColor: '#99C1F0', borderColor: '#0064D9' },
    CHECKING: { code: 'CHECKING', name: 'Đang kiểm tra hồ sơ', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    WAIT_APPROVE: { code: 'WAIT_APPROVE', name: 'Chờ duyệt', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    CONFIRMED: { code: 'CONFIRMED', name: 'Đã xác nhận', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    REQUEST_CONFIRM: { code: 'REQUEST_CONFIRM', name: 'Yêu cầu xác nhận lại', color: '#BA066C', bgColor: '#EEC1DA', borderColor: '#BA066C' },
    PAYING: { code: 'PAYING', name: 'Đang thanh toán', color: '#D8A800', bgColor: '#ddcc8c', borderColor: '#D8A800' },
    PAID: { code: 'PAID', name: 'Đã thanh toán', color: '#006D6A', bgColor: '#F9F2D9', borderColor: '#006D6A' },
    REQUEST_RECHECK: { code: 'REQUEST_RECHECK', name: 'Yêu cầu kiểm tra lại', color: '#AA0808', bgColor: '#E9BFBF', borderColor: '#AA0808' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', description: 'Đã duyệt', color: '#008000', bgColor: '#DEF2E0', borderColor: '#00AA25' },
  },

  /** Trạng thái phiếu nhập kho  */
  InboundStatus: {
    NEW: { code: 'NEW', name: 'Mới tạo', color: '#0063D8', bgColor: '#DCEEFF', borderColor: '#2A7DDF' },
    WAITING: { code: 'WAITING', name: 'Chờ nhận hàng', color: '#ED9A1F', bgColor: '#FCF0DD', borderColor: '#F5CA89' },
    IMPORTED: { code: 'IMPORTED', name: 'Đã nhập kho', color: '#0A915B', bgColor: '#DEF2E0', borderColor: '#0A915B' },
    APPROVED: { code: 'APPROVED', name: 'Đã duyệt', color: '#00AA25', bgColor: '#0A915B', borderColor: '#00AA25' },
    CANCEL: { code: 'CANCEL', name: 'Đã huỷ', color: 'red', bgColor: '#fff1f0', borderColor: '#ffa39e' },
  },
}
