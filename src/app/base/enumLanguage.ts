export const enumLanguage = {
  /** <PERSON>ại ngôn ngữ */
  LangugeType: {
    VI: { code: 'VI', name: '<PERSON><PERSON><PERSON><PERSON> Nam' },
    EN: { code: 'EN', name: 'Tiếng An<PERSON>' },
  },
  /** Key ngôn ngữ */
  LanguageKey: {
    //#start BID_AUCTION
    BidAuction_BiddingPackage: {
      key: 'BidAuction_BiddingPackage',
      value: 'Đấu giá gói thầu:',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đấu giá gói thầu:',
    },
    BidAuction_ExportExcel: { key: 'BidAuction_ExportExcel', value: 'Xuất excel', languageType: 'VI', path: 'all_view', description: 'Xuất excel' },
    BidAuction_ImportExcel: { key: 'BidAuction_ImportExcel', value: 'Nhập excel', languageType: 'VI', path: 'all_view', description: 'Nhập excel' },
    BidAuction_CurrentStatus: {
      key: 'BidAuction_CurrentStatus',
      value: 'Th<PERSON> hạng hiện tại',
      languageType: 'VI',
      path: 'all_view',
      description: '<PERSON><PERSON><PERSON> hạng hiện tại',
    },
    BidAuction_NotParticipated: {
      key: 'BidAuction_NotParticipated',
      value: 'Chưa tham gia',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa tham gia',
    },
    BidAuction_NumberOfCompaniesParticipated: {
      key: 'BidAuction_NumberOfCompaniesParticipated',
      value: 'Tổng công ty tham gia',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tổng công ty tham gia',
    },
    BidAuction_IndexName: { key: 'BidAuction_IndexName', value: 'Tên hạng mục', languageType: 'VI', path: 'all_view', description: 'Tên hạng mục' },
    BidAuction_CountUnit: { key: 'BidAuction_CountUnit', value: 'Đơn vị tính', languageType: 'VI', path: 'all_view', description: 'Đơn vị tính' },
    BidAuction_CurrencyUnit: {
      key: 'BidAuction_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    BidAuction_Quantity: { key: 'BidAuction_Quantity', value: 'Số lượng', languageType: 'VI', path: 'all_view', description: 'Số lượng' },
    BidAuction_MaximumPrice: { key: 'BidAuction_MaximumPrice', value: 'Giá tối đa', languageType: 'VI', path: 'all_view', description: 'Giá tối đa' },
    BidAuction_InputedValue: {
      key: 'BidAuction_InputedValue',
      value: 'Giá trị đã nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá trị đã nhập',
    },
    BidAuction_UnitPrice: { key: 'BidAuction_UnitPrice', value: 'Đơn giá', languageType: 'VI', path: 'all_view', description: 'Đơn giá' },
    BidAuction_DetailInformation: {
      key: 'BidAuction_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chi tiết',
    },
    BidAuction_BackToHome: {
      key: 'BidAuction_BackToHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đi tới trang chủ',
    },
    BidAuction_SaveAuction: { key: 'BidAuction_SaveAuction', value: 'Lưu đấu giá', languageType: 'VI', path: 'all_view', description: 'Lưu đấu giá' },
    BidAuction_Index: {
      key: 'BidAuction_Index',
      value: 'Hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hạng mục',
    },
    BidAuction_InputNotCorrect: {
      key: 'BidAuction_InputNotCorrect',
      value: 'nhập đơn giá không hợp lệ.',
      languageType: 'VI',
      path: 'all_view',
      description: 'nhập đơn giá không hợp lệ.',
    },
    BidAuction_InputOverMaximumPrice: {
      key: 'BidAuction_InputOverMaximumPrice',
      value: 'nhập đơn giá vượt quá giá tối đa.',
      languageType: 'VI',
      path: 'all_view',
      description: 'nhập đơn giá vượt quá giá tối đa.',
    },
    BidAuction_InputMustGreaterThanZero: {
      key: 'BidAuction_InputMustGreaterThanZero',
      value: 'nhập đơn giá phải lớn hơn 0.',
      languageType: 'VI',
      path: 'all_view',
      description: 'nhập đơn giá phải lớn hơn 0.',
    },
    BidAuction_DoYouWantToFinish: {
      key: 'BidAuction_DoYouWantToFinish',
      value: 'Bạn có thực sự muốn hoàn tất hồ sơ đã điền?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có thực sự muốn hoàn tất hồ sơ đã điền?',
    },
    BidAuction_MakeSureAllTheInformationIsCorrectBeforeSubmit: {
      key: 'BidAuction_MakeSureAllTheInformationIsCorrectBeforeSubmit',
      value: 'Hồ sơ đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
      languageType: 'VI',
      path: 'all_view',
      description:
        'Hồ sơ đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
    },
    BidAuction_SendAuctionSuccess: {
      key: 'BidAuction_SendAuctionSuccess',
      value: 'Gửi đấu giá thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gửi đấu giá thành công!',
    },
    BidAuction_TemplateInputItem: {
      key: 'BidAuction_TemplateInputItem',
      value: 'Template nhập đấu giá Item',
      languageType: 'VI',
      path: 'all_view',
      description: 'Template nhập đấu giá Item',
    },
    BidAuction_FileTemplateNotCorrect: {
      key: 'BidAuction_FileTemplateNotCorrect',
      value: 'File không đúng template đấu giá của gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'File không đúng template đấu giá của gói thầu',
    },
    //#end BID_AUCTION
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BID_DEAL
    BidDeal_DealBiddingPrice: {
      key: 'BidDeal_DealBiddingPrice',
      value: 'Đàm phán giá gói thầu: ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đàm phán giá gói thầu',
    },
    BidDeal_DetailPriceFile: {
      key: 'BidDeal_DetailPriceFile',
      value: 'File chi tiết giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'File chi tiết giá',
    },
    BidDeal_PleaseUploadDetailPriceFile: {
      key: 'BidDeal_PleaseUploadDetailPriceFile',
      value: 'Vui lòng upload File chi tiết giá!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng upload File chi tiết giá!',
    },
    BidDeal_TechincalDetailFile: {
      key: 'BidDeal_TechincalDetailFile',
      value: 'File chi tiết kỹ thuật',
      languageType: 'VI',
      path: 'all_view',
      description: 'File chi tiết kỹ thuật',
    },
    BidDeal_IfHave: { key: 'BidDeal_IfHave', value: '(Nếu có)', languageType: 'VI', path: 'all_view', description: '(Nếu có)' },
    BidDeal_PleaseUploadTechnicalDetailFile: {
      key: 'BidDeal_PleaseUploadTechnicalDetailFile',
      value: 'Vui lòng upload File chi tiết kỹ thuật!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng upload File chi tiết kỹ thuật!',
    },
    BidDeal_LinkDriverToAdditionalFile: {
      key: 'BidDeal_LinkDriverToAdditionalFile',
      value: 'Link driver các file bổ sung (Nếu có)',
      languageType: 'VI',
      path: 'all_view',
      description: 'Link driver các file bổ sung (Nếu có)',
    },
    BidDeal_UploadFile: { key: 'BidDeal_UploadFile', value: 'Upload File', languageType: 'VI', path: 'all_view', description: 'Upload File' },
    BidDeal_ViewAttachedFile: {
      key: 'BidDeal_ViewAttachedFile',
      value: 'Xem file đính kèm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xem file đính kèm',
    },
    BidDeal_ExportExcel: { key: 'BidDeal_ExportExcel', value: 'Xuất excel', languageType: 'VI', path: 'all_view', description: 'Xuất excel' },
    BidDeal_ImportExcel: { key: 'BidDeal_ImportExcel', value: 'Nhập excel', languageType: 'VI', path: 'all_view', description: 'Nhập excel' },
    BidDeal_IndexName: { key: 'BidDeal_IndexName', value: 'Tên hạng mục', languageType: 'VI', path: 'all_view', description: 'Tên hạng mục' },
    BidDeal_CountUnit: { key: 'BidDeal_CountUnit', value: 'Đơn vị tính', languageType: 'VI', path: 'all_view', description: 'Đơn vị tính' },
    BidDeal_CurrencyUnit: {
      key: 'BidDeal_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    BidDeal_Quantity: { key: 'BidDeal_Quantity', value: 'Số lượng', languageType: 'VI', path: 'all_view', description: 'Số lượng' },
    BidDeal_MaximumPrice: { key: 'BidDeal_MaximumPrice', value: 'Giá tối đa', languageType: 'VI', path: 'all_view', description: 'Giá tối đa' },
    BidDeal_AgreePrice: { key: 'BidDeal_AgreePrice', value: 'Giá đã chào', languageType: 'VI', path: 'all_view', description: 'Giá đã chào' },
    BidDeal_SuggestedPrice: { key: 'BidDeal_SuggestedPrice', value: 'Giá đề nghị', languageType: 'VI', path: 'all_view', description: 'Giá đề nghị' },
    BidDeal_DetailInformation: {
      key: 'BidDeal_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chi tiết',
    },
    BidDeal_SuggestDealPrice: {
      key: 'BidDeal_SuggestDealPrice',
      value: 'Đề nghị đàm phán giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đề nghị đàm phán giá',
    },
    BidDeal_AgreeDealPrice: {
      key: 'BidDeal_AgreeDealPrice',
      value: 'Chấp nhận giá đề nghị',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chấp nhận giá đề nghị',
    },
    BidDeal_DenyDealPrice: {
      key: 'BidDeal_DenyDealPrice',
      value: 'Từ chối giá đề nghị',
      languageType: 'VI',
      path: 'all_view',
      description: 'Từ chối giá đề nghị',
    },
    BidDeal_Save: { key: 'BidDeal_Save', value: 'Lưu', languageType: 'VI', path: 'all_view', description: 'Lưu' },
    BidDeal_BackToHome: {
      key: 'BidDeal_BackToHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đi tới trang chủ',
    },
    BidDeal_SureToSuggestDealPrice: {
      key: 'BidDeal_SureToSuggestDealPrice',
      value: 'Bạn có chắc muốn đề nghị giá đàm phán?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có chắc muốn đề nghị giá đàm phán?',
    },
    BidDeal_SureToAgreeDealPrice: {
      key: 'BidDeal_SureToAgreeDealPrice',
      value: 'Bạn có chắc muốn chấp nhận giá đề nghị?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có chắc muốn chấp nhận giá đề nghị?',
    },
    BidDeal_SureToDenyDealPriceAndEndBidding: {
      key: 'BidDeal_SureToDenyDealPriceAndEndBidding',
      value: 'Bạn có chắc muốn từ chối giá đề nghị và kết thúc tham gia gói thầu?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có chắc muốn từ chối giá đề nghị và kết thúc tham gia gói thầu?',
    },
    BidDeal_InputedValue: {
      key: 'BidDeal_InputedValue',
      value: 'Giá trị đã nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá trị đã nhập',
    },
    BidDeal_UnitPrice: { key: 'BidDeal_UnitPrice', value: 'Đơn giá', languageType: 'VI', path: 'all_view', description: 'Đơn giá' },
    BidDeal_TemplateInputDealPriceItem: {
      key: 'BidDeal_TemplateInputDealPriceItem',
      value: 'Template nhập đàm phán giá Item',
      languageType: 'VI',
      path: 'all_view',
      description: 'Template nhập đàm phán giá Item',
    },
    BidDeal_DealPriceNotInputCannotSave: {
      key: 'BidDeal_DealPriceNotInputCannotSave',
      value: 'Chưa nhập giá đàm phán, không thể lưu.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa nhập giá đàm phán, không thể lưu.',
    },
    BidDeal_DealPriceInputAllowToActionDealPrice: {
      key: 'BidDeal_DealPriceInputAllowToActionDealPrice',
      value: 'Đã nhập giá đàm phán, vui lòng thao tác "Đề nghị đàm phán giá".',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đã nhập giá đàm phán, vui lòng thao tác "Đề nghị đàm phán giá".',
    },
    BidDeal_Index: { key: 'BidDeal_Index', value: 'Hạng mục', languageType: 'VI', path: 'all_view', description: 'Hạng mục' },
    BidDeal_DealPriceNotInput: {
      key: 'BidDeal_DealPriceNotInput',
      value: 'chưa nhập giá đàm phán.',
      languageType: 'VI',
      path: 'all_view',
      description: 'chưa nhập giá đàm phán.',
    },
    BidDeal_DealPriceMustGreaterThanZero: {
      key: 'BidDeal_DealPriceMustGreaterThanZero',
      value: 'cần chỉnh giá lớn hơn 0.',
      languageType: 'VI',
      path: 'all_view',
      description: 'cần chỉnh giá lớn hơn 0.',
    },
    BidDeal_DealPriceMustNotExceedAgreePrice: {
      key: 'BidDeal_DealPriceMustNotExceedAgreePrice',
      value: 'cần chỉnh giá đàm phán không vượt quá giá đã chào.',
      languageType: 'VI',
      path: 'all_view',
      description: 'cần chỉnh giá đàm phán không vượt quá giá đã chào.',
    },
    BidDeal_DealPriceMustNotExceedMaximumPrice: {
      key: 'BidDeal_DealPriceMustNotExceedMaximumPrice',
      value: 'cần chỉnh giá đàm phán không vượt quá giá tối đa.',
      languageType: 'VI',
      path: 'all_view',
      description: 'cần chỉnh giá đàm phán không vượt quá giá tối đa.',
    },
    BidDeal_FileExceedMaxSize: {
      key: 'BidDeal_FileExceedMaxSize',
      value: 'Vượt qua kích thước tối đa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vượt qua kích thước tối đa',
    },
    BidDeal_FileTemplateNotCorrect: {
      key: 'BidDeal_FileTemplateNotCorrect',
      value: 'File không đúng template nhập đàm phán giá của gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'File không đúng template nhập đàm phán giá của gói thầu',
    },
    //#end BID_DEAL
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BID_RESET_PRICE
    BidResetPrice_DetailPriceFile: {
      key: 'BidResetPrice_DetailPriceFile',
      value: 'File chi tiết giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'File chi tiết giá',
    },
    BidResetPrice_PleaseUploadDetailPriceFile: {
      key: 'BidResetPrice_PleaseUploadDetailPriceFile',
      value: 'Vui lòng upload File chi tiết giá!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng upload File chi tiết giá!',
    },
    BidResetPrice_TechincalDetailFile: {
      key: 'BidResetPrice_TechincalDetailFile',
      value: 'File chi tiết kỹ thuật',
      languageType: 'VI',
      path: 'all_view',
      description: 'File chi tiết kỹ thuật',
    },
    BidResetPrice_PleaseUploadTechnicalDetailFile: {
      key: 'BidResetPrice_PleaseUploadTechnicalDetailFile',
      value: 'Vui lòng upload File chi tiết kỹ thuật!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng upload File chi tiết kỹ thuật!',
    },
    BidResetPrice_ViewAttachedFile: {
      key: 'BidResetPrice_ViewAttachedFile',
      value: 'Xem file đính kèm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xem file đính kèm',
    },
    BidResetPrice_ExportExcel: {
      key: 'BidResetPrice_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xuất excel',
    },
    BidResetPrice_ImportExcel: {
      key: 'BidResetPrice_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập excel',
    },
    BidResetPrice_IndexName: {
      key: 'BidResetPrice_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên hạng mục',
    },
    BidResetPrice_CountUnit: {
      key: 'BidResetPrice_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tính',
    },
    BidResetPrice_CurrencyUnit: {
      key: 'BidResetPrice_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    BidResetPrice_Quantity: { key: 'BidResetPrice_Quantity', value: 'Số lượng', languageType: 'VI', path: 'all_view', description: 'Số lượng' },
    BidResetPrice_UnitPrice: { key: 'BidResetPrice_UnitPrice', value: 'Đơn giá', languageType: 'VI', path: 'all_view', description: 'Đơn giá' },
    BidResetPrice_DetailInformation: {
      key: 'BidResetPrice_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chi tiết',
    },
    BidResetPrice_RequestAdditionalPrice: {
      key: 'BidResetPrice_RequestAdditionalPrice',
      value: 'Yêu cầu bảng giá bổ sung sau',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu bảng giá bổ sung sau',
    },
    BidResetPrice_SubmitAdditionalPrice: {
      key: 'BidResetPrice_SubmitAdditionalPrice',
      value: 'Nộp chào giá bổ sung',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nộp chào giá bổ sung',
    },
    BidResetPrice_NoAccessGrantOrTimeOutForPackage: {
      key: 'BidResetPrice_NoAccessGrantOrTimeOutForPackage',
      value: 'Bạn không có quyền truy cập hoặc đã ngưng nộp giá bổ sung cho gói thầu.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn không có quyền truy cập hoặc đã ngưng nộp giá bổ sung cho gói thầu.',
    },
    BidResetPrice_BackToHome: {
      key: 'BidResetPrice_BackToHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đi tới trang chủ',
    },
    BidResetPrice_SubmitAdditionalSuccessfully: {
      key: 'BidResetPrice_SubmitAdditionalSuccessfully',
      value: 'giá bổ sung thành công.',
      languageType: 'VI',
      path: 'all_view',
      description: 'giá bổ sung thành công.',
    },
    BidResetPrice_TemplateSuggestPrice: {
      key: 'BidResetPrice_TemplateSuggestPrice',
      value: 'Template nhập bảng chào giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Template nhập bảng chào giá',
    },
    BidResetPrice_FileTemplateNotCorrect: {
      key: 'BidResetPrice_FileTemplateNotCorrect',
      value: 'File không đúng template chào giá của gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'File không đúng template chào giá của gói thầu',
    },
    BidResetPrice_FileExceedMaxSize: {
      key: 'BidResetPrice_FileExceedMaxSize',
      value: 'Vượt qua kích thước tối đa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vượt qua kích thước tối đa',
    },
    BidResetPrice_PleaseInput: {
      key: 'BidResetPrice_PleaseInput',
      value: 'Vui lòng nhập dữ liệu trước',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập dữ liệu trước',
    },
    BidResetPrice_PriceGreaterThanZero: {
      key: 'BidResetPrice_PriceGreaterThanZero',
      value: 'Vui lòng nhập giá lớn hơn 0',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập giá lớn hơn 0',
    },
    BidResetPrice_NotFilledInformation: {
      key: 'BidResetPrice_NotFilledInformation',
      value: 'Chưa nhập dữ liệu hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa nhập dữ liệu hạng mục',
    },
    BidResetPrice_NotFilledEnoughInformation: {
      key: 'BidResetPrice_NotFilledEnoughInformation',
      value: 'Chưa điền đủ thông tin hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa điền đủ thông tin hạng mục',
    },
    BidResetPrice_PleaseInputRequiredColumn: {
      key: 'BidResetPrice_PleaseInputRequiredColumn',
      value: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
    },
    BidResetPrice_DataInputNotCorrect: {
      key: 'BidResetPrice_DataInputNotCorrect',
      value: 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại.',
    },
    BidResetPrice_SubmitPriceSuccessfullly: {
      key: 'BidResetPrice_SubmitPriceSuccessfullly',
      value: 'Đã bổ sung bảng chào giá thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đã bổ sung bảng chào giá thành công!',
    },
    BidResetPrice_DoYouWantToFinish: {
      key: 'BidResetPrice_DoYouWantToFinish',
      value: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
    },
    BidResetPrice_MakeSureAllTheInformationIsCorrectBeforeSubmit: {
      key: 'BidResetPrice_MakeSureAllTheInformationIsCorrectBeforeSubmit',
      value:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
      languageType: 'VI',
      path: 'all_view',
      description:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
    },
    BidResetPrice_DetailFileNotUpload: {
      key: 'BidResetPrice_DetailFileNotUpload',
      value: 'Chưa upload File chi tiết giá.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa upload File chi tiết giá.',
    },
    BidResetPrice_TechnicalDetailFileNotUpload: {
      key: 'BidResetPrice_TechnicalDetailFileNotUpload',
      value: 'Chưa upload File chi tiết kỹ thuật.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa upload File chi tiết kỹ thuật.',
    },
    BidResetPrice_PleaseInputValueGreaterThanZero: {
      key: 'BidResetPrice_PleaseInputValueGreaterThanZero',
      value: 'Vui lòng nhập giá lớn hơn 0',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập giá lớn hơn 0',
    },

    //#end BID_RESET_PRICE
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BIDDING_CUSTOM_PRICE
    BiddingCustomPrice_AddIndexConfigPrice: {
      key: 'BiddingCustomPrice_AddIndexConfigPrice',
      value: 'Thêm hạng mục cơ cấu giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thêm hạng mục cơ cấu giá',
    },
    BiddingCustomPrice_ExportExcel: {
      key: 'BiddingCustomPrice_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xuất excel',
    },
    BiddingCustomPrice_ImportExcel: {
      key: 'BiddingCustomPrice_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập excel',
    },
    BiddingCustomPrice_IndexName: {
      key: 'BiddingCustomPrice_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên hạng mục',
    },
    BiddingCustomPrice_CountUnit: {
      key: 'BiddingCustomPrice_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tính',
    },
    BiddingCustomPrice_CurrencyUnit: {
      key: 'BiddingCustomPrice_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    BiddingCustomPrice_Quantity: {
      key: 'BiddingCustomPrice_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng',
    },
    BiddingCustomPrice_UnitPrice: {
      key: 'BiddingCustomPrice_UnitPrice',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn giá',
    },
    BiddingCustomPrice_Select: {
      key: 'BiddingCustomPrice_Select',
      value: 'Tuỳ chọn',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tuỳ chọn',
    },
    BiddingCustomPrice_DeleteIndex: {
      key: 'BiddingCustomPrice_DeleteIndex',
      value: 'Xoá hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xoá hạng mục',
    },
    BiddingCustomPrice_RequestConfigAdditionalPrice: {
      key: 'BiddingCustomPrice_RequestConfigAdditionalPrice',
      value: 'Yêu cầu cơ cấu giá bổ sung sau',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu cơ cấu giá bổ sung sau',
    },
    BiddingCustomPrice_IsRequired: {
      key: 'BiddingCustomPrice_IsRequired',
      value: 'Bắt buộc?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bắt buộc?',
    },
    BiddingCustomPrice_TemplateDocumentConfigPrice: {
      key: 'BiddingCustomPrice_TemplateDocumentConfigPrice',
      value: 'Template nhập hồ sơ cơ cấu giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Template nhập hồ sơ cơ cấu giá',
    },
    BiddingCustomPrice_DownloadTemplateExcelSuccessfully: {
      key: 'BiddingCustomPrice_DownloadTemplateExcelSuccessfully',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tải template thành công!',
    },
    BiddingCustomPrice_FileTemplateNotCorrect: {
      key: 'BiddingCustomPrice_FileTemplateNotCorrect',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'all_view',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },
    BiddingCustomPrice_IndexNameMustNotBeNull: {
      key: 'BiddingCustomPrice_IndexNameMustNotBeNull',
      value: 'Tên hạng mục không được để trống',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên hạng mục không được để trống',
    },
    BiddingCustomPrice_CountUnitNotExist: {
      key: 'BiddingCustomPrice_CountUnitNotExist',
      value: 'Đơn vị tính không tồn tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tính không tồn tại',
    },
    BiddingCustomPrice_CurrencyUnitNotExist: {
      key: 'BiddingCustomPrice_CurrencyUnitNotExist',
      value: 'Đơn vị tiền tệ không tồn tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ không tồn tại',
    },
    BiddingCustomPrice_InputNumber: {
      key: 'BiddingCustomPrice_InputNumber',
      value: 'Số lượng là số, không được để trống',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng là số, không được để trống',
    },
    BiddingCustomPrice_UnitPriceNotNull: {
      key: 'BiddingCustomPrice_UnitPriceNotNull',
      value: 'Đơn giá là bắt buộc không được để trống!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn giá là bắt buộc không được để trống!',
    },
    BiddingCustomPrice_UnitPriceNotNumber: {
      key: 'BiddingCustomPrice_UnitPriceNotNumber',
      value: 'Đơn giá không phải kiểu Number',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn giá không phải kiểu Number',
    },

    //#end BIDDING_CUSTOM_PRICE
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BIDDING_PRICE
    BiddingPrice_ExportExcel: {
      key: 'BiddingPrice_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xuất excel',
    },
    BiddingPrice_ImportExcel: {
      key: 'BiddingPrice_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập excel',
    },
    BiddingPrice_IndexName: {
      key: 'BiddingPrice_IndexName',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên hạng mục',
    },
    BiddingPrice_CountUnit: {
      key: 'BiddingPrice_CountUnit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tính',
    },
    BiddingPrice_CurrencyUnit: {
      key: 'BiddingPrice_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    BiddingPrice_Quantity: {
      key: 'BiddingPrice_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng',
    },
    BiddingPrice_UnitPrice: {
      key: 'BiddingPrice_UnitPrice',
      value: 'Đơn giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn giá',
    },
    BiddingPrice_DetailInformation: {
      key: 'BiddingPrice_DetailInformation',
      value: 'Thông tin chi tiết',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chi tiết',
    },
    BiddingPrice_RequestAdditionalPrice: {
      key: 'BiddingPrice_RequestAdditionalPrice',
      value: 'Yêu cầu bảng giá bổ sung sau',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu bảng giá bổ sung sau',
    },
    BiddingPrice_ItemNotSettleIndexOfSuggestPrice: {
      key: 'BiddingPrice_ItemNotSettleIndexOfSuggestPrice',
      value: 'Item chưa có thiết lập hạng mục chào giá.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Item chưa có thiết lập hạng mục chào giá.',
    },
    BiddingPrice_TemplateForInputSuggestedPrice: {
      key: 'BiddingPrice_TemplateForInputSuggestedPrice',
      value: 'Template nhập bảng chào giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Template nhập bảng chào giá',
    },
    BiddingPrice_DownloadTemplateExcelSuccessfully: {
      key: 'BiddingPrice_DownloadTemplateExcelSuccessfully',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tải template thành công!',
    },
    BiddingPrice_FileTemplateNotCorrect: {
      key: 'BiddingPrice_FileTemplateNotCorrect',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'all_view',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },
    BiddingPrice_Index: {
      key: 'BiddingPrice_Index',
      value: 'Hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hạng mục',
    },
    BiddingPrice_Column: {
      key: 'BiddingPrice_Column',
      value: 'cột',
      languageType: 'VI',
      path: 'all_view',
      description: 'cột',
    },
    BiddingPrice_NotNull: {
      key: 'BiddingPrice_NotNull',
      value: 'không được để trống',
      languageType: 'VI',
      path: 'all_view',
      description: 'không được để trống',
    },
    BiddingPrice_InputNumber: {
      key: 'BiddingPrice_InputNumber',
      value: 'phải nhập số',
      languageType: 'VI',
      path: 'all_view',
      description: 'phải nhập số',
    },
    BiddingPrice_IsRequired: {
      key: 'BiddingPrice_IsRequired',
      value: 'Bắt buộc?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bắt buộc?',
    },

    //#end BIDDING_PRICE
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BIDDING_TECH
    BiddingTech_ExportExcel: {
      key: 'BiddingTech_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xuất excel',
    },
    BiddingTech_ImportExcel: {
      key: 'BiddingTech_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập excel',
    },
    BiddingTech_Criteria: {
      key: 'BiddingTech_Criteria',
      value: 'Tiêu chí',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu chí',
    },
    BiddingTech_DataType: {
      key: 'BiddingTech_DataType',
      value: 'Kiểu dữ liệu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Kiểu dữ liệu',
    },
    BiddingTech_Value: {
      key: 'BiddingTech_Value',
      value: 'Giá trị',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá trị',
    },
    BiddingTech_RequestAdditionalTechnical: {
      key: 'BiddingTech_RequestAdditionalTechnical',
      value: 'Yêu cầu kỹ thuật bổ sung sau',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu kỹ thuật bổ sung sau',
    },
    BiddingTech_FileExceedMaxSize: {
      key: 'BiddingTech_FileExceedMaxSize',
      value: 'Kích thước tối đa để upload là {maxSizeUpload}MB, vui lòng chọn file khác',
      languageType: 'VI',
      path: 'all_view',
      description: 'Kích thước tối đa để upload là {maxSizeUpload}MB, vui lòng chọn file khác',
    },
    BiddingTech_ItemNotSettleTachnicalCriteria: {
      key: 'BiddingTech_ItemNotSettleTachnicalCriteria',
      value: 'Item chưa có thiết lập tiêu chí kỹ thuật.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Item chưa có thiết lập tiêu chí kỹ thuật.',
    },
    BiddingTech_NoticeAtFileField: {
      key: 'BiddingTech_NoticeAtFileField',
      value: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    BiddingTech_DownloadTemplateExcelSuccessfully: {
      key: 'BiddingTech_DownloadTemplateExcelSuccessfully',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tải template thành công!',
    },
    BiddingTech_TemplateTechnicalDocument: {
      key: 'BiddingTech_TemplateTechnicalDocument',
      value: 'Template nhập hồ sơ kỹ thuật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Template nhập hồ sơ kỹ thuật',
    },
    BiddingTech_FileTemplateNotCorrect: {
      key: 'BiddingTech_FileTemplateNotCorrect',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'all_view',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },
    BiddingTech_NotType: {
      key: 'BiddingTech_NotType',
      value: 'không phải kiểu',
      languageType: 'VI',
      path: 'all_view',
      description: 'không phải kiểu',
    },
    BiddingTech_DateFormatYYYY_MM_DD: {
      key: 'BiddingTech_DateFormatYYYY_MM_DD',
      value: 'ngày phải có định dạng yyyy-mm-dd',
      languageType: 'VI',
      path: 'all_view',
      description: 'ngày phải có định dạng yyyy-mm-dd',
    },
    BiddingTech_NotInList: {
      key: 'BiddingTech_NotInList',
      value: 'không nằm trong List',
      languageType: 'VI',
      path: 'all_view',
      description: 'không nằm trong List',
    },

    //#end BIDDING_TECH
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BIDDING_TRADE
    BiddingTrade_ExportExcel: {
      key: 'BiddingTrade_ExportExcel',
      value: 'Xuất excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xuất excel',
    },
    BiddingTrade_ImportExcel: {
      key: 'BiddingTrade_ImportExcel',
      value: 'Nhập excel',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập excel',
    },
    BiddingTrade_Criteria: {
      key: 'BiddingTrade_Criteria',
      value: 'Tiêu chí',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu chí',
    },
    BiddingTrade_DataType: {
      key: 'BiddingTrade_DataType',
      value: 'Kiểu dữ liệu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Kiểu dữ liệu',
    },
    BiddingTrade_Value: {
      key: 'BiddingTrade_Value',
      value: 'Giá trị',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá trị',
    },
    BiddingTrade_RequestAdditionTradeCondition: {
      key: 'BiddingTrade_RequestAdditionTradeCondition',
      value: 'Yêu cầu điều kiện thương mại bổ sung sau',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu điều kiện thương mại bổ sung sau',
    },
    BiddingTrade_ItemNotHaveTradeCondition: {
      key: 'BiddingTrade_ItemNotHaveTradeCondition',
      value: 'Item chưa có thiết lập ĐKTM.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Item chưa có thiết lập ĐKTM.',
    },
    BiddingTrade_NoticeAtFileField: {
      key: 'BiddingTrade_NoticeAtFileField',
      value: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    BiddingTrade_TemplateTradeConditionDocument: {
      key: 'BiddingTrade_TemplateTradeConditionDocument',
      value: 'Template nhập hồ sơ ĐKTM.xlsx',
      languageType: 'VI',
      path: 'all_view',
      description: 'Template nhập hồ sơ ĐKTM.xlsx',
    },
    BiddingTrade_DownloadTemplateExcelSuccessfully: {
      key: 'BiddingTrade_DownloadTemplateExcelSuccessfully',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tải template thành công!',
    },
    BiddingTrade_FileTemplateNotCorrect: {
      key: 'BiddingTrade_FileTemplateNotCorrect',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'all_view',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },
    BiddingTrade_IsRequired: {
      key: 'BiddingTrade_IsRequired',
      value: 'Bắt buộc?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bắt buộc?',
    },
    BiddingTrade_NotNull: {
      key: 'BiddingTrade_NotNull',
      value: 'bắt buộc không được để trống!',
      languageType: 'VI',
      path: 'all_view',
      description: 'bắt buộc không được để trống!',
    },
    BiddingTrade_DateFormatYYYY_MM_DD: {
      key: 'BiddingTrade_DateFormatYYYY_MM_DD',
      value: ' ngày phải có định dạng yyyy-mm-dd',
      languageType: 'VI',
      path: 'all_view',
      description: ' ngày phải có định dạng yyyy-mm-dd',
    },
    BiddingTrade_NotType: {
      key: 'BiddingTrade_NotType',
      value: 'không phải kiểu',
      languageType: 'VI',
      path: 'all_view',
      description: 'không phải kiểu',
    },
    BiddingTrade_NotInList: {
      key: 'BiddingTrade_NotInList',
      value: ' không nằm trong List',
      languageType: 'VI',
      path: 'all_view',
      description: ' không nằm trong List',
    },

    //#end BIDDING_TRADE
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BIDDING_ITEM
    BiddingItem_TechnicalReuire: {
      key: 'BiddingItem_TechnicalReuire',
      value: 'Yêu cầu kỹ thuật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu kỹ thuật',
    },
    BiddingItem_SuggestPrice: {
      key: 'BiddingItem_SuggestPrice',
      value: 'Chào giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chào giá',
    },
    BiddingItem_ConfigPrice: {
      key: 'BiddingItem_ConfigPrice',
      value: 'Cơ cấu giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Cơ cấu giá',
    },
    BiddingItem_TradeCondition: {
      key: 'BiddingItem_TradeCondition',
      value: 'Điều kiện thương mại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Điều kiện thương mại',
    },
    BiddingItem_Prev: {
      key: 'BiddingItem_Prev',
      value: 'Quay lại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Quay lại',
    },
    BiddingItem_Next: {
      key: 'BiddingItem_Next',
      value: 'Tiếp tục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiếp tục',
    },
    BiddingItem_SubmitDocument: {
      key: 'BiddingItem_SubmitDocument',
      value: 'Nộp hồ sơ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nộp hồ sơ',
    },
    BiddingItem_DoYouWantToFinish: {
      key: 'BiddingItem_DoYouWantToFinish',
      value: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?',
    },
    BiddingItem_MakeSureAllTheInformationIsCorrectBeforeSubmit: {
      key: 'BiddingItem_MakeSureAllTheInformationIsCorrectBeforeSubmit',
      value:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
      languageType: 'VI',
      path: 'all_view',
      description:
        'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận',
    },
    BiddingItem_PleaseInput: {
      key: 'BiddingItem_PleaseInput',
      value: 'Vui lòng nhập dữ liệu trước',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập dữ liệu trước',
    },
    BiddingItem_NotFilledInformation: {
      key: 'BiddingItem_NotFilledInformation',
      value: 'Chưa nhập dữ liệu hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa nhập dữ liệu hạng mục',
    },
    BiddingItem_PriceGreaterThanZero: {
      key: 'BiddingItem_PriceGreaterThanZero',
      value: 'giá phải lớn hơn 0',
      languageType: 'VI',
      path: 'all_view',
      description: 'giá phải lớn hơn 0',
    },
    BiddingItem_PleaseInputValueGreaterThanZero: {
      key: 'BiddingItem_PleaseInputValueGreaterThanZero',
      value: 'Vui lòng nhập giá lớn hơn 0',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập giá lớn hơn 0',
    },
    BiddingItem_PleaseInputRequiredColumn: {
      key: 'BiddingItem_PleaseInputRequiredColumn',
      value: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng điền đủ thông tin các cột động bắt buộc',
    },
    BiddingItem_NotFilledEnoughInformation: {
      key: 'BiddingItem_NotFilledEnoughInformation',
      value: 'Chưa điền đủ thông tin hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa điền đủ thông tin hạng mục',
    },

    //#end BIDDING_ITEM
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start BIDDING
    Bidding_IndexNumber: {
      key: 'Bidding_IndexNumber',
      value: 'STT',
      languageType: 'VI',
      path: 'all_view',
      description: 'STT',
    },
    Bidding_ItemName: {
      key: 'Bidding_ItemName',
      value: 'Tên Item',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên Item',
    },
    Bidding_Quantity: {
      key: 'Bidding_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng',
    },
    Bidding_Status: {
      key: 'Bidding_Status',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái',
    },
    Bidding_SubmitDocumentPackage: {
      key: 'Bidding_SubmitDocumentPackage',
      value: 'Nhập hồ sơ thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập hồ sơ thầu',
    },
    Bidding_NoAccessGrant: {
      key: 'Bidding_NoAccessGrant',
      value: 'ông có quyền truy cập',
      languageType: 'VI',
      path: 'all_view',
      description: 'ông có quyền truy cập',
    },
    Bidding_BackToHome: {
      key: 'Bidding_BackToHome',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đi tới trang chủ',
    },
    Bidding_SubmitDocumentSuccessfully: {
      key: 'Bidding_SubmitDocumentSuccessfully',
      value: 'Nộp hồ sơ thành công',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nộp hồ sơ thành công',
    },
    Bidding_SelectItemForPackage: {
      key: 'Bidding_SelectItemForPackage',
      value: 'Chọn Item để nhập hồ sơ gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chọn Item để nhập hồ sơ gói thầu',
    },
    Bidding_CannotDetectPackage: {
      key: 'Bidding_CannotDetectPackage',
      value: 'Không xác định được gói thầu!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Không xác định được gói thầu!',
    },

    //#end BIDDING
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start CHANGE_PASSWORD
    ChangePassword_CurrentPassword: {
      key: 'ChangePassword_CurrentPassword',
      value: 'Mật khẩu hiện tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu hiện tại',
    },
    ChangePassword_PleaseInputCurrentPassword: {
      key: 'ChangePassword_PleaseInputCurrentPassword',
      value: 'Vui lòng nhập mật khẩu hiện tại!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập mật khẩu hiện tại!',
    },
    ChangePassword_NewPassword: {
      key: 'ChangePassword_NewPassword',
      value: 'Mật khẩu mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu mới',
    },
    ChangePassword_PleaseInputNewPassword: {
      key: 'ChangePassword_PleaseInputNewPassword',
      value: 'Vui lòng nhập mật khẩu mới!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập mật khẩu mới!',
    },
    ChangePassword_PleaseReinputNewPassword: {
      key: 'ChangePassword_PleaseReinputNewPassword',
      value: 'Vui lòng nhập lại mật khẩu mới!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập lại mật khẩu mới!',
    },
    ChangePassword_PasswordNotMatch: {
      key: 'ChangePassword_PasswordNotMatch',
      value: 'Mật khẩu không khớp!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu không khớp!',
    },
    ChangePassword_ChangePassword: {
      key: 'ChangePassword_ChangePassword',
      value: 'Đổi mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đổi mật khẩu',
    },

    //#end CHANGE_PASSWORD
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start CHANGE_USERNAME
    ChangeUsername_CurrentPassword: {
      key: 'ChangeUsername_CurrentPassword',
      value: 'Mật khẩu hiện tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu hiện tại',
    },
    ChangeUsername_PleaseInputCurrentPassword: {
      key: 'ChangeUsername_PleaseInputCurrentPassword',
      value: 'Vui lòng nhập mật khẩu hiện tại!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập mật khẩu hiện tại!',
    },
    ChangeUsername_NewUsername: {
      key: 'ChangeUsername_NewUsername',
      value: 'Tên đăng nhập mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên đăng nhập mới',
    },
    ChangeUsername_PleaseInputNewUsername: {
      key: 'ChangeUsername_PleaseInputNewUsername',
      value: 'Vui lòng nhập tên đăng nhập mới!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập tên đăng nhập mới!',
    },
    ChangeUsername_ChangeUsername: {
      key: 'ChangeUsername_ChangeUsername',
      value: 'Thay đổi tên đăng nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thay đổi tên đăng nhập',
    },

    //#end CHANGE_USERNAME
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start DETAIL
    Detail_BiddingAnnoucement: {
      key: 'Detail_BiddingAnnoucement',
      value: 'THÔNG BÁO MỜI THẦU',
      languageType: 'VI',
      path: 'all_view',
      description: 'THÔNG BÁO MỜI THẦU',
    },
    Detail_BasicInformation: {
      key: 'Detail_BasicInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chung',
    },
    Detail_BiddingSide: { key: 'Detail_BiddingSide', value: 'Bên mời thầu', languageType: 'VI', path: 'all_view', description: 'Bên mời thầu' },
    Detail_BiddingSubmitAddress: {
      key: 'Detail_BiddingSubmitAddress',
      value: 'Địa chỉ nộp hồ sơ thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Địa chỉ nộp hồ sơ thầu',
    },
    Detail_BiddingPlace: {
      key: 'Detail_BiddingPlace',
      value: 'Các địa điểm thực hiện gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Các địa điểm thực hiện gói thầu',
    },
    Detail_BiddingPackageInformation: {
      key: 'Detail_BiddingPackageInformation',
      value: 'Thông tin gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin gói thầu',
    },
    Detail_BiddingPackageName: {
      key: 'Detail_BiddingPackageName',
      value: 'Tên gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên gói thầu',
    },
    Detail_BiddingPackageCode: { key: 'Detail_BiddingPackageCode', value: 'Mã', languageType: 'VI', path: 'all_view', description: 'Mã' },
    Detail_BiddingPackageStatus: {
      key: 'Detail_BiddingPackageStatus',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái',
    },
    Detail_BiddingPackageDescription: {
      key: 'Detail_BiddingPackageDescription',
      value: 'Mô tả nội dung mời thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mô tả nội dung mời thầu',
    },
    Detail_BiddingPackageMethod: {
      key: 'Detail_BiddingPackageMethod',
      value: 'Hình thức đấu thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hình thức đấu thầu',
    },
    Detail_BiddingPackagePreserveMethod: {
      key: 'Detail_BiddingPackagePreserveMethod',
      value: 'Hình thức bảo lãnh dự thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hình thức bảo lãnh dự thầu',
    },
    Detail_BiddingPackageMoneyForMethod: {
      key: 'Detail_BiddingPackageMoneyForMethod',
      value: 'Số tiền bảo lãnh dự thầu (VNĐ)',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số tiền bảo lãnh dự thầu (VNĐ)',
    },
    Detail_BiddingPackageDeadlineForMethod: {
      key: 'Detail_BiddingPackageDeadlineForMethod',
      value: 'Thời hạn bảo lãnh dự thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thời hạn bảo lãnh dự thầu',
    },
    Detail_BiddingPackageUploadDate: {
      key: 'Detail_BiddingPackageUploadDate',
      value: 'Ngày đăng tải gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày đăng tải gói thầu',
    },
    Detail_BiddingPackageDeadlineForConfirm: {
      key: 'Detail_BiddingPackageDeadlineForConfirm',
      value: 'Ngày hết hạn xác nhận tham gia',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày hết hạn xác nhận tham gia',
    },
    Detail_BiddingPackageDeadlineForSubmit: {
      key: 'Detail_BiddingPackageDeadlineForSubmit',
      value: 'Ngày hết hạn nộp hồ sơ thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày hết hạn nộp hồ sơ thầu',
    },
    Detail_BiddingPackageValidPeriod: {
      key: 'Detail_BiddingPackageValidPeriod',
      value: 'Hiệu lực hợp đồng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hiệu lực hợp đồng',
    },
    Detail_TechnicalSketchOrSampleImage: {
      key: 'Detail_TechnicalSketchOrSampleImage',
      value: 'Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ',
    },
    Detail_ScopeOfWork: {
      key: 'Detail_ScopeOfWork',
      value: 'Phạm vi công việc',
      languageType: 'VI',
      path: 'all_view',
      description: 'Phạm vi công việc',
    },
    Detail_KPI_Criteria: {
      key: 'Detail_KPI_Criteria',
      value: 'Tiêu chuẩn đánh giá KPI',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu chuẩn đánh giá KPI',
    },
    Detail_Regulations: {
      key: 'Detail_Regulations',
      value: 'Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,...',
      languageType: 'VI',
      path: 'all_view',
      description: 'Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,...',
    },
    Detail_SampleDocuments: {
      key: 'Detail_SampleDocuments',
      value: 'Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...)',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...)',
    },
    Detail_Other: { key: 'Detail_Other', value: 'Khác', languageType: 'VI', path: 'all_view', description: 'Khác' },
    Detail_ItemList: { key: 'Detail_ItemList', value: 'Danh sách Item', languageType: 'VI', path: 'all_view', description: 'Danh sách Item' },
    Detail_Index: { key: 'Detail_Index', value: 'STT', languageType: 'VI', path: 'all_view', description: 'STT' },
    Detail_BiddingNumberCreated: {
      key: 'Detail_BiddingNumberCreated',
      value: 'Số lượng tạo thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng tạo thầu',
    },
    Detail_Notice: { key: 'Detail_Notice', value: 'Lưu ý: ', languageType: 'VI', path: 'all_view', description: 'Lưu ý: ' },
    Detail_ClickToJoin: {
      key: 'Detail_ClickToJoin',
      value: 'Để tham gia gói thầu quý NCC vui lòng ấn vào link để đăng ký tham gia',
      languageType: 'VI',
      path: 'all_view',
      description: 'Để tham gia gói thầu quý NCC vui lòng ấn vào link để đăng ký tham gia',
    },
    Detail_AtHere: { key: 'Detail_AtHere', value: ' tại đây', languageType: 'VI', path: 'all_view', description: ' tại đây' },
    Detail_NotParticipate: {
      key: 'Detail_NotParticipate',
      value: 'Không tham gia',
      languageType: 'VI',
      path: 'all_view',
      description: 'Không tham gia',
    },
    Detail_MovingToSubmitBiddingDocument: {
      key: 'Detail_MovingToSubmitBiddingDocument',
      value: 'Di chuyển đến trang nộp hồ sơ thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Di chuyển đến trang nộp hồ sơ thầu',
    },
    Detail_DenyAccess: { key: 'Detail_DenyAccess', value: 'Từ chối truy cập', languageType: 'VI', path: 'all_view', description: 'Từ chối truy cập' },
    Detail_YouNotAllowToViewDetailPackage: {
      key: 'Detail_YouNotAllowToViewDetailPackage',
      value: 'Bạn không được xem chi tiết gói thầu, vui lòng đăng nhập trước!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn không được xem chi tiết gói thầu, vui lòng đăng nhập trước!',
    },
    Detail_Using: { key: 'Detail_Using', value: 'Đang phát hành', languageType: 'VI', path: 'all_view', description: 'Đang phát hành' },
    Detail_OpenBidding: { key: 'Detail_OpenBidding', value: 'Đã mở thầu', languageType: 'VI', path: 'all_view', description: 'Đã mở thầu' },
    Detail_CloseBidding: { key: 'Detail_CloseBidding', value: 'Đóng thầu', languageType: 'VI', path: 'all_view', description: 'Đóng thầu' },
    Detail_NoAccessGrantPackage: {
      key: 'Detail_NoAccessGrantPackage',
      value: 'Bạn không có quyền truy cập gói thầu này!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn không có quyền truy cập gói thầu này!',
    },
    Detail_CheckedParticipatedBidding: {
      key: 'Detail_CheckedParticipatedBidding',
      value: 'Đã xác nhận tham gia đấu thầu thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đã xác nhận tham gia đấu thầu thành công!',
    },
    Detail_CheckedNotParticipatedBidding: {
      key: 'Detail_CheckedNotParticipatedBidding',
      value: 'Đã xác nhận không tham gia đấu thầu thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đã xác nhận không tham gia đấu thầu thành công!',
    },

    //#end DETAIL
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start EVALUATION
    Evaluation_FailedAccess: {
      key: 'Evaluation_FailedAccess',
      value: 'Truy cập thất bại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Truy cập thất bại',
    },
    Evaluation_LegalInformation: {
      key: 'Evaluation_LegalInformation',
      value: 'Thông tin pháp lý',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin pháp lý',
    },
    Evaluation_Information: { key: 'Evaluation_Information', value: 'Thông tin', languageType: 'VI', path: 'all_view', description: 'Thông tin' },
    Evaluation_Subscribed: { key: 'Evaluation_Subscribed', value: 'Đã đăng ký', languageType: 'VI', path: 'all_view', description: 'Đã đăng ký' },
    Evaluation_EditRequest: {
      key: 'Evaluation_EditRequest',
      value: 'Yêu cầu điều chỉnh',
      languageType: 'VI',
      path: 'all_view',
      description: 'Yêu cầu điều chỉnh',
    },
    Evaluation_EnterpriseCode: {
      key: 'Evaluation_EnterpriseCode',
      value: 'Mã doanh nghiệp',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã doanh nghiệp',
    },
    Evaluation_OfficialName: {
      key: 'Evaluation_OfficialName',
      value: 'Tên chính thức',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên chính thức',
    },
    Evaluation_DealName: { key: 'Evaluation_DealName', value: 'Tên giao dịch', languageType: 'VI', path: 'all_view', description: 'Tên giao dịch' },
    Evaluation_OfficeAddress: {
      key: 'Evaluation_OfficeAddress',
      value: 'Địa chỉ trụ sở',
      languageType: 'VI',
      path: 'all_view',
      description: 'Địa chỉ trụ sở',
    },
    Evaluation_DealAddress: {
      key: 'Evaluation_DealAddress',
      value: 'Địa chỉ giao dịch',
      languageType: 'VI',
      path: 'all_view',
      description: 'Địa chỉ giao dịch',
    },
    Evaluation_License: { key: 'Evaluation_License', value: 'Giấy phép ĐKKD', languageType: 'VI', path: 'all_view', description: 'Giấy phép ĐKKD' },
    Evaluation_Represent: {
      key: 'Evaluation_Represent',
      value: 'Đại diện pháp luật',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đại diện pháp luật',
    },
    Evaluation_CEO_Name: { key: 'Evaluation_CEO_Name', value: 'Tên giám đốc', languageType: 'VI', path: 'all_view', description: 'Tên giám đốc' },
    Evaluation_BankNumber: { key: 'Evaluation_BankNumber', value: 'Số tài khoản', languageType: 'VI', path: 'all_view', description: 'Số tài khoản' },
    Evaluation_Bank: { key: 'Evaluation_Bank', value: 'Ngân hàng', languageType: 'VI', path: 'all_view', description: 'Ngân hàng' },
    Evaluation_Branch: { key: 'Evaluation_Branch', value: 'Chi nhánh', languageType: 'VI', path: 'all_view', description: 'Chi nhánh' },
    Evaluation_OpenAccountNotification: {
      key: 'Evaluation_OpenAccountNotification',
      value: 'Thông báo mở TK',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông báo mở TK',
    },
    Evaluation_ContactName: {
      key: 'Evaluation_ContactName',
      value: 'Người liên hệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Người liên hệ',
    },
    Evaluation_Phone: { key: 'Evaluation_Phone', value: 'Điện thoại', languageType: 'VI', path: 'all_view', description: 'Điện thoại' },
    Evaluation_EstablishYear: {
      key: 'Evaluation_EstablishYear',
      value: 'Năm thành lập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Năm thành lập',
    },
    Evaluation_Capital: { key: 'Evaluation_Capital', value: 'Vốn điều lệ', languageType: 'VI', path: 'all_view', description: 'Vốn điều lệ' },
    Evaluation_Asset: { key: 'Evaluation_Asset', value: 'Tài sản cố định', languageType: 'VI', path: 'all_view', description: 'Tài sản cố định' },
    Evaluation_FileBill: {
      key: 'Evaluation_FileBill',
      value: 'HĐ mẫu/phiếu thu/biên lai',
      languageType: 'VI',
      path: 'all_view',
      description: 'HĐ mẫu/phiếu thu/biên lai',
    },
    Evaluation_BillInfo: {
      key: 'Evaluation_BillInfo',
      value: 'Thông tin phát hành HĐ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin phát hành HĐ',
    },
    Evaluation_AccessInformation: {
      key: 'Evaluation_AccessInformation',
      value: 'Thông tin năng lực',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin năng lực',
    },
    Evaluation_Criteria: { key: 'Evaluation_Criteria', value: 'Tiêu chí', languageType: 'VI', path: 'all_view', description: 'Tiêu chí' },
    Evaluation_Subcribed: { key: 'Evaluation_Subcribed', value: 'Đã đăng ký', languageType: 'VI', path: 'all_view', description: 'Đã đăng ký' },
    Evaluation_Note: { key: 'Evaluation_Note', value: 'Ghi chú', languageType: 'VI', path: 'all_view', description: 'Ghi chú' },
    Evaluation_AccessInformationNote: {
      key: 'Evaluation_AccessInformationNote',
      value: 'Ghi chú thông tin năng lực:',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ghi chú thông tin năng lực:',
    },
    Evaluation_Detail: { key: 'Evaluation_Detail', value: 'Chi tiết', languageType: 'VI', path: 'all_view', description: 'Chi tiết' },
    Evaluation_Year: { key: 'Evaluation_Year', value: 'Năm', languageType: 'VI', path: 'all_view', description: 'Năm' },
    Evaluation_ViewAttachedFile: {
      key: 'Evaluation_ViewAttachedFile',
      value: 'Xem file đính kèm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xem file đính kèm',
    },
    Evaluation_AgreeToUpdateInformation: {
      key: 'Evaluation_AgreeToUpdateInformation',
      value: 'Đồng ý cập nhật lại thông tin theo nội dung thẩm định',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đồng ý cập nhật lại thông tin theo nội dung thẩm định',
    },
    Evaluation_NoAccessGrant: {
      key: 'Evaluation_NoAccessGrant',
      value: 'Bạn không có quyền truy cập!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bạn không có quyền truy cập!',
    },
    Evaluation_NotDetermineAppraisal: {
      key: 'Evaluation_NotDetermineAppraisal',
      value: 'Không xác định lần thẩm định!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Không xác định lần thẩm định!',
    },
    Evaluation_UpdateDataSuccessfully: {
      key: 'Evaluation_UpdateDataSuccessfully',
      value: 'Đã cập nhật dữ liệu thành công!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đã cập nhật dữ liệu thành công!',
    },

    //#end EVALUATION
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start FORGOT_PASSWORD
    ForgetPassword_EmailNotValid: {
      key: 'ForgetPassword_EmailNotValid',
      value: 'Email không hợp lệ!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Email không hợp lệ!',
    },
    ForgetPassword_SendConfirmCode: {
      key: 'ForgetPassword_SendConfirmCode',
      value: 'Gửi mã xác nhận tới Email',
      languageType: 'VI',
      path: 'all_view',
      description: 'Gửi mã xác nhận tới Email',
    },
    ForgetPassword_ConfirmCode: {
      key: 'ForgetPassword_ConfirmCode',
      value: 'Mã xác nhận',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã xác nhận',
    },
    ForgetPassword_PleaseInputConfirmCode: {
      key: 'ForgetPassword_PleaseInputConfirmCode',
      value: 'Vui lòng nhập mã xác nhận!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập mã xác nhận!',
    },
    ForgetPassword_NewPassword: {
      key: 'ForgetPassword_NewPassword',
      value: 'Mật khẩu mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu mới',
    },
    ForgetPassword_PleaseInputNewPassword: {
      key: 'ForgetPassword_PleaseInputNewPassword',
      value: 'Vui lòng nhập mật khẩu mới!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập mật khẩu mới!',
    },
    ForgetPassword_InputNewPassword: {
      key: 'ForgetPassword_InputNewPassword',
      value: 'Nhập lại mật khẩu mới',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập lại mật khẩu mới',
    },
    ForgetPassword_PleaseReInputNewPassword: {
      key: 'ForgetPassword_PleaseReInputNewPassword',
      value: 'Vui lòng nhập lại mật khẩu mới!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập lại mật khẩu mới!',
    },
    ForgetPassword_PasswordUnmatched: {
      key: 'ForgetPassword_PasswordUnmatched',
      value: 'Mật khẩu không khớp!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu không khớp!',
    },
    ForgetPassword_ChangePassword: {
      key: 'ForgetPassword_ChangePassword',
      value: 'Đổi mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đổi mật khẩu',
    },

    //#end FORGOT_PASSWORD
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start HOME
    Home_SuggestingBidNotification: {
      key: 'Home_SuggestingBidNotification',
      value: 'Thông báo mời thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông báo mời thầu',
    },
    Home_Month: { key: 'Home_Month', value: 'Tháng', languageType: 'VI', path: 'all_view', description: 'Tháng' },
    Home_Code: { key: 'Home_Code', value: 'Mã', languageType: 'VI', path: 'all_view', description: 'Mã' },
    Home_ReleasePeriod: {
      key: 'Home_ReleasePeriod',
      value: 'Thời gian phát hành',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thời gian phát hành',
    },
    Home_SuggestedBidSide: { key: 'Home_SuggestedBidSide', value: 'Bên mời thầu', languageType: 'VI', path: 'all_view', description: 'Bên mời thầu' },
    Home_OpeningBid: { key: 'Home_OpeningBid', value: 'Đang phát hành', languageType: 'VI', path: 'all_view', description: 'Đang phát hành' },
    Home_OpenedBid: { key: 'Home_OpenedBid', value: 'Đã mở thầu', languageType: 'VI', path: 'all_view', description: 'Đã mở thầu' },
    Home_ClosedBid: { key: 'Home_ClosedBid', value: 'Đóng thầu', languageType: 'VI', path: 'all_view', description: 'Đóng thầu' },
    Home_BiddingPackage: { key: 'Home_BiddingPackage', value: 'Gói thầu', languageType: 'VI', path: 'all_view', description: 'Gói thầu' },

    //#end HOME
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start FAQ
    FAQ_Title: {
      key: 'FAQ_Title',
      value: 'Tiêu đề FAQ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu đề FAQ',
    },
    FAQ_Content: {
      key: 'FAQ_Content',
      value: 'Nội dung FAQ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nội dung FAQ',
    },
    //#end FAQ
    /////////////////////////////////////////
    /////////////////////////////////////////
    //#start SIGN_IN
    SignIn_SignIn: {
      key: 'SignIn_SignIn',
      value: 'Đăng nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng nhập',
    },
    SignIn_SignUp: {
      key: 'SignIn_SignUp',
      value: 'Đăng ký ngay!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng ký ngay!',
    },
    SignIn_ForgetPassword: {
      key: 'SignIn_ForgetPassword',
      value: 'Quên mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Quên mật khẩu',
    },
    //#end SIGN_IN

    //#endregion

    //#region Modal_AddBididingPriceModal
    Modal_AddBiddingPriceModal_NameCategory: {
      key: 'Modal_AddBiddingPriceModal_NameCategory',
      value: 'Tên hạng mục',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên hạng mục',
    },
    Modal_AddBiddingPriceModal_PleaseEnterCharacters: {
      key: 'Modal_AddBiddingPriceModal_PleaseEnterCharacters',
      value: 'Vui lòng nhập tên hạng mục cơ cấu giá (3-250 kí tự)!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập tên hạng mục cơ cấu giá (3-250 kí tự)!',
    },
    Modal_AddBiddingPriceModal_EnterCharacters: {
      key: 'Modal_AddBiddingPriceModal_EnterCharacters',
      value: 'Nhập 3-250 kí tự',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập 3-250 kí tự',
    },
    Modal_AddBiddingPriceModal_Unit: {
      key: 'Modal_AddBiddingPriceModal_Unit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tính',
    },
    Modal_AddBiddingPriceModal_PleaseEnterUnit: {
      key: 'Modal_AddBiddingPriceModal_PleaseEnterUnit',
      value: 'Vui lòng chọn đơn vị tính!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng chọn đơn vị tính!',
    },
    Modal_AddBiddingPriceModal_EnterUnit: {
      key: 'Modal_AddBiddingPriceModal_EnterUnit',
      value: 'Chọn đơn vị tính!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chọn đơn vị tính!',
    },
    Modal_AddBiddingPriceModal_CurrencyUnit: {
      key: 'Modal_AddBiddingPriceModal_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    Modal_AddBiddingPriceModal_PleaseEnterCurrencyUnit: {
      key: 'Modal_AddBiddingPriceModal_PleaseEnterCurrencyUnit',
      value: 'Vui lòng chọn đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng chọn đơn vị tiền tệ',
    },
    Modal_AddBiddingPriceModal_ChooseCurrencyUnit: {
      key: 'Modal_AddBiddingPriceModal_ChooseCurrencyUnit',
      value: 'Chọn đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chọn đơn vị tiền tệ',
    },
    Modal_AddBiddingPriceModal_Quantity: {
      key: 'AddBiddingPriceModal_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng',
    },
    Modal_AddBiddingPriceModal_PleaseNotEnterNumberLessThanZero: {
      key: 'AddBiddingPriceModal_PleaseNotEnterNumberLessThanZero',
      value: 'Vui lòng không nhập số nhỏ hơn 0!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng không nhập số nhỏ hơn 0!',
    },
    Modal_AddBiddingPriceModal_EnterQuantity: {
      key: 'Modal_AddBiddingPriceModal_EnterQuantity',
      value: 'Nhập số lượng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nhập số lượng',
    },
    Modal_AddBiddingPriceModal_Save: {
      key: 'Modal_AddBiddingPriceModal_Save',
      value: 'Lưu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lưu',
    },
    Modal_AddBiddingPriceModal_EditStructureCategories: {
      key: 'Modal_AddBiddingPriceModal_EditStructureCategories',
      value: 'Chỉnh sửa hạng mục cơ cấu giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chỉnh sửa hạng mục cơ cấu giá',
    },
    Modal_AddBiddingPriceModal_AddStructureCategories: {
      key: 'Modal_AddBiddingPriceModal_AddStructureCategories',
      value: 'Thêm mới hạng mục cơ cấu giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thêm mới hạng mục cơ cấu giá',
    },
    //#endregion

    //#region Modal_BiddingPricePriceListDetailModal
    Modal_BiddingPricePriceListDetailModal_InformationFieldName: {
      key: 'Modal_BiddingPricePriceListDetailModal_InformationFieldName',
      value: 'Tên trường thông tin',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên trường thông tin',
    },
    Modal_BiddingPricePriceListDetailModal_DataType: {
      key: 'Modal_BiddingPricePriceListDetailModal_DataType',
      value: 'Kiểu dữ liệu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Kiểu dữ liệu',
    },
    Modal_BiddingPricePriceListDetailModal_Value: {
      key: 'Modal_BiddingPricePriceListDetailModal_Value',
      value: 'Giá trị',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá trị',
    },
    Modal_BiddingPricePriceListDetailModal_Details: {
      key: 'Modal_BiddingPricePriceListDetailModal_Details',
      value: 'Thông tin chi tiết -',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chi tiết -',
    },
    //#endregion

    //#region PageFooter
    PageFooter_TUTORIAL: {
      key: 'PageFooter_TUTORIAL',
      value: 'Hướng dẫn',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hướng dẫn',
    },
    PageFooter_FAQ: {
      key: 'PageFooter_FAQ',
      value: 'FAQ',
      languageType: 'VI',
      path: 'all_view',
      description: 'FAQ',
    },
    PageFooter_UserManual: {
      key: 'PageFooter_UserManual',
      value: 'HƯỚNG DẪN SỬ DỤNG',
      languageType: 'VI',
      path: 'all_view',
      description: 'HƯỚNG DẪN SỬ DỤNG',
    },
    PageFooter_LINK: {
      key: 'PageFooter_LINK',
      value: 'Liên kết',
      languageType: 'VI',
      path: 'all_view',
      description: 'Liên kết',
    },
    PageFooter_ChooseWebsite: {
      key: 'PageFooter_ChooseWebsite',
      value: '- Chọn Website -',
      languageType: 'VI',
      path: 'all_view',
      description: '- Chọn Website -',
    },
    //#endregion

    //#region Page-Header
    PageHeader_HomePage: {
      key: 'PageHeader_HomePage',
      value: 'TRANG CHỦ',
      languageType: 'VI',
      path: 'all_view',
      description: 'TRANG CHỦ',
    },
    PageHeader_TenderNotice: {
      key: 'PageHeader_TenderNotice',
      value: 'THÔNG BÁO MỜI THẦU',
      languageType: 'VI',
      path: 'all_view',
      description: 'THÔNG BÁO MỜI THẦU',
    },
    PageHeader_BiddingHistory: {
      key: 'PageHeader_BiddingHistory',
      value: 'LỊCH SỬ ĐẤU THẦU',
      languageType: 'VI',
      path: 'all_view',
      description: 'LỊCH SỬ ĐẤU THẦU',
    },
    PageHeader_Complain: {
      key: 'PageHeader_Complain',
      value: 'KHIẾU NẠI',
      languageType: 'VI',
      path: 'all_view',
      description: 'KHIẾU NẠI',
    },
    PageHeader_ManagePO: {
      key: 'PageHeader_ManagePO',
      value: 'QUẢN LÝ PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'QUẢN LÝ PO',
    },
    PageHeader_Login: {
      key: 'PageHeader_Login',
      value: 'ĐĂNG NHẬP',
      languageType: 'VI',
      path: 'all_view',
      description: 'ĐĂNG NHẬP',
    },
    PageHeader_FAQ: {
      key: 'PageHeader_FAQ',
      value: 'FAQ',
      languageType: 'VI',
      path: 'faq',
      description: 'FAQ',
    },
    PageHeader_UserManual: {
      key: 'PageHeader_UserManual',
      value: 'HƯỚNG DẪN SỬ DỤNG',
      languageType: 'VI',
      path: 'all_view',
      description: 'HƯỚNG DẪN SỬ DỤNG',
    },
    PageHeader_Notice: {
      key: 'PageHeader_Notice',
      value: 'THÔNG BÁO',
      languageType: 'VI',
      path: 'all_view',
      description: 'THÔNG BÁO',
    },
    PageHeader_PleaseEnterYourUsername: {
      key: 'PageHeader_PleaseEnterYourUsername',
      value: 'Vui lòng nhập tên đăng nhập!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vui lòng nhập tên đăng nhập!',
    },
    PageHeader_Username: {
      key: 'PageHeader_Username',
      value: 'Tài khoản đăng nhập!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tài khoản đăng nhập!',
    },
    PageHeader_Password: {
      key: 'PageHeader_Password',
      value: 'Mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mật khẩu',
    },
    PageHeader_RememberMe: {
      key: 'PageHeader_RememberMe',
      value: 'Ghi nhớ tôi',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ghi nhớ tôi',
    },
    PageHeader_ForgotPassword: {
      key: 'PageHeader_ForgotPassword',
      value: 'Quên mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Quên mật khẩu',
    },
    PageHeader_SignUpNow: {
      key: 'PageHeader_SignUpNow',
      value: 'Đăng ký ngay!',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng ký ngay khi user chưa có tài khoản',
    },
    PageHeader_AccountInformation: {
      key: 'PageHeader_AccountInformation',
      value: 'Thông tin tài khoản',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin tài khoản user',
    },
    PageHeader_ChangeLoginName: {
      key: 'PageHeader_ChangeLoginName',
      value: 'Thay đổi tên đăng nhập',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thay đổi tên đăng nhập',
    },
    PageHeader_ChangePassword: {
      key: 'PageHeader_ChangePassword',
      value: 'Đổi mật khẩu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đổi mật khẩu',
    },
    PageHeader_LogOut: {
      key: 'PageHeader_LogOut',
      value: 'Đăng xuất',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đăng xuất',
    },
    PageHeader_SeeMore: {
      key: 'PageHeader_SeeMore',
      value: 'Xem thêm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xem thêm thông tin',
    },
    PageHeader_NonNotify: {
      key: 'PageHeader_NonNotify',
      value: 'Chưa có thông báo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Chưa có thông báo',
    },
    PageHeader_NotAccess: {
      key: 'PageHeader_NotAccess',
      value: 'Không có quyền truy cập.',
      languageType: 'VI',
      path: 'all_view',
      description: 'Không có quyền truy cập.',
    },
    PageHeader_ManageBill: {
      key: 'PageHeader_ManageBill',
      value: 'QUẢN LÝ HÓA ĐƠN',
      languageType: 'VI',
      path: 'all_view',
      description: 'QUẢN LÝ HÓA ĐƠN',
    },
    //#end region

    //#region PageNotFound
    PageNotFound_SorryPageNotFound: {
      key: 'PageNotFound_SorryPageNotFound',
      value: 'Xin lỗi, trang bạn đang tìm kiếm không tồn tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Xin lỗi, trang bạn đang tìm kiếm không tồn tại',
    },
    PageNotFound_GoToHomePage: {
      key: 'PageNotFound_GoToHomePage',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đi tới trang chủ',
    },
    //#endregion

    //#region PageSideLeft
    PageSideLeft_BiddingField: {
      key: 'PageSideLeft_BiddingField',
      value: 'Lĩnh vực đấu thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lĩnh vực đấu thầu',
    },
    //#endregion

    //#region PageUnauthorized
    PageUnauthorized_SorryPleaseLogin: {
      key: 'PageUnauthorized_SorryPleaseLogin',
      value: 'Xin lỗi, bạn cần phải đăng nhập để truy cập vào trang này',
      languageType: 'VI',
      path: 'page-unauthorized',
      description: 'Xin lỗi, bạn cần phải đăng nhập để truy cập vào trang này',
    },
    PageUnauthorized_GoToHomePage: {
      key: 'PageUnauthorized_GoToHomePage',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'page-unauthorized',
      description: 'Đi tới trang chủ',
    },
    //#endregion

    //#region PurchaseOrder_PurchaseOrderDetail_
    PurchaseOrder_PurchaseOrderDetail_GeneralInformation: {
      key: 'PurchaseOrder_PurchaseOrderDetail_GeneralInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thông tin chung',
    },
    PurchaseOrder_PurchaseOrderDetail_CodePO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_CodePO',
      value: 'Mã PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã PO',
    },
    PurchaseOrder_PurchaseOrderDetail_TitlePO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_TitlePO',
      value: 'Tiêu đề PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiêu đề PO',
    },
    PurchaseOrder_PurchaseOrderDetail_ContractCode: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ContractCode',
      value: 'Mã Hợp đồng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã Hợp đồng',
    },
    // Add additional language keys following the same pattern as above
    PurchaseOrder_PurchaseOrderDetail_HistoryChanges: {
      key: 'PurchaseOrder_PurchaseOrderDetail_HistoryChanges',
      value: 'Lịch sử thay đổi',
      languageType: 'VI',
      path: 'all_view',
      description: 'Lịch sử thay đổi',
    },
    PurchaseOrder_PurchaseOrderDetail_ContractName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ContractName',
      value: 'Tên Hợp đồng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên Hợp đồng',
    },
    PurchaseOrder_PurchaseOrderDetail_BiddingPackageCode: {
      key: 'PurchaseOrder_PurchaseOrderDetail_BiddingPackageCode',
      value: 'Mã gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mã gói thầu',
    },
    PurchaseOrder_PurchaseOrderDetail_BiddingPackageName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_BiddingPackageName',
      value: 'Tên gói thầu',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên gói thầu',
    },
    PurchaseOrder_PurchaseOrderDetail_DateCreated: {
      key: 'PurchaseOrder_PurchaseOrderDetail_DateCreated',
      value: 'Ngày tạo',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày tạo',
    },
    PurchaseOrder_PurchaseOrderDetail_DeliveryDate: {
      key: 'PurchaseOrder_PurchaseOrderDetail_DeliveryDate',
      value: 'Ngày giao hàng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ngày giao hàng',
    },
    PurchaseOrder_PurchaseOrderDetail_ValuePO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ValuePO',
      value: 'Giá trị PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá trị PO',
    },
    PurchaseOrder_PurchaseOrderDetail_Status: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Status',
      value: 'Trạng thái',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái',
    },
    PurchaseOrder_PurchaseOrderDetail_FormPO: {
      key: 'PurchaseOrder_PurchaseOrderDetail_FormPO',
      value: 'Hình thức PO',
      languageType: 'VI',
      path: 'all_view',
      description: 'Hình thức PO',
    },
    PurchaseOrder_PurchaseOrderDetail_CurrencyUnit: {
      key: 'PurchaseOrder_PurchaseOrderDetail_CurrencyUnit',
      value: 'Đơn vị tiền tệ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tiền tệ',
    },
    PurchaseOrder_PurchaseOrderDetail_Email: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Email',
      value: 'Email',
      languageType: 'VI',
      path: 'all_view',
      description: 'Email',
    },
    PurchaseOrder_PurchaseOrderDetail_Buyer: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Buyer',
      value: 'Bên mua hàng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Bên mua hàng',
    },
    PurchaseOrder_PurchaseOrderDetail_PhoneNumber: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PhoneNumber',
      value: 'Số điện thoại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số điện thoại',
    },
    PurchaseOrder_PurchaseOrderDetail_Time: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Time',
      value: 'Thời gian',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thời gian',
    },
    PurchaseOrder_PurchaseOrderDetail_Implementer: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Implementer',
      value: 'Người thực hiện',
      languageType: 'VI',
      path: 'all_view',
      description: 'Người thực hiện',
    },
    PurchaseOrder_PurchaseOrderDetail_OldState: {
      key: 'PurchaseOrder_PurchaseOrderDetail_OldState',
      value: 'Trạng thái cũ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái cũ',
    },
    PurchaseOrder_PurchaseOrderDetail_CurrentState: {
      key: 'PurchaseOrder_PurchaseOrderDetail_CurrentState',
      value: 'Trạng thái hiện tại',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái hiện tại',
    },
    PurchaseOrder_PurchaseOrderDetail_ContentChanges: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ContentChanges',
      value: 'Nội dung thay đổi',
      languageType: 'VI',
      path: 'all_view',
      description: 'Nội dung thay đổi',
    },
    PurchaseOrder_PurchaseOrderDetail_ListProducts: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ListProducts',
      value: 'Danh sách sản phẩm',
      languageType: 'VI',
      path: 'all_view',
      description: 'Danh sách sản phẩm',
    },
    PurchaseOrder_PurchaseOrderDetail_Supplies: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Supplies',
      value: 'Vật tư',
      languageType: 'VI',
      path: 'all_view',
      description: 'Vật tư',
    },
    PurchaseOrder_PurchaseOrderDetail_GoodsName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_GoodsName',
      value: 'Tên hàng hóa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên hàng hóa',
    },
    PurchaseOrder_PurchaseOrderDetail_Unit: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Unit',
      value: 'Đơn vị tính',
      languageType: 'VI',
      path: 'all_view',
      description: 'Đơn vị tính',
    },
    PurchaseOrder_PurchaseOrderDetail_Quantity: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Quantity',
      value: 'Số lượng',
      languageType: 'VI',
      path: 'all_view',
      description: 'Số lượng',
    },
    PurchaseOrder_PurchaseOrderDetail_Price: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Price',
      value: 'Giá',
      languageType: 'VI',
      path: 'all_view',
      description: 'Giá',
    },
    PurchaseOrder_PurchaseOrderDetail_IntoMoney: {
      key: 'PurchaseOrder_PurchaseOrderDetail_IntoMoney',
      value: 'Thành tiền',
      languageType: 'VI',
      path: 'all_view',
      description: 'Thành tiền',
    },
    PurchaseOrder_PurchaseOrderDetail_DescriptionGoods: {
      key: 'PurchaseOrder_PurchaseOrderDetail_DescriptionGoods',
      value: 'Mô tả hàng hóa',
      languageType: 'VI',
      path: 'all_view',
      description: 'Mô tả hàng hóa',
    },
    PurchaseOrder_PurchaseOrderDetail_Note: {
      key: 'PurchaseOrder_PurchaseOrderDetail_Note',
      value: 'Ghi chú',
      languageType: 'VI',
      path: 'all_view',
      description: 'Ghi chú',
    },
    PurchaseOrder_PurchaseOrderDetail_PaymentProgress: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PaymentProgress',
      value: 'Tiến độ thanh toán',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tiến độ thanh toán',
    },
    PurchaseOrder_PurchaseOrderDetail_ProgressName: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ProgressName',
      value: 'Tên tiến độ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Tên tiến độ',
    },
    PurchaseOrder_PurchaseOrderDetail_ProgressPercent: {
      key: 'PurchaseOrder_PurchaseOrderDetail_ProgressPercent',
      value: 'Phần trăm tiến độ',
      languageType: 'VI',
      path: 'all_view',
      description: 'Phần trăm tiến độ',
    },
    PurchaseOrder_PurchaseOrderDetail_PaymentStatus: {
      key: 'PurchaseOrder_PurchaseOrderDetail_PaymentStatus',
      value: 'Trạng thái thanh toán',
      languageType: 'VI',
      path: 'all_view',
      description: 'Trạng thái thanh toán',
    },
    //#endregion

    //#region PurchaseOrder
    PurchaseOrder_POList: {
      key: 'PurchaseOrder_POList',
      value: 'DANH SÁCH PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'DANH SÁCH PO',
    },
    PurchaseOrder_POSearchCode: {
      key: 'PurchaseOrder_POSearchCode',
      value: 'Tìm theo mã PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tìm theo mã PO',
    },
    PurchaseOrder_POSearchName: {
      key: 'PurchaseOrder_POSearchName',
      value: 'Tìm theo tên PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tìm theo tên PO',
    },
    PurchaseOrder_ChooseStatus: {
      key: 'PurchaseOrder_ChooseStatus',
      value: 'Chọn trạng thái',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Chọn trạng thái',
    },
    PurchaseOrder_Search: {
      key: 'PurchaseOrder_Search',
      value: 'Tìm kiếm',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tìm kiếm',
    },
    PurchaseOrder_POCode: {
      key: 'PurchaseOrder_POCode',
      value: 'Mã PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Mã PO',
    },
    PurchaseOrder_POName: {
      key: 'PurchaseOrder_POName',
      value: 'Tên PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tên PO',
    },
    PurchaseOrder_ContractCode: {
      key: 'PurchaseOrder_ContractCode',
      value: 'Mã Hợp đồng',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Mã Hợp đồng',
    },
    PurchaseOrder_BiddingPackageName: {
      key: 'PurchaseOrder_BiddingPackageName',
      value: 'Tên Gói Thầu',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tên Gói Thầu',
    },
    PurchaseOrder_CreatedDate: {
      key: 'PurchaseOrder_CreatedDate',
      value: 'Ngày tạo',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Ngày tạo',
    },
    PurchaseOrder_POValue: {
      key: 'PurchaseOrder_POValue',
      value: 'Giá trị PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Giá trị PO',
    },
    PurchaseOrder_DateDelivery: {
      key: 'PurchaseOrder_DateDelivery',
      value: 'Ngày giao hàng',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Ngày giao hàng',
    },
    PurchaseOrder_POStatus: {
      key: 'PurchaseOrder_POStatus',
      value: 'Trạng thái PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Trạng thái PO',
    },
    PurchaseOrder_Action: {
      key: 'PurchaseOrder_Action',
      value: 'Tác vụ',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Tác vụ',
    },
    PurchaseOrder_ViewDetail: {
      key: 'PurchaseOrder_ViewDetail',
      value: 'Xem chi tiết',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Xem chi tiết',
    },
    PurchaseOrder_POConfirm: {
      key: 'PurchaseOrder_POConfirm',
      value: 'Xác nhận PO',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Xác nhận PO',
    },
    PurchaseOrder_SurePOConfirm: {
      key: 'PurchaseOrder_SurePOConfirm',
      value: 'Bạn có chắc muốn Xác nhận PO ?',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Bạn có chắc muốn Xác nhận PO ?',
    },
    PurchaseOrder_RejectShip: {
      key: 'PurchaseOrder_RejectShip',
      value: 'Từ Chối Giao Hàng',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Từ Chối Giao Hàng',
    },
    PurchaseOrder_ConfirmShip: {
      key: 'PurchaseOrder_ConfirmShip',
      value: 'Xác nhận giao hàng',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Xác nhận giao hàng',
    },
    PurchaseOrder_SureConfirmShip: {
      key: 'PurchaseOrder_SureConfirmShip',
      value: 'Bạn có chắc muốn Xác nhận giao hàng ?',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Bạn có chắc muốn Xác nhận giao hàng ?',
    },
    PurchaseOrder_EnterReasonReject: {
      key: 'PurchaseOrder_EnterReasonReject',
      value: 'Nhập lý do từ chối',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nhập lý do từ chối',
    },
    PurchaseOrder_Reason: {
      key: 'PurchaseOrder_Reason',
      value: 'Lý do',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Lý do',
    },
    PurchaseOrder_EnterName: {
      key: 'PurchaseOrder_EnterName',
      value: 'Vui lòng nhập tên (1-250 kí tự)!',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Vui lòng nhập tên (1-250 kí tự)!',
    },
    PurchaseOrder_EnterReason: {
      key: 'PurchaseOrder_EnterReason',
      value: 'Nhập lý do',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Nhập lý do',
    },
    PurchaseOrder_Cancel: {
      key: 'PurchaseOrder_Cancel',
      value: 'Hủy',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Hủy',
    },
    PurchaseOrder_Close: {
      key: 'PurchaseOrder_Close',
      value: 'Đóng',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Đóng',
    },
    PurchaseOrder_PleaseEnterReason: {
      key: 'PurchaseOrder_PleaseEnterReason',
      value: 'Vui lòng nhập lý do',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Vui lòng nhập lý do',
    },
    PurchaseOrder_RejectShipSuccess: {
      key: 'PurchaseOrder_RejectShipSuccess',
      value: 'Gửi xác nhận từ chối giao hàng thành công.',
      languageType: 'VI',
      path: 'purchase-order',
      description: 'Gửi xác nhận từ chối giao hàng thành công.',
    },
    //#endregion

    //#region SupplierAdditionalCapacity
    SupplierAdditionalCapacity_SuppliersAddCapacity: {
      key: 'SupplierAdditionalCapacity_SuppliersAddCapacity',
      value: 'Nhà cung cấp bổ sung năng lực:',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Nhà cung cấp bổ sung năng lực:',
    },
    SupplierAdditionalCapacity_DownloadTemplate: {
      key: 'SupplierAdditionalCapacity_DownloadTemplate',
      value: 'Tải template',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Tải template',
    },
    SupplierAdditionalCapacity_UploadExcel: {
      key: 'SupplierAdditionalCapacity_UploadExcel',
      value: 'Upload Excel',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'UploadExcel',
    },
    SupplierAdditionalCapacity_Delete: {
      key: 'SupplierAdditionalCapacity_Delete',
      value: 'Xóa',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Xóa',
    },
    SupplierAdditionalCapacity_InvalidEmail: {
      key: 'SupplierAdditionalCapacity_InvalidEmail',
      value: 'E-mail không hợp lệ!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'E-mail không hợp lệ!',
    },
    SupplierAdditionalCapacity_PleaseEnterPassword: {
      key: 'SupplierAdditionalCapacity_PleaseEnterPassword',
      value: 'Vui lòng nhập mật khẩu!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng nhập mật khẩu!',
    },
    SupplierAdditionalCapacity_PleaseEnterData: {
      key: 'SupplierAdditionalCapacity_PleaseEnterData',
      value: 'Vui lòng nhập dữ liệu!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng nhập dữ liệu!',
    },
    SupplierAdditionalCapacity_Year: {
      key: 'SupplierAdditionalCapacity_Year',
      value: 'Năm',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Năm',
    },
    SupplierAdditionalCapacity_Value: {
      key: 'SupplierAdditionalCapacity_Value',
      value: 'Giá trị',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Giá trị',
    },
    SupplierAdditionalCapacity_AddValue: {
      key: 'SupplierAdditionalCapacity_AddValue',
      value: 'Thêm giá trị',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Thêm giá trị',
    },
    SupplierAdditionalCapacity_InvalidDataPleaseFillInAllFields: {
      key: 'SupplierAdditionalCapacity_InvalidDataPleaseFillInAllFields',
      value: 'Dữ liệu không hợp lệ vui lòng điền đầy đủ thông tin các ô',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Dữ liệu không hợp lệ vui lòng điền đầy đủ thông tin các ô',
    },
    SupplierAdditionalCapacity_UpdateNewChangesEveryYear: {
      key: 'SupplierAdditionalCapacity_UpdateNewChangesEveryYear',
      value: 'Cập nhật thay đổi mới hàng năm(nếu có)',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Cập nhật thay đổi mới hàng năm(nếu có)',
    },
    SupplierAdditionalCapacity_PleaseChooseData: {
      key: 'SupplierAdditionalCapacity_PleaseChooseData',
      value: 'Vui lòng chọn dữ liệu!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng chọn dữ liệu!',
    },
    SupplierAdditionalCapacity_LinkPhoto: {
      key: 'SupplierAdditionalCapacity_LinkPhoto',
      value: 'Đường dẫn của ảnh',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Đường dẫn của ảnh',
    },
    SupplierAdditionalCapacity_InvalidData: {
      key: 'SupplierAdditionalCapacity_InvalidData',
      value: 'Dữ liệu không hợp lệ!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Dữ liệu không hợp lệ!',
    },
    SupplierAdditionalCapacity_Save: {
      key: 'SupplierAdditionalCapacity_Save',
      value: 'Lưu',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Lưu',
    },
    SupplierAdditionalCapacity_ChooseBusiness: {
      key: 'SupplierAdditionalCapacity_ChooseBusiness',
      value: 'Vui lòng chọn lĩnh vực kinh doanh.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng chọn lĩnh vực kinh doanh.',
    },
    SupplierAdditionalCapacity_ChooseBusinessArea: {
      key: 'SupplierAdditionalCapacity_ChooseBusinessArea',
      value: 'Vui lòng chọn lĩnh vực kinh doanh để thêm!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng chọn lĩnh vực kinh doanh để thêm!',
    },
    SupplierAdditionalCapacity_AddBusinessArea: {
      key: 'SupplierAdditionalCapacity_AddBusinessArea',
      value: 'Lĩnh vực kinh doanh đã được thêm, vui lòng kiểm tra lại!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Lĩnh vực kinh doanh đã được thêm, vui lòng kiểm tra lại!',
    },
    SupplierAdditionalCapacity_NotFoundBusinessArea: {
      key: 'SupplierAdditionalCapacity_NotFoundBusinessArea',
      value: 'Không tìm thấy lĩnh vực kinh doanh, vui lòng kiểm tra lại!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Không tìm thấy lĩnh vực kinh doanh, vui lòng kiểm tra lại!',
    },
    SupplierAdditionalCapacity_ProvidingCapacityInformation: {
      key: 'SupplierAdditionalCapacity_ProvidingCapacityInformation',
      value: 'Cung cấp thông tin năng lực:',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Cung cấp thông tin năng lực:',
    },
    SupplierAdditionalCapacity_ErrorOccurred: {
      key: 'SupplierAdditionalCapacity_ErrorOccurred',
      value: 'Đã có lỗi xảy ra vui lòng kiểm tra lại dữ liệu.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Đã có lỗi xảy ra vui lòng kiểm tra lại dữ liệu.',
    },
    SupplierAdditionalCapacity_AddCapacitySuccess: {
      key: 'SupplierAdditionalCapacity_AddCapacitySuccess',
      value: 'Đã bổ sung năng lực thành thành công',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Đã bổ sung năng lực thành thành công',
    },
    SupplierAdditionalCapacity_MaximumSizeToUpload: {
      key: 'SupplierAdditionalCapacity_MaximumSizeToUpload',
      value: 'Kích thước tối đa để upload là',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Kích thước tối đa để upload là',
    },
    SupplierAdditionalCapacity_ChooseAnotherFile: {
      key: 'SupplierAdditionalCapacity_ChooseAnotherFile',
      value: 'vui lòng chọn file khác',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'vui lòng chọn file khác',
    },
    SupplierAdditionalCapacity_DataEnterFirst: {
      key: 'SupplierAdditionalCapacity_DataEnterFirst',
      value: 'Vui lòng nhập dữ liệu trước',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng nhập dữ liệu trước',
    },
    SupplierAdditionalCapacity_DataEnterComplete: {
      key: 'SupplierAdditionalCapacity_DataEnterComplete',
      value: 'Vui lòng nhập đầy đủ dữ liệu ',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng nhập đầy đủ dữ liệu ',
    },
    SupplierAdditionalCapacity_NotDataCriteria: {
      key: 'SupplierAdditionalCapacity_NotDataCriteria',
      value: 'Chưa nhập dữ liệu cho tiêu chí: ',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Chưa nhập dữ liệu cho tiêu chí: ',
    },
    SupplierAdditionalCapacity_NotFullyDataCriteria: {
      key: 'SupplierAdditionalCapacity_NotFullyDataCriteria',
      value: 'Chưa nhập đầy đủ dữ liệu cho tiêu chí:',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Chưa nhập đầy đủ dữ liệu cho tiêu chí:',
    },
    SupplierAdditionalCapacity_ItemNoCriteria: {
      key: 'SupplierAdditionalCapacity_ItemNoCriteria',
      value: 'Item chưa có thiết lập tiêu chí năng lực.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Item chưa có thiết lập tiêu chí năng lực.',
    },
    SupplierAdditionalCapacity_ItemNoCriteriaUpExcel: {
      key: 'SupplierAdditionalCapacity_ItemNoCriteriaUpExcel',
      value: 'Item chưa có thiết lập tiêu chí năng lực có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Item chưa có thiết lập tiêu chí năng lực có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    SupplierAdditionalCapacity_Note: {
      key: 'SupplierAdditionalCapacity_Note',
      value: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    SupplierAdditionalCapacity_DownTemp: {
      key: 'SupplierAdditionalCapacity_DownTemp',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Tải template thành công!',
    },
    SupplierAdditionalCapacity_YearType: {
      key: 'SupplierAdditionalCapacity_YearType',
      value: '[Kiểu theo năm] Nhập như ví dụ:',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: '[Kiểu theo năm] Nhập như ví dụ:',
    },
    SupplierAdditionalCapacity_ChangeColumn: {
      key: 'SupplierAdditionalCapacity_ChangeColumn',
      value: 'File không đúng template (cột đã bị thay đổi)',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'File không đúng template (cột đã bị thay đổi)',
    },
    SupplierAdditionalCapacity_ChangeLine: {
      key: 'SupplierAdditionalCapacity_ChangeLine',
      value: 'File không đúng template (dòng đã bị thay đổi)!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'File không đúng template (dòng đã bị thay đổi)!',
    },
    SupplierAdditionalCapacity_ValueCriteria: {
      key: 'SupplierAdditionalCapacity_ValueCriteria',
      value: 'Giá trị của tiêu chí',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Giá trị của tiêu chí',
    },
    SupplierAdditionalCapacity_NotBlank: {
      key: 'SupplierAdditionalCapacity_NotBlank',
      value: 'bắt buộc không được để trống!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'bắt buộc không được để trống!',
    },
    SupplierAdditionalCapacity_ValueIs: {
      key: 'SupplierAdditionalCapacity_ValueIs',
      value: '[Giá trị] là',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: '[Giá trị] là',
    },
    SupplierAdditionalCapacity_NotType: {
      key: 'SupplierAdditionalCapacity_NotType',
      value: 'không phải kiểu',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'không phải kiểu',
    },
    SupplierAdditionalCapacity_NotList: {
      key: 'SupplierAdditionalCapacity_NotList',
      value: 'không nằm trong List',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'không nằm trong List',
    },
    SupplierAdditionalCapacity_NotValidYear: {
      key: 'SupplierAdditionalCapacity_NotValidYear',
      value: 'không hợp lệ với kiểu theo năm kiểu',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'không hợp lệ với kiểu theo năm kiểu',
    },
    SupplierAdditionalCapacity_YMD: {
      key: 'SupplierAdditionalCapacity_YMD',
      value: 'ngày phải có định dạng yyyy-mm-dd',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'ngày phải có định dạng yyyy-mm-dd',
    },
    //#endregion

    //#region SupplierInfor
    SupplierInfor_GeneralInformation: {
      key: 'SupplierInfor_GeneralInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Thông tin chung',
    },
    SupplierInfor_AccountInformation: {
      key: 'SupplierInfor_AccountInformation',
      value: 'Thông tin tài khoản',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Thông tin tài khoản',
    },
    SupplierInfor_CompanyName: {
      key: 'SupplierInfor_CompanyName',
      value: 'Tên doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tên doanh nghiệp',
    },
    SupplierInfor_TradingName: {
      key: 'SupplierInfor_TradingName',
      value: 'Tên giao dịch',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tên giao dịch',
    },
    SupplierInfor_CompanyCreateYear: {
      key: 'SupplierInfor_CompanyCreateYear',
      value: 'Năm thành lập công ty',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Năm thành lập công ty',
    },
    SupplierInfor_BusinessCode: {
      key: 'SupplierInfor_BusinessCode',
      value: 'Mã số doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Mã số doanh nghiệp',
    },
    SupplierInfor_OfficeAddress: {
      key: 'SupplierInfor_OfficeAddress',
      value: 'Địa chỉ trụ sở',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Địa chỉ trụ sở',
    },
    SupplierInfor_ChooseOfficeAddress: {
      key: 'SupplierInfor_ChooseOfficeAddress',
      value: 'Chọn địa chỉ trụ sở',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Chọn địa chỉ trụ sở',
    },
    SupplierInfor_LegalRepresentative: {
      key: 'SupplierInfor_LegalRepresentative',
      value: 'Người đại diện pháp luật',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Người đại diện pháp luật',
    },
    SupplierInfor_DirectorName: {
      key: 'SupplierInfor_DirectorName',
      value: 'Tên giám đốc',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tên giám đốc',
    },
    SupplierInfor_CharterCapital: {
      key: 'SupplierInfor_CharterCapital',
      value: 'Vốn điều lệ (tỷ đồng)',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Vốn điều lệ (tỷ đồng)',
    },
    SupplierInfor_FixedAssets: {
      key: 'SupplierInfor_FixedAssets',
      value: 'Tài sản cố định (tỷ đồng)',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tài sản cố định (tỷ đồng)',
    },
    SupplierInfor_SampleInvoiceReceiptFile: {
      key: 'SupplierInfor_SampleInvoiceReceiptFile',
      value: 'File hóa đơn mẫu/phiếu thu/biên lai',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'File hóa đơn mẫu/phiếu thu/biên lai',
    },
    SupplierInfor_InvoiceIssuanceInforFile: {
      key: 'SupplierInfor_InvoiceIssuanceInforFile',
      value: 'File thông tin phát hành hóa đơn',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'File thông tin phát hành hóa đơn',
    },
    SupplierInfor_BusinessLicenseTaxCode: {
      key: 'SupplierInfor_BusinessLicenseTaxCode',
      value: 'Giấy phép kinh doanh/Mã số thuế',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Giấy phép kinh doanh/Mã số thuế',
    },
    SupplierInfor_DescriptionBusiness: {
      key: 'SupplierInfor_DescriptionBusiness',
      value: 'Mô tả về doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Mô tả về doanh nghiệp',
    },
    SupplierInfor_BankName: {
      key: 'SupplierInfor_BankName',
      value: 'Tên ngân hàng',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tên ngân hàng',
    },
    SupplierInfor_BankNumber: {
      key: 'SupplierInfor_BankNumber',
      value: 'Số tài khoản ngân hàng',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Số tài khoản ngân hàng',
    },
    SupplierInfor_BankBranch: {
      key: 'SupplierInfor_BankBranch',
      value: 'Tên chi nhánh ngân hàng',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tên chi nhánh ngân hàng',
    },
    SupplierInfor_FileNoticeOpenAccount: {
      key: 'SupplierInfor_FileNoticeOpenAccount',
      value: 'File thông báo mở tài khoản/mẫu 08',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'File thông báo mở tài khoản/mẫu 08',
    },
    SupplierInfor_ContactName: {
      key: 'SupplierInfor_ContactName',
      value: 'Người liên hệ',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Người liên hệ',
    },
    SupplierInfor_PhoneNumber: {
      key: 'SupplierInfor_PhoneNumber',
      value: 'Số điện thoại',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Số điện thoại',
    },
    SupplierInfor_Email: {
      key: 'SupplierInfor_Email',
      value: 'Email',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Email',
    },
    SupplierInfor_Choose: {
      key: 'SupplierInfor_Choose',
      value: 'Chọn',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Chọn',
    },
    SupplierInfor_ChooseAddress: {
      key: 'SupplierInfor_ChooseAddress',
      value: 'Chọn địa chỉ',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Chọn địa chỉ',
    },
    SupplierInfor_Province: {
      key: 'SupplierInfor_Province',
      value: 'Tỉnh thành',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Tỉnh thành',
    },
    SupplierInfor_District: {
      key: 'SupplierInfor_District',
      value: 'Quận huyện',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Quận huyện',
    },
    SupplierInfor_Wards: {
      key: 'SupplierInfor_Wards',
      value: 'Phường xã',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Phường xã',
    },
    SupplierInfor_AdditionalInformationVillage: {
      key: 'SupplierInfor_AdditionalInformationVillage',
      value: 'Thông tin thêm (thôn, xóm, đường, số nhà...)',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Thông tin thêm (thôn, xóm, đường, số nhà...)',
    },
    SupplierInfor_EnterCompanyName: {
      key: 'SupplierInfor_EnterCompanyName',
      value: 'Vui lòng nhập tên doanh nghiệp (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering company name',
    },
    SupplierInfor_EnterTradingName: {
      key: 'SupplierInfor_EnterTradingName',
      value: 'Vui lòng nhập tên giao dịch (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering trading name',
    },
    SupplierInfor_EnterCompanyCreateYear: {
      key: 'SupplierInfor_EnterCompanyCreateYear',
      value: 'Vui lòng nhập năm thành lập công ty!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering company creation year',
    },
    SupplierInfor_EnterBusinessCode: {
      key: 'SupplierInfor_EnterBusinessCode',
      value: 'Vui lòng nhập mã số doanh nghiệp (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering business code',
    },
    SupplierInfor_EnterOfficeAddress: {
      key: 'SupplierInfor_EnterOfficeAddress',
      value: 'Vui lòng chọn địa chỉ trụ sở (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering office address',
    },
    SupplierInfor_EnterLegalRepresentative: {
      key: 'SupplierInfor_EnterLegalRepresentative',
      value: 'Vui lòng nhập người đại diện pháp luật (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering legal representative',
    },
    SupplierInfor_EnterDirectorName: {
      key: 'SupplierInfor_EnterDirectorName',
      value: 'Vui lòng nhập tên giám đốc (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering director name',
    },
    SupplierInfor_EnterCharterCapital: {
      key: 'SupplierInfor_EnterCharterCapital',
      value: 'Vui lòng nhập vốn điều lệ (tỷ đồng)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering charter capital',
    },
    SupplierInfor_EnterFixedAssets: {
      key: 'SupplierInfor_EnterFixedAssets',
      value: 'Vui lòng nhập tài sản cố định (tỷ đồng)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering fixed assets',
    },
    SupplierInfor_EnterSampleInvoiceReceiptFile: {
      key: 'SupplierInfor_EnterSampleInvoiceReceiptFile',
      value: 'Vui lòng upload file hóa đơn mẫu/phiếu thu/biên lai',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for uploading sample invoice/receipt file',
    },
    SupplierInfor_UpInvoiceIssuanceInforFile: {
      key: 'SupplierInfor_UpInvoiceIssuanceInforFile',
      value: 'Vui lòng upload file thông tin phát hành hóa đơn',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for uploading invoice issuance information file',
    },
    SupplierInfor_EnterBusinessLicenseTaxCode: {
      key: 'SupplierInfor_EnterBusinessLicenseTaxCode',
      value: 'Vui lòng upload giấy phép kinh doanh/Mã số thuế',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for uploading business license/tax code',
    },
    SupplierInfor_EnterDescriptionBusiness: {
      key: 'SupplierInfor_EnterDescriptionBusiness',
      value: 'Vui lòng nhập mô tả về doanh nghiệp (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering business description',
    },
    SupplierInfor_ChooseProvince: {
      key: 'SupplierInfor_ChooseProvince',
      value: 'Chọn tỉnh thành',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Placeholder for choosing a province',
    },
    SupplierInfor_ChooseDistrict: {
      key: 'SupplierInfor_ChooseDistrict',
      value: 'Chọn quận huyện',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Placeholder for choosing a district',
    },
    SupplierInfor_ChooseWards: {
      key: 'SupplierInfor_ChooseWards',
      value: 'Chọn phường xã',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Placeholder for choosing wards',
    },
    SupplierInfor_ChooseTradingAddress: {
      key: 'SupplierInfor_ChooseTradingAddress',
      value: 'Chọn địa chỉ giao dịch',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Placeholder for choosing a trading address',
    },
    SupplierInfor_EnterBankName: {
      key: 'SupplierInfor_EnterBankName',
      value: 'Vui lòng nhập tên ngân hàng (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering bank name',
    },
    SupplierInfor_EnterBankNumber: {
      key: 'SupplierInfor_EnterBankNumber',
      value: 'Vui lòng nhập số tài khoản ngân hàng (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering bank account number',
    },
    SupplierInfor_EnterBankBranch: {
      key: 'SupplierInfor_EnterBankBranch',
      value: 'Vui lòng nhập tên chi nhánh ngân hàng (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering bank branch name',
    },
    SupplierInfor_EnterContactName: {
      key: 'SupplierInfor_EnterContactName',
      value: 'Vui lòng nhập người liên hệ (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering contact name',
    },
    SupplierInfor_EnterPhoneNumber: {
      key: 'SupplierInfor_EnterPhoneNumber',
      value: 'Vui lòng nhập số điện thoại (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering phone number',
    },
    SupplierInfor_EnterEmail: {
      key: 'SupplierInfor_EnterEmail',
      value: 'Vui lòng nhập email (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for entering email',
    },
    SupplierInfor_UpFileNoticeOpenAccount: {
      key: 'SupplierInfor_UpFileNoticeOpenAccount',
      value: 'Vui lòng upload file thông báo mở tài khoản/mẫu 08',
      languageType: 'VI',
      path: 'supplier-info',
      description: 'Error message for uploading file notice of account opening',
    },
    //component
    SupplierInfor_ErrorOccurred: {
      key: 'SupplierInfor_ErrorOccurred',
      value: 'Đã có lỗi xảy ra vui lòng kiểm tra lại dữ liệu.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Đã có lỗi xảy ra vui lòng kiểm tra lại dữ liệu.',
    },
    SupplierInfor_AddCapacitySuccess: {
      key: 'SupplierInfor_AddCapacitySuccess',
      value: 'Đã bổ sung năng lực thành thành công',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Đã bổ sung năng lực thành thành công',
    },
    SupplierInfor_MaximumSizeToUpload: {
      key: 'SupplierInfor_MaximumSizeToUpload',
      value: 'Kích thước tối đa để upload là',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Kích thước tối đa để upload là',
    },
    SupplierInfor_ChooseAnotherFile: {
      key: 'SupplierInfor_ChooseAnotherFile',
      value: 'vui lòng chọn file khác',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'vui lòng chọn file khác',
    },
    SupplierInfor_DataEnterFirst: {
      key: 'SupplierInfor_DataEnterFirst',
      value: 'Vui lòng nhập dữ liệu trước',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng nhập dữ liệu trước',
    },
    SupplierInfor_DataEnterComplete: {
      key: 'SupplierInfor_DataEnterComplete',
      value: 'Vui lòng nhập đầy đủ dữ liệu ',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Vui lòng nhập đầy đủ dữ liệu ',
    },
    SupplierInfor_NotDataCriteria: {
      key: 'SupplierInfor_NotDataCriteria',
      value: 'Chưa nhập dữ liệu cho tiêu chí: ',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Chưa nhập dữ liệu cho tiêu chí: ',
    },
    SupplierInfor_NotFullyDataCriteria: {
      key: 'SupplierInfor_NotFullyDataCriteria',
      value: 'Chưa nhập đầy đủ dữ liệu cho tiêu chí:',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Chưa nhập đầy đủ dữ liệu cho tiêu chí:',
    },
    SupplierInfor_ItemNoCriteria: {
      key: 'SupplierInfor_ItemNoCriteria',
      value: 'Item chưa có thiết lập tiêu chí năng lực.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Item chưa có thiết lập tiêu chí năng lực.',
    },
    SupplierInfor_ItemNoCriteriaUpExcel: {
      key: 'SupplierInfor_ItemNoCriteriaUpExcel',
      value: 'Item chưa có thiết lập tiêu chí năng lực có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Item chưa có thiết lập tiêu chí năng lực có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    SupplierInfor_Note: {
      key: 'SupplierInfor_Note',
      value: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    SupplierInfor_DownTemp: {
      key: 'SupplierInfor_DownTemp',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Tải template thành công!',
    },
    SupplierInfor_YearType: {
      key: 'SupplierInfor_YearType',
      value: '[Kiểu theo năm] Nhập như ví dụ:',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: '[Kiểu theo năm] Nhập như ví dụ:',
    },
    SupplierInfor_ChangeColumn: {
      key: 'SupplierInfor_ChangeColumn',
      value: 'File không đúng template (cột đã bị thay đổi)',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'File không đúng template (cột đã bị thay đổi)',
    },
    SupplierInfor_ChangeLine: {
      key: 'SupplierInfor_ChangeLine',
      value: 'File không đúng template (dòng đã bị thay đổi)!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'File không đúng template (dòng đã bị thay đổi)!',
    },
    SupplierInfor_ValueCriteria: {
      key: 'SupplierInfor_ValueCriteria',
      value: 'Giá trị của tiêu chí',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'Giá trị của tiêu chí',
    },
    SupplierInfor_NotBlank: {
      key: 'SupplierInfor_NotBlank',
      value: 'bắt buộc không được để trống!',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'bắt buộc không được để trống!',
    },
    SupplierInfor_ValueIs: {
      key: 'SupplierInfor_ValueIs',
      value: '[Giá trị] là',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: '[Giá trị] là',
    },
    SupplierInfor_NotType: {
      key: 'SupplierInfor_NotType',
      value: 'không phải kiểu',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'không phải kiểu',
    },
    SupplierInfor_NotList: {
      key: 'SupplierInfor_NotList',
      value: 'không nằm trong List',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'không nằm trong List',
    },
    SupplierInfor_NotValidYear: {
      key: 'SupplierInfor_NotValidYear',
      value: 'không hợp lệ với kiểu theo năm kiểu',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'không hợp lệ với kiểu theo năm kiểu',
    },
    SupplierInfor_YMD: {
      key: 'SupplierInfor_YMD',
      value: 'ngày phải có định dạng yyyy-mm-dd',
      languageType: 'VI',
      path: 'supplier-additional-capacity',
      description: 'ngày phải có định dạng yyyy-mm-dd',
    },

    //#endregion

    //#region SupplierRegistration
    SupplierRegistration_GeneralInformation: {
      key: 'SupplierRegistration_GeneralInformation',
      value: 'Thông tin chung',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thông tin chung',
    },
    SupplierRegistration_CompanyName: {
      key: 'SupplierRegistration_CompanyName',
      value: 'Tên doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên doanh nghiệp',
    },
    SupplierRegistration_TradingName: {
      key: 'SupplierRegistration_TradingName',
      value: 'Tên giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên giao dịch',
    },
    SupplierRegistration_CompanyCreateYear: {
      key: 'SupplierRegistration_CompanyCreateYear',
      value: 'Năm thành lập công ty',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Năm thành lập công ty',
    },
    SupplierRegistration_BusinessCode: {
      key: 'SupplierRegistration_BusinessCode',
      value: 'Mã số doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mã số doanh nghiệp',
    },
    SupplierRegistration_OfficeAddress: {
      key: 'SupplierRegistration_OfficeAddress',
      value: 'Địa chỉ trụ sở',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ trụ sở',
    },
    SupplierRegistration_EnterCompanyName: {
      key: 'SupplierRegistration_EnterCompanyName',
      value: 'Vui lòng nhập tên doanh nghiệp (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lời nhắc nhập tên doanh nghiệp',
    },
    SupplierRegistration_ChooseOfficeAddress: {
      key: 'SupplierRegistration_ChooseOfficeAddress',
      value: 'Chọn địa chỉ trụ sở',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn địa chỉ trụ sở',
    },
    SupplierRegistration_Choose: {
      key: 'SupplierRegistration_Choose',
      value: 'Chọn',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn',
    },
    SupplierRegistration_EnterTradingName: {
      key: 'SupplierRegistration_EnterTradingName',
      value: 'Vui lòng nhập tên giao dịch (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lời nhắc nhập tên giao dịch',
    },
    SupplierRegistration_EnterOfficeAddress: {
      key: 'SupplierRegistration_EnterOfficeAddress',
      value: 'Vui lòng chọn địa chỉ trụ sở (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lời nhắc chọn địa chỉ trụ sở',
    },
    SupplierRegistration_Password: {
      key: 'SupplierRegistration_Password',
      value: 'Mật khẩu',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mật khẩu',
    },
    SupplierRegistration_EnterPassword: {
      key: 'SupplierRegistration_EnterPassword',
      value: 'Vui lòng nhập mật khẩu!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập mật khẩu!',
    },
    SupplierRegistration_PasswordConfirm: {
      key: 'SupplierRegistration_PasswordConfirm',
      value: 'Xác nhận mật khẩu',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Xác nhận mật khẩu',
    },
    SupplierRegistration_EnterPasswordConfirm: {
      key: 'SupplierRegistration_EnterPasswordConfirm',
      value: 'Vui lòng nhập xác nhận mật khẩu!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập xác nhận mật khẩu!',
    },
    SupplierRegistration_SelectBusinessField: {
      key: 'SupplierRegistration_SelectBusinessField',
      value: 'Chọn lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn lĩnh vực kinh doanh',
    },
    SupplierRegistration_AddBusinessArea: {
      key: 'SupplierRegistration_AddBusinessArea',
      value: 'Thêm lĩnh vực kinh doanh',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thêm lĩnh vực kinh doanh',
    },
    SupplierRegistration_DownloadTemplate: {
      key: 'SupplierRegistration_DownloadTemplate',
      value: 'Tải template',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tải template',
    },
    SupplierRegistration_UploadExcel: {
      key: 'SupplierRegistration_UploadExcel',
      value: 'Upload Excel',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Upload Excel',
    },
    SupplierRegistration_Delete: {
      key: 'SupplierRegistration_Delete',
      value: 'Xóa',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Xóa',
    },
    SupplierRegistration_TradingAddress: {
      key: 'SupplierRegistration_TradingAddress',
      value: 'Địa chỉ giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Địa chỉ giao dịch',
    },
    SupplierRegistration_PleaseChooseTradingAddress: {
      key: 'SupplierRegistration_PleaseChooseTradingAddress',
      value: 'Vui lòng chọn địa chỉ giao dịch (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn địa chỉ giao dịch (1-250 kí tự)!',
    },
    SupplierRegistration_ChooseTradingAddress: {
      key: 'SupplierRegistration_ChooseTradingAddress',
      value: 'Chọn địa chỉ giao dịch',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn địa chỉ giao dịch',
    },
    SupplierRegistration_LegalRepresentative: {
      key: 'SupplierRegistration_LegalRepresentative',
      value: 'Người đại diện pháp luật',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Người đại diện pháp luật',
    },
    SupplierRegistration_EnterLegalRepresentative: {
      key: 'SupplierRegistration_EnterLegalRepresentative',
      value: 'Vui lòng nhập người đại diện pháp luật (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập người đại diện pháp luật (1-50 kí tự)!',
    },
    SupplierRegistration_DirectorName: {
      key: 'SupplierRegistration_DirectorName',
      value: 'Tên giám đốc',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên giám đốc',
    },
    SupplierRegistration_EnterDirectorName: {
      key: 'SupplierRegistration_EnterDirectorName',
      value: 'Vui lòng nhập tên giám đốc (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên giám đốc (1-50 kí tự)!',
    },
    SupplierRegistration_CharterCapital: {
      key: 'SupplierRegistration_CharterCapital',
      value: 'Vốn điều lệ (tỷ đồng)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vốn điều lệ (tỷ đồng)',
    },
    SupplierRegistration_FixedAssets: {
      key: 'SupplierRegistration_FixedAssets',
      value: 'Tài sản cố định (tỷ đồng)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tài sản cố định (tỷ đồng)',
    },
    SupplierRegistration_EnterFixedAssets: {
      key: 'SupplierRegistration_EnterFixedAssets',
      value: 'Vui lòng nhập tài sản cố định (tỷ đồng)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tài sản cố định (tỷ đồng)!',
    },
    SupplierRegistration_SampleInvoiceReceiptFile: {
      key: 'SupplierRegistration_SampleInvoiceReceiptFile',
      value: 'File hóa đơn mẫu/phiếu thu/biên lai',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File hóa đơn mẫu/phiếu thu/biên lai',
    },
    SupplierRegistration_EnterSampleInvoiceReceiptFile: {
      key: 'SupplierRegistration_EnterSampleInvoiceReceiptFile',
      value: 'Vui lòng upload file hóa đơn mẫu/phiếu thu/biên lai',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload file hóa đơn mẫu/phiếu thu/biên lai',
    },
    SupplierRegistration_InvoiceIssuanceInforFile: {
      key: 'SupplierRegistration_InvoiceIssuanceInforFile',
      value: 'File thông tin phát hành hóa đơn',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File thông tin phát hành hóa đơn',
    },
    SupplierRegistration_UpInvoiceIssuanceInforFile: {
      key: 'SupplierRegistration_UpInvoiceIssuanceInforFile',
      value: 'Vui lòng upload file thông tin phát hành hóa đơn',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload file thông tin phát hành hóa đơn',
    },
    SupplierRegistration_BusinessLicenseTaxCode: {
      key: 'SupplierRegistration_BusinessLicenseTaxCode',
      value: 'Giấy phép kinh doanh/Mã số thuế',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Giấy phép kinh doanh/Mã số thuế',
    },
    SupplierRegistration_EnterBusinessLicenseTaxCode: {
      key: 'SupplierRegistration_EnterBusinessLicenseTaxCode',
      value: 'Vui lòng upload giấy phép kinh doanh/Mã số thuế',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload giấy phép kinh doanh/Mã số thuế',
    },
    SupplierRegistration_IntroCode: {
      key: 'SupplierRegistration_IntroCode',
      value: 'Mã giới thiệu',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mã giới thiệu',
    },
    SupplierRegistration_EnterIntroCode: {
      key: 'SupplierRegistration_EnterIntroCode',
      value: 'Vui lòng nhập mã giới thiệu (0-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập mã giới thiệu (0-50 kí tự)!',
    },
    SupplierRegistration_DescriptionBusiness: {
      key: 'SupplierRegistration_DescriptionBusiness',
      value: 'Mô tả về doanh nghiệp',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mô tả về doanh nghiệp',
    },
    SupplierRegistration_EnterDescriptionBusiness: {
      key: 'SupplierRegistration_EnterDescriptionBusiness',
      value: 'Vui lòng nhập mô tả về doanh nghiệp (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập mô tả về doanh nghiệp (1-250 kí tự)!',
    },
    SupplierRegistration_AccountInformation: {
      key: 'SupplierRegistration_AccountInformation',
      value: 'Thông tin tài khoản',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thông tin tài khoản',
    },
    SupplierRegistration_LoginName: {
      key: 'SupplierRegistration_LoginName',
      value: 'Tên đăng nhập',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên đăng nhập',
    },
    SupplierRegistration_EnterLoginName: {
      key: 'SupplierRegistration_EnterLoginName',
      value: 'Vui lòng nhập tên đăng nhập (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên đăng nhập (1-50 kí tự)!',
    },
    SupplierRegistration_BankName: {
      key: 'SupplierRegistration_BankName',
      value: 'Tên ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên ngân hàng',
    },
    SupplierRegistration_EnterBankName: {
      key: 'SupplierRegistration_EnterBankName',
      value: 'Vui lòng nhập tên ngân hàng (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên ngân hàng (1-250 kí tự)!',
    },
    SupplierRegistration_BankNumber: {
      key: 'SupplierRegistration_BankNumber',
      value: 'Số tài khoản ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số tài khoản ngân hàng',
    },
    SupplierRegistration_EnterBankNumber: {
      key: 'SupplierRegistration_EnterBankNumber',
      value: 'Vui lòng nhập số tài khoản ngân hàng (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số tài khoản ngân hàng (1-50 kí tự)!',
    },
    SupplierRegistration_BankBranch: {
      key: 'SupplierRegistration_BankBranch',
      value: 'Tên chi nhánh ngân hàng',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tên chi nhánh ngân hàng',
    },
    SupplierRegistration_EnterBankBranch: {
      key: 'SupplierRegistration_EnterBankBranch',
      value: 'Vui lòng nhập tên chi nhánh ngân hàng (1-250 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập tên chi nhánh ngân hàng (1-250 kí tự)!',
    },
    SupplierRegistration_FileNoticeOpenAccount: {
      key: 'SupplierRegistration_FileNoticeOpenAccount',
      value: 'File thông báo mở tài khoản/mẫu 08',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File thông báo mở tài khoản/mẫu 08',
    },
    SupplierRegistration_UpFileNoticeOpenAccount: {
      key: 'SupplierRegistration_UpFileNoticeOpenAccount',
      value: 'Vui lòng upload file thông báo mở tài khoản/mẫu 08',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng upload file thông báo mở tài khoản/mẫu 08',
    },
    SupplierRegistration_ContactName: {
      key: 'SupplierRegistration_ContactName',
      value: 'Người liên hệ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Người liên hệ',
    },
    SupplierRegistration_EnterContactName: {
      key: 'SupplierRegistration_EnterContactName',
      value: 'Vui lòng nhập người liên hệ (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập người liên hệ (1-50 kí tự)!',
    },
    SupplierRegistration_PhoneNumber: {
      key: 'SupplierRegistration_PhoneNumber',
      value: 'Số điện thoại',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Số điện thoại',
    },
    SupplierRegistration_EnterPhoneNumber: {
      key: 'SupplierRegistration_EnterPhoneNumber',
      value: 'Vui lòng nhập số điện thoại (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập số điện thoại (1-50 kí tự)!',
    },
    SupplierRegistration_Email: {
      key: 'SupplierRegistration_Email',
      value: 'Email',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Email',
    },
    SupplierRegistration_EnterEmail: {
      key: 'SupplierRegistration_EnterEmail',
      value: 'Vui lòng nhập email (1-50 kí tự)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập email (1-50 kí tự)!',
    },
    SupplierRegistration_ChooseAddress: {
      key: 'SupplierRegistration_ChooseAddress',
      value: 'Chọn địa chỉ',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn địa chỉ',
    },
    SupplierRegistration_ChooseProvince: {
      key: 'SupplierRegistration_ChooseProvince',
      value: 'Tỉnh thành',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tỉnh thành',
    },
    SupplierRegistration_Province: {
      key: 'SupplierRegistration_Province',
      value: 'Chọn tỉnh thành',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn tỉnh thành',
    },
    SupplierRegistration_District: {
      key: 'SupplierRegistration_District',
      value: 'Quận huyện',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Quận huyện',
    },
    SupplierRegistration_ChooseDistrict: {
      key: 'SupplierRegistration_ChooseDistrict',
      value: 'Chọn quận huyện',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn quận huyện',
    },
    SupplierRegistration_Wards: {
      key: 'SupplierRegistration_Wards',
      value: 'Phường xã',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Phường xã',
    },
    SupplierRegistration_ChooseWards: {
      key: 'SupplierRegistration_ChooseWards',
      value: 'Chọn phường xã',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chọn phường xã',
    },
    SupplierRegistration_AdditionalInformationVillage: {
      key: 'SupplierRegistration_AdditionalInformationVillage',
      value: 'Thông tin thêm (thôn, xóm, đường, số nhà...)',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Thông tin thêm (thôn, xóm, đường, số nhà...)',
    },
    SupplierRegistration_ChooseBusinessArea: {
      key: 'SupplierRegistration_ChooseBusinessArea',
      value: 'Vui lòng chọn lĩnh vực kinh doanh để thêm!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn lĩnh vực kinh doanh để thêm!',
    },
    SupplierRegistration_AddedBusinessAreaCheck: {
      key: 'SupplierRegistration_AddedBusinessAreaCheck',
      value: 'Lĩnh vực kinh doanh đã được thêm, vui lòng kiểm tra lại!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lĩnh vực kinh doanh đã được thêm, vui lòng kiểm tra lại!',
    },
    SupplierRegistration_NotFoundBusinessArea: {
      key: 'SupplierRegistration_NotFoundBusinessArea',
      value: 'Không tìm thấy lĩnh vực kinh doanh, vui lòng kiểm tra lại!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Không tìm thấy lĩnh vực kinh doanh, vui lòng kiểm tra lại!',
    },
    SupplierRegistration_ErrorOccurred: {
      key: 'SupplierRegistration_ErrorOccurred',
      value: 'Đã có lỗi xảy ra vui lòng kiểm tra lại dữ liệu.',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Đã có lỗi xảy ra vui lòng kiểm tra lại dữ liệu.',
    },
    SupplierRegistration_PasswordNotMatch: {
      key: 'SupplierRegistration_PasswordNotMatch',
      value: 'Mật khẩu không trùng khớp.',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Mật khẩu không trùng khớp.',
    },
    SupplierRegistration_ChooseBusiness: {
      key: 'SupplierRegistration_ChooseBusiness',
      value: 'Vui lòng chọn lĩnh vực kinh doanh.',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng chọn lĩnh vực kinh doanh.',
    },
    SupplierRegistration_PleaseWaitForEmailResponse: {
      key: 'SupplierRegistration_PleaseWaitForEmailResponse',
      value: 'Đã tạo thành công, vui lòng đợi mail phản hồi.',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Đã tạo thành công, vui lòng đợi mail phản hồi.',
    },
    SupplierRegistration_ItemNoCriteria: {
      key: 'SupplierRegistration_ItemNoCriteria',
      value: 'Item chưa có thiết lập tiêu chí năng lực.',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Item chưa có thiết lập tiêu chí năng lực.',
    },
    SupplierRegistration_ItemNoCriteriaUpExcel: {
      key: 'SupplierRegistration_ItemNoCriteriaUpExcel',
      value: 'Item chưa có thiết lập tiêu chí năng lực có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Item chưa có thiết lập tiêu chí năng lực có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    SupplierRegistration_Note: {
      key: 'SupplierRegistration_Note',
      value: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.',
    },
    SupplierRegistration_DownTemp: {
      key: 'SupplierRegistration_DownTemp',
      value: 'Tải template thành công!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Tải template thành công!',
    },
    SupplierRegistration_ChangeColumn: {
      key: 'SupplierRegistration_ChangeColumn',
      value: 'File không đúng template (cột đã bị thay đổi)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File không đúng template (cột đã bị thay đổi)!',
    },
    SupplierRegistration_ChangeLine: {
      key: 'SupplierRegistration_ChangeLine',
      value: 'File không đúng template (dòng đã bị thay đổi)!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'File không đúng template (dòng đã bị thay đổi)!',
    },
    SupplierRegistration_ValueCriteria: {
      key: 'SupplierRegistration_ValueCriteria',
      value: 'Giá trị của tiêu chí',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Giá trị của tiêu chí',
    },
    SupplierRegistration_NotBlank: {
      key: 'SupplierRegistration_NotBlank',
      value: 'bắt buộc không được để trống!',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'bắt buộc không được để trống!',
    },
    SupplierRegistration_ValueIs: {
      key: 'SupplierRegistration_ValueIs',
      value: '[Giá trị] là',
      languageType: 'VI',
      path: 'supplier-registration',
      description: '[Giá trị] là',
    },
    SupplierRegistration_NotType: {
      key: 'SupplierRegistration_NotType',
      value: 'không phải kiểu',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'không phải kiểu',
    },
    SupplierRegistration_YMD: {
      key: 'SupplierRegistration_YMD',
      value: 'ngày phải có định dạng yyyy-mm-dd',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'ngày phải có định dạng yyyy-mm-dd',
    },
    SupplierRegistration_NotValidYear: {
      key: 'SupplierRegistration_NotValidYear',
      value: 'không hợp lệ với kiểu theo năm kiểu',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'không hợp lệ với kiểu theo năm kiểu',
    },
    SupplierRegistration_NotList: {
      key: 'SupplierRegistration_NotList',
      value: 'không nằm trong List',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'không nằm trong List',
    },
    SupplierRegistration_MaximumSizeToUpload: {
      key: 'SupplierRegistration_MaximumSizeToUpload',
      value: 'Kích thước tối đa để upload là',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Kích thước tối đa để upload là',
    },
    SupplierRegistration_ChooseAnotherFile: {
      key: 'SupplierRegistration_ChooseAnotherFile',
      value: 'vui lòng chọn file khác',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'vui lòng chọn file khác',
    },
    SupplierRegistration_UploadFile: {
      key: 'SupplierRegistration_UploadFile',
      value: 'Upload File',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Upload File',
    },
    SupplierRegistration_ViewFile: {
      key: 'SupplierRegistration_ViewFile',
      value: 'Xem file',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Xem file',
    },
    SupplierRegistration_YearType: {
      key: 'SupplierRegistration_YearType',
      value: '[Kiểu theo năm] Nhập như ví dụ:',
      languageType: 'VI',
      path: 'supplier-registration',
      description: '[Kiểu theo năm] Nhập như ví dụ:',
    },
    SupplierRegistration_DataEnterFirst: {
      key: 'SupplierRegistration_DataEnterFirst',
      value: 'Vui lòng nhập dữ liệu trước',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập dữ liệu trước',
    },
    SupplierRegistration_NotDataCriteria: {
      key: 'SupplierRegistration_NotDataCriteria',
      value: 'Chưa nhập dữ liệu cho tiêu chí:',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chưa nhập dữ liệu cho tiêu chí:',
    },
    SupplierRegistration_DataEnterComplete: {
      key: 'SupplierRegistration_DataEnterComplete',
      value: 'Vui lòng nhập đầy đủ dữ liệu',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Vui lòng nhập đầy đủ dữ liệu',
    },
    SupplierRegistration_NotFullyDataCriteria: {
      key: 'SupplierRegistration_NotFullyDataCriteria',
      value: 'Chưa nhập đầy đủ dữ liệu cho tiêu chí:',
      languageType: 'VI',
      path: 'supplier-registration',
      description: 'Chưa nhập đầy đủ dữ liệu cho tiêu chí:',
    },
    //#endregion

    //#region welcome-New-User
    WelcomeNewUser_GoToHomePage: {
      key: 'WelcomeNewUser_GoToHomePage',
      value: 'Đi tới trang chủ',
      languageType: 'VI',
      path: 'welcome-new-user',
      description: 'Đi tới trang chủ',
    },
    WelcomeNewUser_SignUpSuccess: {
      key: 'WelcomeNewUser_SignUpSuccess',
      value: 'Chúc mừng quý công ty đã đăng ký thành công!',
      languageType: 'VI',
      path: 'welcome-new-user',
      description: 'Chúc mừng quý công ty đã đăng ký thành công!',
    },
    WelcomeNewUser_PleaseWaitForEmailResponse: {
      key: 'WelcomeNewUser_PleaseWaitForEmailResponse',
      value: 'Đã tạo thành công, vui lòng đợi mail phản hồi',
      languageType: 'VI',
      path: 'welcome-new-user',
      description: 'Đã tạo thành công, vui lòng đợi mail phản hồi',
    },
    WelcomeNewUser_NoticeToEmail: {
      key: 'WelcomeNewUser_NoticeToEmail',
      value: 'Thông tin đăng ký của quý công ty sẽ được gửi đến email.',
      languageType: 'VI',
      path: 'welcome-new-user',
      description: 'Thông tin đăng ký của quý công ty sẽ được gửi đến email.',
    },
    WelcomeNewUser_IfNotInfor: {
      key: 'WelcomeNewUser_IfNotInfor',
      value: 'Nếu không nhận được thông tin, Quý công ty vui lòng liên hệ bộ phận mua hàng của APE',
      languageType: 'VI',
      path: 'welcome-new-user',
      description: 'Nếu không nhận được thông tin, Quý công ty vui lòng liên hệ bộ phận mua hàng của APE',
    },
    //#endregion
  },
}
