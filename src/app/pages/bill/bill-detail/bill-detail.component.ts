import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/base/enumData'
import { ApiService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  selector: 'app-bill-detail',
  templateUrl: './bill-detail.component.html',
  styleUrls: ['./bill-detail.component.scss'],
})
export class BillDetailComponent {
  dataObject: any = {}
  isEditItem = false
  modalTitle = 'Chi tiết hóa đơn'
  language_key: any
  lstCompany: any = []
  referencesInvoice = enumData.referencesInvoice
  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<BillDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private router: Router,
    private route: ActivatedRoute,
    private languageService: LanguageService
  ) {}

  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()

    this.onLoadDetail(this.data.id)
  }

  onLoadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BILL.FIND_DETAIL, { id: id }).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.dataObject = res
      }
    })
  }

  onBack(): void {
    this.dialogRef.close()
  }

  downloadFileFromS3(url: string) {
    window.open(url, '_blank')
  }
}
