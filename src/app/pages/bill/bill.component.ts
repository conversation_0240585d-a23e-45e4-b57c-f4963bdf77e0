import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/base/enumData'
import { AuthenticationService, ApiService, CoreService, StorageService, NotifyService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'
import $ from 'jquery'
import { AddOrEditBillComponent } from './add-or-edit-bill/add-or-edit-bill.component'
import { BillDetailComponent } from './bill-detail/bill-detail.component'

@Component({
  templateUrl: './bill.component.html',
  styleUrls: ['./bill.component.scss'],
})
export class BillComponent {
  language_key: any = {}
  loading = false
  listOfData: any[] = []
  isVisibleRefuse = false
  enumData: any
  maxSizeUpload = enumData.maxSizeUpload
  subscriptions: Subscription = new Subscription()
  dataSearch: any = {}
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  enumStatus: any
  dataObject: any
  dataStatus = this.coreService.convertObjToArray(enumData.BillStatus)
  dataPaymentStatus = this.coreService.convertObjToArray(enumData.BillPaymentStatus)
  enumDataStatus = enumData.BillStatus
  lstCompany: any[] = []
  constructor(
    private authenticationService: AuthenticationService,
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    private notifyService: NotifyService,
    private languageService: LanguageService,
    private router: Router,
    private dialog: MatDialog
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.searchData()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }
  ngAfterViewInit() {
    $('.input-enter').on('keyup', (e) => {
      if (e.key === 'Enter' || e.keyCode === 13) {
        this.searchData()
      }
    })
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const dataSearch: any = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.BILL.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    this.dialog
      .open(AddOrEditBillComponent, { disableClose: false })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  clickEdit(object: any) {
    this.dialog
      .open(AddOrEditBillComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  showDetail(data: any) {
    this.dialog
      .open(BillDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  onDelete(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BILL.CANCEL, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }

  onSendBill(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BILL.SEND_BILL, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }
  onApproved(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.BILL.APPROVED, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }
}
