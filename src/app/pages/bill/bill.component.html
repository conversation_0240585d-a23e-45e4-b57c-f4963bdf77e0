<nz-row class="mt-3">
  <h2>{{ 'DANH SÁCH HÓA ĐƠN' }}</h2>
</nz-row>

<nz-collapse>
  <nz-collapse-panel [nzHeader]="language_key?.Button_Search || 'Tìm kiếm'">
    <nz-row class="mt-3" nzGutter="8">
      <nz-col nzSpan="8">
        <input
          nz-input
          [(ngModel)]="dataSearch.code"
          name="code"
          [placeholder]="language_key?.Bill_SearchInvoiceCode || 'Tìm theo mã hóa đơn'"
          class="input-enter"
        />
      </nz-col>

      <nz-col nzSpan="8">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSearch.status"
          name="status"
          [nzPlaceHolder]="language_key?.Button_ChooseStatus || 'Chọn trạng thái'"
        >
          <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>

      <nz-col nzSpan="8">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSearch.paymentStatus"
          name="paymentStatus"
          [nzPlaceHolder]="language_key?.Bill_SelectPaymentStatus || 'Chọn trạng thái thanh toán'"
        >
          <nz-option *ngFor="let item of dataPaymentStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>
    </nz-row>
    <nz-row nzGutter="8" class="mt-2">
      <nz-col nzSpan="24" class="text-center">
        <button nz-button (click)="searchData(true)">
          <span nz-icon nzType="search"></span>
          {{ language_key?.Button_Search || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3" nzJustify="space-between" nzAlign="middle">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" nzGhost (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus"></span>
      {{ language_key?.Button_Add || 'Thêm mới' }}
    </button>

    <!-- <button nzShape="round" class="mr-2 button-add" nzGhost nz-button (click)="clickAdd()"><span nz-icon nzType="plus"></span>Thêm mới</button> -->
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
    [nzScroll]="{ x: '2000px' }"
  >
    <thead>
      <tr>
        <th>{{ language_key?.Bill_Code || 'Mã hóa đơn' }}</th>
        <th>{{ language_key?.Bill_Contract || 'Hợp đồng' }}</th>
        <th>{{ language_key?.Bill_Po || 'PO' }}</th>
        <th>{{ language_key?.Bill_Value || 'Trị giá' }}</th>
        <th>{{ language_key?.Bill_CurrencyUnit || 'Đơn vị tiền tệ' }}</th>
        <th>{{ language_key?.Bill_Vat || 'Thuế VAT' }}</th>
        <th>{{ language_key?.Bill_TotalValue || 'Tổng trị giá' }}</th>
        <th>{{ language_key?.Button_Status || 'Trạng thái' }}</th>
        <th nzWidth="150px" nzRight>{{ language_key?.Column_Action || 'Tác vụ' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData">
        <td>{{ data.code }}</td>
        <td>{{ data.contractName }}</td>
        <td>{{ data.poCode }}</td>
        <td class="text-right">{{ data.invoiceValue | number }}</td>
        <td>{{ data.currencyName }}</td>
        <td>{{ data.vat }}</td>
        <td class="text-right">{{ data.totalInvoiceValue | number }}</td>
        <td class="mw-25">
          <nz-tag *ngIf="data.statusName" [nzColor]="data.statusColor" style="width: 180px; font-weight: 600; border-radius: 30px">
            <div style="display: flex; align-items: center; justify-content: center">
              <div class="dot"></div>
              <span class="ml-1"> {{ data.statusName }}</span>
            </div>
          </nz-tag>
        </td>

        <td nzRight class="text-center">
          <button
            nz-tooltip
            (click)="showDetail(data)"
            [nzTooltipTitle]="language_key?.Bill_ViewDetail || 'Xem chi tiết'"
            [nzTooltipTitle]="''"
            class="btn-primary mb-2 mt-2 mr-1"
            nz-button
          >
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.NEW.code"
            nz-tooltip
            (click)="clickEdit(data)"
            [nzTooltipTitle]="language_key?.Bill_Update || 'Cập nhật'"
            class="btn-primary mb-2 mt-2 mr-1"
            nz-button
          >
            <span nz-icon nzType="edit"></span>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.NEW.code"
            (nzOnConfirm)="onSendBill(data)"
            nz-tooltip
            [nzTooltipTitle]="'Gửi hóa đơn'"
            nz-button
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn gửi hóa đơn  ?"
            nzPopconfirmPlacement="bottom"
            nzShape="circle"
            class="mr-2"
          >
            <i nz-icon nzType="send" class="text-icon"></i>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.NEW.code"
            (nzOnConfirm)="onDelete(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="language_key?.DELETE || 'Hủy Hóa Đơn'"
            nz-button
            nzDanger
            nz-popconfirm
            nzPopconfirmTitle="Bạn có chắc muốn Hủy hóa đơn  ?"
            nzPopconfirmPlacement="bottom"
            class="mr-2"
          >
            <i nz-icon nzType="delete" class="text-icon"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="text-center">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>
