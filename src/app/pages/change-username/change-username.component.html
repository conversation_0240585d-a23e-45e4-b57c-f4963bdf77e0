<form nz-form [formGroup]="validateForm" (ngSubmit)="submitForm()">
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="password" nzRequired class="text-left">
      {{ this.language_key?.ChangeUsername_CurrentPassword || 'Mật khẩu hiện tại' }}</nz-form-label
    >
    <nz-form-control
      [nzSm]="24"
      [nzXs]="24"
      [nzErrorTip]="this.language_key?.ChangeUsername_PleaseInputCurrentPassword || 'Vui lòng nhập mật khẩu hiện tại!'"
    >
      <input nz-input type="password" id="password" formControlName="password" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="password" nzRequired class="text-left">{{
      this.language_key?.ChangeUsername_NewUsername || 'Tên đăng nhập mới'
    }}</nz-form-label>
    <nz-form-control
      [nzSm]="24"
      [nzXs]="24"
      [nzErrorTip]="this.language_key?.ChangeUsername_PleaseInputNewUsername || 'Vui lòng nhập tên đăng nhập mới!'"
    >
      <input nz-input (keypress)="omit_special_char($event)" id="newUsername" formControlName="newUsername" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item nz-row class="text-center">
    <nz-form-control>
      <button nz-button nzType="primary" [disabled]="validateForm.invalid">
        {{ this.language_key?.ChangeUsername_ChangeUsername || 'Thay đổi tên đăng nhập' }}
      </button>
    </nz-form-control>
  </nz-form-item>
</form>
