import { Component, OnInit } from '@angular/core'
import { FormGroup, FormBuilder, Validators } from '@angular/forms'
import { AuthenticationService, NotifyService } from '../../services'
import { Router } from '@angular/router'
import { LanguageService } from 'src/app/services/language.service'

@Component({ templateUrl: './change-username.component.html' })
export class ChangeUsernameComponent implements OnInit {
  language_key: any = {}
  constructor(
    private fb: FormBuilder,
    private languageService: LanguageService,
    private authService: AuthenticationService,
    private notifyService: NotifyService,
    private router: Router
  ) {}
  validateForm: any = FormGroup

  ngOnInit() {
    this.notifyService.showloading()
    this.validateForm = this.fb.group({
      password: ['', [Validators.required]],
      newUsername: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(256)]],
    })
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.notifyService.hideloading()
  }

  submitForm() {
    for (const i in this.validateForm.controls) {
      if (this.validateForm.controls[i]) {
        this.validateForm.controls[i].markAsDirty()
        this.validateForm.controls[i].updateValueAndValidity()
      }
    }
    if (this.validateForm.valid) {
      this.notifyService.showloading()
      this.authService
        .updateUserName(this.validateForm.value.password, this.validateForm.value.newUsername)
        .toPromise()
        .then((res: any) => {
          this.authService.logout()
          this.router.navigate([`home`])
          // window.location.reload()
          this.notifyService.showSuccess(res.message)
        })
    }
  }

  omit_special_char(event: any) {
    return (
      (event.charCode > 96 && event.charCode < 123) ||
      (event.charCode > 64 && event.charCode < 91) ||
      (event.charCode >= 48 && event.charCode <= 57) ||
      event.charCode <= 31
    )
  }
}
