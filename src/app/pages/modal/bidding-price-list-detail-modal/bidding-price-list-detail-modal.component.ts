import { Component } from '@angular/core'
import { enumData } from '../../../base/enumData'
import { LanguageService } from '../../../services/language.service'
@Component({
  selector: 'app-bidding-price-list-detail-modal',
  templateUrl: './bidding-price-list-detail-modal.component.html',
})
export class BiddingPriceListDetailModalComponent {
  lstOfData: any = []
  isVisible = false
  modalTitle = ''
  loading = false
  dataType = enumData.DataType
  language_key: any = {}

  constructor( private languageService: LanguageService) {}
  
  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  showModal(data: any) {
    this.isVisible = true
    this.modalTitle = `${this.language_key?.Modal_BiddingPricePriceListDetailModal_Details || 'Thông tin chi tiết'} - ${data.name}`
    this.lstOfData = data.__bidPriceListDetails__
  }

  handleCancel() {
    this.isVisible = false
  }
}
