<nz-modal [(nzVisible)]="isVisible" [nzTitle]="modalTitle" [nzFooter]="null" (nzOnCancel)="handleCancel()"
  [nzWidth]="'60vw'">
  <ng-container *nzModalContent>
    <div nz-row>
      <nz-table nz-col nzSpan="24" #basicTable nzBordered [nzData]="lstOfData" [nzLoading]="loading"
        [nzShowPagination]="false">
        <thead>
          <tr>
            <th>{{ language_key?.Modal_BiddingPricePriceListDetailModal_InformationFieldName || "Tên trường thông tin"}}</th>
            <th>{{ language_key?.Modal_BiddingPricePriceListDetailModal_DataType || "Kiểu dữ liệu"}}</th>
            <th>{{ language_key?.Modal_BiddingPricePriceListDetailModal_Value || "Giá trị"}}</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of basicTable.data">
            <tr>
              <td class="mw-25">{{ item.name }}</td>
              <td>{{ item.type }}</td>
              <td class="mw-25">
                <span *ngIf="item.type === dataType.String.code || item.type === dataType.Address.code">
                  {{ item.value }}
                </span>
                <span *ngIf="item.type === dataType.Km.code || item.type === dataType.Time.code">
                  {{ item.value | number: '1.0-2' }}
                </span>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </div>
  </ng-container>
</nz-modal>