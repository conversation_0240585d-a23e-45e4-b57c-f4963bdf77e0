import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, NotifyService } from '../../../services'
import { enumData } from '../../../base/enumData'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { LanguageService } from '../../../services/language.service'

@Component({ 
  selector: 'app-add-bidding-price-modal',
  templateUrl: './add-bidding-price-modal.component.html' 
})

export class AddBiddingPriceModalComponent implements OnInit {
  dataObject: any = {}
  lstUnit: any[] = []
  lstCurrency: any[] = []
  language_key: any = {}
  modalTitle = ''

  constructor(
    private notifyService: NotifyService,
    private apiService: ApiService,
    private dialogRef: MatDialogRef<AddBiddingPriceModalComponent>,
    private languageService: LanguageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.dataObject = this.data
    this.modalTitle = this.language_key?.Modal_AddBiddingPriceModal_EditStructureCategories || 'Chỉnh sửa hạng mục cơ cấu giá'
    if (this.dataObject?.isNew) {
      this.modalTitle = this.language_key?.Modal_AddBiddingPriceModal_AddStructureCategories || 'Thêm mới hạng mục cơ cấu giá'
    }

    this.loadAllSelect()
  }

  loadAllSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.SETTINGSTRING.FIND, { type: enumData.SettingStringType.unit, isDeleted: false }), // 0
      this.apiService.post(this.apiService.SETTINGSTRING.FIND, { type: enumData.SettingStringType.currency, isDeleted: false }), // 1
    ]).then((res) => {
      this.notifyService.hideloading()
      this.lstUnit = res[0]
      this.lstCurrency = res[1]
    })
  }

  closeDialog() {
    this.dialogRef.close(this.dataObject)
  }
}
