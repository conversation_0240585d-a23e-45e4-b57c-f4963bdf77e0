<div nz-row nzJustify="center">
  <div nz-col nzSpan="6"></div>
  <div nz-col nzSpan="12">
    <form nz-form [formGroup]="validateForm" class="login-form mt-3" (ngSubmit)="submitForm()">
      <nz-form-item>
        <nz-form-control nzErrorTip="Please input your username!">
          <nz-input-group nzPrefixIcon="user">
            <input type="text" nz-input formControlName="username" placeholder="Username" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control nzErrorTip="Please input your Password!">
          <nz-input-group nzPrefixIcon="lock" [nzSuffix]="suffixTemplate">
            <input [type]="passwordVisible ? 'text' : 'password'" nz-input formControlName="password" placeholder="Password" />
          </nz-input-group>
          <ng-template #suffixTemplate>
            <span nz-icon [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" (click)="passwordVisible = !passwordVisible"></span>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <button nz-button class="login-form-button login-form-margin" [nzType]="'primary'">{{ language_key?.SignIn_SignIn || 'Đăng nhập' }}</button>
      <a [routerLink]="['/supplier-registration']"> {{ language_key?.SignIn_SignUp || 'Đăng ký ngay!' }} </a>
      <a [routerLink]="['/forgot-password']" class="login-form-forgot">{{ language_key?.SignIn_ForgetPassword || 'Quên mật khẩu' }}</a>
    </form>
  </div>
</div>
