import { Component, OnInit } from '@angular/core'
import { FormGroup, FormBuilder, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { AuthenticationService } from '../../services'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  validateForm: any = FormGroup
  returnUrl!: string
  language_key: any = {}
  submitted = false
  passwordVisible = false
  submitForm() {
    // tslint:disable-next-line: forin
    for (const i in this.validateForm.controls) {
      if (this.validateForm.controls[i]) {
        this.validateForm.controls[i].markAsDirty()
        this.validateForm.controls[i].updateValueAndValidity()
      }
    }
    if (this.validateForm.valid) {
      this.authenticationService.login(this.validateForm.controls.username.value, this.validateForm.controls.password.value).subscribe((res) => {
        this.authenticationService.eventLogin.next(true)
        this.router.navigateByUrl(this.returnUrl)
      })
    }
  }

  constructor(
    private fb: FormBuilder,
    private languageService: LanguageService,
    private route: ActivatedRoute,
    private router: Router,
    private authenticationService: AuthenticationService
  ) {
    if (this.authenticationService.currentUserValue) {
      this.router.navigate(['/'])
    }
  }

  ngOnInit() {
    this.validateForm = this.fb.group({
      username: [null, [Validators.required]],
      password: [null, [Validators.required]],
      remember: [true],
    })
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    // get return url from route parameters or default to '/'
    // tslint:disable-next-line:no-string-literal
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/'
  }
}
