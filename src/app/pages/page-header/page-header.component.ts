import { AfterViewInit, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core'
import { FormGroup, FormBuilder, Validators } from '@angular/forms'
import { ApiService } from 'src/app/services'
import { enumData } from 'src/app/base/enumData'
import { NotifyService, AuthenticationService, CoreService, StorageService } from 'src/app/services'
import { MediaModel } from 'src/app/models/common.model'
import { User } from 'src/app/models/user.model'
import { Router } from '@angular/router'
import { SupplierNotifyComponent } from '../supplier-notify/supplier-notify.component'
import { MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import * as $ from 'jquery'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  selector: 'app-page-header',
  templateUrl: './page-header.component.html',
  styleUrls: ['./page-header.component.scss'],
})
export class PageHeaderComponent implements On<PERSON><PERSON>t, OnD<PERSON>roy, AfterViewInit {
  logo!: MediaModel
  user: any = User
  bannerName = 'CỔNG THÔNG TIN ĐẤU THẦU APE'
  passwordVisible = false
  visiblePopupLogin = false
  lstNotify: any = []
  numNotify = 10
  dataLanguage: any = []
  validateForm: any = FormGroup
  numNotifyNew!: number
  hrefHDSD: string = './../assets/file/huongdansudung.pdf'
  banner: string = './../assets/img/bg.png'
  hrefThuNgo: string = './../assets/file/thungo.pdf'
  selectedLanguage: any
  subscriptions: Subscription = new Subscription()
  language_key: any

  constructor(
    private apiService: ApiService,
    public storage: StorageService,
    public coreService: CoreService,
    private notifyService: NotifyService,
    private languageService: LanguageService,
    private fb: FormBuilder,
    private authenticationService: AuthenticationService,
    private router: Router,
    private dialog: MatDialog
  ) {
    this.user = this.authenticationService.currentUserValue
  }

  customeHeaderClick() {
    this.user = this.authenticationService.currentUserValue
  }

  async loadLanguage(reload: boolean, user: any) {
    this.notifyService.showloading()
    if (user) {
      await this.apiService.post(this.apiService.LANGUAGE_KEY.LOAD_DATA_BY_TENANT, { languageType: this.selectedLanguage }).then((res: any) => {
        if (res) {
          this.notifyService.hideloading()
          this.storage.setItem('lang', JSON.stringify(this.selectedLanguage))
          this.storage.setItem('language', JSON.stringify(res))
        }
      })
    } else {
      await this.apiService.post(this.apiService.LANGUAGE_KEY.LOAD_DATA, { languageType: this.selectedLanguage }).then((res: any) => {
        if (res) {
          this.notifyService.hideloading()
          this.storage.setItem('lang', JSON.stringify(this.selectedLanguage))
          this.storage.setItem('language', JSON.stringify(res))
        }
      })
    }

    if (reload) window.location.reload()
  }

  async ngOnInit(): Promise<void> {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.selectedLanguage = enumData.LanguageType.VI.code
    this.dataLanguage = this.coreService.convertObjToArray(enumData.LanguageType)
    const language: any = JSON.parse(localStorage.getItem('lang') as any)
    if (language) this.selectedLanguage = language
    await this.loadLanguage(false, this.user)
    this.logo = {
       url: 'assets/img/logo_ngang.png',
      atr: 'Logo',
      href: '/',
    }

    const bannerTops = await this.apiService.post(this.apiService.HOMEPAGE.GET_BANNER, {
      position: enumData.BannerClientPosition.Top.code,
    })

    setTimeout(() => {
      if (bannerTops && bannerTops.length) $('.header-line').css('background-image', `url(${bannerTops[0].url})`)
      else $('.header-line').css('background-image', `url(${this.banner})`)
    }, 10)

    const stringClients = await this.apiService.post(this.apiService.HOMEPAGE.GET_SETTING_STRING)
    setTimeout(() => {
      if (stringClients && stringClients.length) {
        const findItem = stringClients.find((v: any) => v.type === enumData.SettingStringClientType.BannerName.code)
        if (findItem) {
          this.bannerName = findItem.name
        }
      }
    }, 10)

    this.validateForm = this.fb.group({
      username: [null, [Validators.required]],
      password: [null, [Validators.required]],
      remember: [true],
    })

    this.loadNotify()

    this.authenticationService.eventLogin.subscribe((res) => {
      if (res === true) {
        this.customeHeaderClick()
        this.loadNotify()
      }
    })
  }

  // tslint:disable-next-line:use-lifecycle-interface
  ngAfterViewInit() {
    setTimeout(() => {
      this.customeHeaderClick()
    })
  }

  submitForm() {
    for (const i in this.validateForm.controls) {
      if (this.validateForm.controls[i]) {
        this.validateForm.controls[i].markAsDirty()
        this.validateForm.controls[i].updateValueAndValidity()
      }
    }
    if (this.validateForm.valid) {
      this.authenticationService.login(this.validateForm.controls.username.value, this.validateForm.controls.password.value).subscribe((res) => {
        this.user = res
        if (this.user) {
          this.visiblePopupLogin = false
          this.router.navigate([`home`])
          window.location.reload()
        } else {
          this.notifyService.showError(`${this.language_key?.PageHeader_NotAccess || 'Không có quyền truy cập.'}`)
        }
      })
    }
  }

  closePopupLogin() {
    this.visiblePopupLogin = false
  }

  logout() {
    this.authenticationService.logout()
    location.reload()
  }

  loadNotify() {
    if (this.authenticationService.currentUserValue) {
      this.apiService.get(this.apiService.HOMEPAGE.GET_NOTIFYS(this.numNotify), {}).then((res) => {
        this.lstNotify = res
        this.numNotifyNew = this.lstNotify?.filter((c: any) => c.isNew).length
      })
    }
  }

  loadMore() {
    this.numNotify += 10
    this.loadNotify()
  }

  viewNotify(item: any) {
    this.dialog
      .open(SupplierNotifyComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((res) => {
        this.loadNotify()
      })
  }

  ngOnDestroy() {
    this.authenticationService.eventLogin.next(false)
  }
}
