.logo {
  margin-left: 15px;
}

.logo img {
  width: auto;
  height: 50px;
}
.header-menu {
  float: right;
}

.login-form {
  max-width: 300px;
}

.login-form-forgot {
  float: right;
}

.login-form-button {
  width: 100%;
}

.text-info-user {
  padding-left: 5px;
  font-weight: bold;
}

.user-info-pd {
  padding: 5px 0;
  font-size: 20px;
}
.user-info-pd i {
  padding-right: 4px;
}

.user-info-pd.log-out {
  font-weight: bold;
}

.header-line {
  text-align: center;
  /* background: #1fbda5 url(../../../assets/img/bg-header.jpg) top center; */
  /* background-attachment: fixed; */
  text-transform: uppercase;
  color: #fff;
  font-weight: bold;
  font-size: 40px;
  padding-top: 140px;
}

.ant-menu {
  background: none !important;
}

.btn-float-bottom {
  position: absolute;
  bottom: 0;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  text-align: center;
}

.popover-scroll {
  border-right: 0px;
  height: 500px !important;
  overflow-x: hidden;
  overflow-y: scroll;
}
.popover-row {
  overflow-x: hidden;
  overflow-y: scroll;
  max-width: 350px;
  text-overflow: ellipsis;
}

.popover-scroll::-webkit-scrollbar {
  background-color: #fff;
  width: 16px
}

.popover-scroll::-webkit-scrollbar-track:hover {
  background-color: #f4f4f4
}

.popover-scroll::-webkit-scrollbar-thumb {
  background-color: #babac0;
  border-radius: 16px;
  border: 5px solid #fff
}

.popover-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #a0a0a5;
  border: 4px solid #f4f4f4
}

