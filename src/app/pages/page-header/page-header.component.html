<div nz-row>
  <div nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="24" nzXl="24">
    <div id="custom-header" (click)="customeHeaderClick()"></div>
    <div
      nz-row
      style="position: absolute; width: 100%; left: 0; top: 0; background-color: transparent; border: none; z-index: 50"
      nzType="flex"
      nzAlign="bottom"
    >
      <div nz-col nzXs="0" nzSm="0" nzMd="0" nzLg="1" nzXl="2"></div>
      <div nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="22" nzXl="20" class="mt-1">
        <div nz-row style="width: 100%; margin-top: 1.5rem">
          <div nz-col nzSpan="6">
            <a [href]="logo.href" *ngIf="logo" class="logo">
              <img [src]="logo.url" [alt]="logo.atr" />
            </a>
          </div>
          <div nz-col nzXs="18">
            <ul nz-menu nzMode="horizontal" class="header-menu">
              <li nz-menu-item class="fw-600">
                <a [routerLink]="['/home-page']">
                  <span>{{ language_key?.PageHeader_HomePage || 'TRANG CHỦ' }}</span>
                </a>
              </li>

              <li nz-menu-item class="fw-600">
                <a [routerLink]="['/home']">
                  <span>{{ language_key?.PageHeader_TenderNotice || 'THÔNG BÁO MỜI THẦU' }}</span>
                </a>
              </li>

              <li nz-menu-item *ngIf="user" class="fw-600">
                <a [routerLink]="['/bidding-history']">
                  <span>{{ language_key?.PageHeader_BiddingHistory || 'LỊCH SỬ ĐẤU THẦU' }}</span>
                </a>
              </li>
              <li nz-menu-item class="fw-600" *ngIf="user">
                <a [routerLink]="['/purchase-order']">
                  <span>
                    {{ language_key?.PageHeader_ManagePO || 'QUẢN LÝ PO' }}
                  </span>
                </a>
              </li>

              <li nz-menu-item class="fw-600" *ngIf="user">
                <a [routerLink]="['/bill']">
                  <span>
                    {{ 'HÓA ĐƠN' }}
                  </span>
                </a>
              </li>

              <li nz-menu-item class="fw-600" *ngIf="user">
                <a [routerLink]="['/payment']">
                  <span>
                    {{ 'THANH TOÁN' }}
                  </span>
                </a>
              </li>

              <li nz-menu-item class="fw-600" *ngIf="user">
                <a [routerLink]="['/inbound']">
                  <span>
                    {{ 'THÔNG BÁO GIAO HÀNG' }}
                  </span>
                </a>
              </li>

              <li *ngIf="user?.supplierId" nz-menu-item class="fw-600">
                <a [routerLink]="['/price-quote']">
                  <span>
                    {{ 'BÁO GIÁ NHANH' }}
                  </span>
                </a>
              </li>

              <li
                nz-menu-item
                *ngIf="!user"
                nz-popover
                [nzPopoverTitle]="language_key?.PageHeader_Login || 'ĐĂNG NHẬP'"
                [(nzPopoverVisible)]="visiblePopupLogin"
                nzPopoverTrigger="click"
                [nzPopoverContent]="loginTemplate"
                nzPopoverPlacement="bottomRight"
                class="fw-600"
                style="color: white !important;"
              >
                {{ language_key?.PageHeader_Login || 'ĐĂNG NHẬP' }}
              </li>
              <li nz-menu-item class="fw-600" *ngIf="!user">
                <a [routerLink]="['/faq-category']">
                  <span>{{ language_key?.PageHeader_FAQ || 'FAQ' }}</span>
                </a>
              </li>
              <li nz-menu-item class="fw-600" *ngIf="!user">
                <a
                  ><span>{{ language_key?.PageHeader_UserManual || 'HƯỚNG DẪN SỬ DỤNG' }}</span></a
                >
              </li>

              <li nz-menu-item *ngIf="user" nz-popover [nzPopoverContent]="notifyTemplate" nzPopoverPlacement="bottomRight" class="pr-0 fw-600">
                <nz-badge [nzCount]="numNotifyNew" *ngIf="numNotifyNew > 0">
                  <b>{{ language_key?.PageHeader_Notice || 'THÔNG BÁO' }}&emsp;</b>
                </nz-badge>
                <span *ngIf="numNotifyNew === 0">{{ language_key?.PageHeader_Notice || 'THÔNG BÁO' }}</span>
              </li>

              <li nz-menu-item *ngIf="user" nz-popover [nzPopoverContent]="infoTemplate" nzPopoverPlacement="bottomRight" class="pr-0 fw-600">
                <span>
                  <span class="header-right-avatar mr-1">
                    <img src="./assets/img/BiazfanxmamNRoxxVxka.png" alt="avatar" height="30" width="30" />
                  </span>
                  <span class="">{{ user ? user.supplierName : '' }}</span>
                </span>
              </li>
              <li nz-menu-item class="fw-600">
                <a>
                  <span class="mr-3">
                    <nz-select [(ngModel)]="selectedLanguage" name="selectedLanguage" (ngModelChange)="loadLanguage(true, user)" nzAllowClear="false">
                      <nz-option *ngFor="let item of dataLanguage" [nzValue]="item.code" [nzLabel]="item.code"> </nz-option>
                    </nz-select> </span
                ></a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div style="width: 100%; height: 100%; overflow: hidden; z-index: 20; visibility: inherit; opacity: 1; background-color: rgba(255, 255, 255, 0)">
    <div
      style="
        position: absolute;
        top: 0px;
        left: 0px;
        z-index: 0;
        width: 100%;
        height: 100%;
        visibility: inherit;
        opacity: 1;
        transform: matrix(1, 0, 0, 1, 0, 0);
      "
    >
      <div
        class="header-line"
        style="
          background-color: rgba(0, 0, 0, 0);
          background-repeat: no-repeat;
          background-size: cover;
          background-position: center center;
          width: 100%;
          height: 300px;
          opacity: 1;
          visibility: inherit;
          z-index: 20;
        "
      >
        {{ bannerName }}
      </div>
    </div>
  </div>
  <ng-template #loginTemplate>
    <form nz-form [formGroup]="validateForm" class="login-form" (ngSubmit)="submitForm()">
      <nz-form-item>
        <nz-form-control [nzErrorTip]="language_key?.PageHeader_PleaseEnterYourUsername || 'Vui lòng nhập tên đăng nhập!'">
          <nz-input-group nzPrefixIcon="user">
            <input type="text" nz-input formControlName="username" [placeholder]="language_key?.PageHeader_Username || 'Tài khoản đăng nhập'" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control nzErrorTip="Please input your Password!">
          <nz-input-group nzPrefixIcon="lock" [nzSuffix]="suffixTemplate">
            <input
              [type]="passwordVisible ? 'text' : 'password'"
              nz-input
              formControlName="password"
              [placeholder]="language_key?.PageHeader_Password || 'Mật khẩu'"
            />
          </nz-input-group>
          <ng-template #suffixTemplate>
            <span nz-icon [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" (click)="passwordVisible = !passwordVisible"></span>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control>
          <!-- <label ngDefaultControl nz-checkbox formControlName="remember" name="remember">
                        <span>{{ language_key?.REMEMBER_ME || 'Ghi nhớ tôi' }}</span>
                    </label> -->
          <button nz-button class="login-form-button mb-2" [nzType]="'primary'">
            {{ language_key?.PageHeader_Login || 'ĐĂNG NHẬP' }}
          </button>

          <a [routerLink]="['/forgot-password']" (click)="closePopupLogin()" class="login-form-forgot">
            {{ language_key?.PageHeader_ForgotPassword || 'Quên mật khẩu' }}
          </a>

          <a [routerLink]="['/supplier-registration']" (click)="closePopupLogin()">
            {{ language_key?.PageHeader_SignUpNow || 'Đăng ký ngay!' }}
          </a>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>
  <ng-template #infoTemplate>
    <ul nz-menu style="border-right: 0px">
      <li nz-menu-item>
        <a [routerLink]="['/supplier-info']"
          ><span nz-icon nzType="setting"></span>
          {{ language_key?.PageHeader_AccountInformation || 'Thông tin tài khoản' }}
        </a>
      </li>
      <li nz-menu-item>
        <a [routerLink]="['/change-username']"
          ><span nz-icon nzType="user"></span>
          {{ language_key?.PageHeader_ChangeLoginName || 'Thay đổi tên đăng nhập' }}
        </a>
      </li>
      <li nz-menu-item>
        <a [routerLink]="['/change-password']"
          ><span nz-icon nzType="key"></span>
          {{ language_key?.PageHeader_ChangePassword || 'Đổi mật khẩu' }}
        </a>
      </li>
      <li nz-menu-item>
        <a href="javascript:void(0)" (click)="logout()"
          ><span nz-icon nzType="logout"></span>
          {{ language_key?.PageHeader_LogOut || 'Đăng xuất' }}
        </a>
      </li>
    </ul>
  </ng-template>
  <ng-template #notifyTemplate>
    <ul nz-menu class="popover-scroll" *ngIf="lstNotify && lstNotify.length > 0">
      <li nz-menu-item *ngFor="let item of lstNotify">
        <a (click)="viewNotify(item)" *ngIf="item.isNew" class="popover-row">
          <nz-badge nzDot>
            <b>{{ item.message }}&emsp;</b>
          </nz-badge>
        </a>
        <a (click)="viewNotify(item)" *ngIf="!item.isNew" class="popover-row">
          {{ item.message }}
        </a>
      </li>
      <li nz-menu-item></li>
      <li nz-menu-item (click)="loadMore()" class="btn-float-bottom">
        <button nz-button type="button" nzType="primary">
          <span nz-icon nzType="reload"></span> <span>{{ language_key?.PageHeader_SeeMore || 'Xem thêm' }}</span>
        </button>
      </li>
    </ul>
    <i *ngIf="!lstNotify || lstNotify.length === 0">{{ language_key?.PageHeader_NonNotify || 'Chưa có thông báo' }}</i>
  </ng-template>
</div>
