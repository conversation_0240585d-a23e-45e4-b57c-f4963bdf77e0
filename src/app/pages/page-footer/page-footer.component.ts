import { Component, OnInit } from '@angular/core'
import { ApiService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { enumData } from 'src/app/base/enumData'
import { LinkClient } from 'src/app/models/common.model'
import { Subscription } from 'rxjs'
import { LanguageService } from '../../services/language.service'

@Component({
  selector: 'app-page-footer',
  templateUrl: './page-footer.component.html',
  styleUrls: ['./page-footer.component.scss'],
})
export class PageFooterComponent implements OnInit {
  links: any[] = []
  selectedUrl = ''

  footerText = {
    Footer1: `Địa chỉ: 66 đường D, LakeView City, Phường An Phú, 
    Thành phố Thủ Đức, Thành ph<PERSON> Hồ Chí Minh`,
    Footer2: `Email: <EMAIL>`,
    Footer2_mailto: `mailto: <EMAIL>`,
    Footer3: 'Website: http://apetechs.com/',
  }

  languageId: any
  lstLanguage: any[] = []
  subscriptions: Subscription = new Subscription()
  language_key: any

  constructor(
    public storage: StorageService,
    public coreService: CoreService,
    private apiService: ApiService,
    private notifyService: NotifyService,
    private languageService: LanguageService
  ) {}

  async ngOnInit(): Promise<void> {
    this.loadLanguage()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    const sampleLinks = [
      {
        url: 'http://viettel.com.vn',
        name: 'Tập đoàn Viettel',
      },
      {
        url: 'http://www.g2b.go.kr/',
        name: 'Mua sắm công Hàn Quốc',
      },
      {
        url: 'http://www.eperolehan.gov.my',
        name: 'Mua sắm công Malaysia',
      },
      {
        url: 'http://chinhphu.vn/',
        name: 'Chính phủ điện tử',
      },
      {
        url: 'http://www.mpi.gov.vn/',
        name: 'Bộ Kế hoạch và đầu tư',
      },
      {
        url: 'http://www.evn.com.vn/',
        name: 'Tập đoàn điện lực Việt Nam',
      },
      {
        url: 'http://www.vnpt.com.vn/',
        name: 'Tập đoàn VNPT',
      },
      {
        url: 'http://www.hanoi.gov.vn',
        name: 'Thành phố Hà Nội',
      },
      {
        url: 'http://muasamcong.vn',
        name: 'Báo Đấu thầu',
      },
    ]

    const links: LinkClient[] = await this.apiService.post(this.apiService.HOMEPAGE.GET_LINK)
    // .catch((err) => {
    //   this.notifyService.showError(err)
    // })
    this.links = links && links.length ? (links as LinkClient[]) : sampleLinks

    const stringClients = await this.apiService.post(this.apiService.HOMEPAGE.GET_SETTING_STRING)
    // .catch((err) => {
    //   this.notifyService.showError(err)
    // })
    if (stringClients && stringClients.length) {
      let findItem = stringClients.find((v: any) => v.type === enumData.SettingStringClientType.Footer1.code)
      if (findItem) {
        this.footerText.Footer1 = findItem.name
      }

      findItem = stringClients.find((v: any) => v.type === enumData.SettingStringClientType.Footer2.code)
      if (findItem) {
        this.footerText.Footer2 = findItem.name
        this.footerText.Footer2_mailto = findItem.name
      }

      findItem = stringClients.find((v: any) => v.type === enumData.SettingStringClientType.Footer3.code)
      if (findItem) {
        this.footerText.Footer3 = findItem.name
      }
    }
  }

  changeLink() {
    if (this.selectedUrl) {
      // location.href = this.selectedUrl;
      window.open(this.selectedUrl, '_blank')
    }
  }

  async loadLanguage() {
    await this.apiService.post(this.apiService.LANGUAGE.FIND, {}).then((result) => {
      this.lstLanguage = result
      this.storage.setItem('LstLanguage', JSON.stringify(result))

      let langId = localStorage && localStorage.getItem('Language') ? JSON.parse(localStorage.getItem('Language') || '') : null
      if (langId) {
        this.languageId = langId
        this.storage.setItem('Language', JSON.stringify(langId))
        this.changeLanguage(this.languageId)
      } else {
        let lang = result.find((s: any) => s.code === 'VN')
        if (lang) {
          this.apiService.post(this.apiService.LANGUAGE.FIND_LANGUAGE_CONFIG, { id: lang.id }).then((res) => {
            if (res) {
              this.languageId = lang.id
              this.storage.setItem('Language', JSON.stringify(lang.id))
              this.storage.setItem('LanguageConfig', JSON.stringify(res))
              this.changeLanguage(this.languageId)
            }
          })
        }
      }
    })
  }

  async changeLanguage(event: any) {
    if (!this.languageId && localStorage.getItem('LstLanguage')) {
      let lstLanguage = JSON.parse(localStorage.getItem('LstLanguage') ?? '')
      if (lstLanguage) {
        let lang = lstLanguage.find((s: any) => s.code === 'VN')
        if (lang) {
          this.languageId = lang.id
        }
      }
    }

    if (this.languageId) {
      await this.apiService.post(this.apiService.LANGUAGE.FIND_LANGUAGE_CONFIG, { id: this.languageId }).then((res) => {
        if (res) {
          this.storage.setItem('Language', JSON.stringify(this.languageId))
          this.storage.setItem('LanguageConfig', JSON.stringify(res))
        }
      })
    }
  }
}
