<div nz-row nzType="flex" nzJustify="center" nzAlign="bottom">
    <div nz-col nzXs="0" nzSm="0" nzMd="0" nzLg="1" nzXl="2"></div>
    <div nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="22" nzXl="20" class="pd-content" style="display: flex;">
        <div nz-col nzXs="8" nzSm="8">
            <div class="footer-infos">
                <h5>{{footerText.Footer1}}</h5>
                <h5>{{footerText.Footer2}}</h5>
                <h5>{{footerText.Footer3}}</h5>
                <p></p>
            </div>
        </div>
        <div nz-col nzXs="8" nzSm="8">
            <h5>{{ language_key?.PageFooter_TUTORIAL || 'Hướng dẫn' }}</h5>
            <div class="footer-item">
                <a [routerLink]="['/faq-category']">
                    <span>{{ language_key?.PageFooter_FAQ || 'FAQ' }}</span>
                </a>
            </div>
            <div class="footer-item">
                <a><span>{{ language_key?.PageFooter_UserManual || "HƯỚNG DẪN SỬ DỤNG"}}</span></a>
            </div>
        </div>
        <div nz-col nzXs="8" nzSm="8">
            <h5>{{ language_key?.PageFooter_LINK || 'Liên kết' }}</h5>
            <nz-select [(ngModel)]="selectedUrl" nzAllowClear [nzPlaceHolder]="language_key?.PageFooter_ChooseWebsite ||'- Chọn Website - '" style="width: 100%;"
                (ngModelChange)="changeLink()">
                <nz-option *ngFor="let item of links" [nzValue]="item.url" [nzLabel]="item.name"></nz-option>
            </nz-select>
        </div>
        <!-- <div nz-col nzXs="5" nzSm="5" style="padding-left: 50px;">
            <h5>{{ language_key?.LANGUAGE || 'Ngôn Ngữ' }}</h5>
            <nz-select *ngIf="lstLanguage.length > 0" nzShowSearch="false" nzAllowClear="false" [(ngModel)]="languageId" name="languageId"
                (ngModelChange)="changeLanguage($event)" style="width: 150px">
                <ng-container *ngFor="let item of lstLanguage">
                  <nz-option nzCustomContent [nzLabel]="item.name" [nzValue]="item.id">
                    <img style="width: 25px;" src="{{ item.avatarUrl }}"> {{ item.name }}
                  </nz-option>
                </ng-container>
              </nz-select>
        </div> -->
    </div>
    <div nz-col nzXs="0" nzSm="0" nzMd="0" nzLg="1" nzXl="2"></div>
</div>