<div *ngIf="notify">
  <nz-result [nzTitle]="language_key?.Evaluation_FailedAccess || 'T<PERSON>y cập thất bại'" nzStatus="error" nzSubTitle="{{ notify }}"> </nz-result>
</div>
<div *ngIf="dataSuggest">
  <div nz-col [nzSpan]="24">
    <div nz-col [nzSpan]="24">
      <nz-tabset>
        <nz-tab [nzTitle]="language_key?.Evaluation_LegalInformation || 'Thông tin pháp lý'">
          <div *ngIf="dataObject">
            <nz-table
              nz-col
              nzSpan="24"
              class="mb-3"
              #expandTable
              [nzData]="[1, 2]"
              [(nzPageSize)]="pageSize"
              [nzLoading]="loading"
              [nzShowPagination]="false"
            >
              <thead>
                <tr>
                  <th>{{ language_key?.Evaluation_Information || 'Thông tin' }}</th>
                  <th>{{ language_key?.Evaluation_Subscribed || 'Đ<PERSON> đăng ký' }}</th>
                  <th *ngIf="isEnableBtnHistory">{{ language_key?.Evaluation_EditRequest || 'Yêu cầu điều chỉnh' }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ language_key?.Evaluation_EnterpriseCode || 'Mã doanh nghiệp' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.code }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('code')" nz-input readonly value="{{ getHistory('code') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_OfficialName || 'Tên chính thức' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.name }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('name')" nz-input readonly value="{{ getHistory('name') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_DealName || 'Tên giao dịch' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.dealName }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('dealName')" nz-input readonly value="{{ getHistory('dealName') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_OfficeAddress || 'Địa chỉ trụ sở' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.address }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('address')" nz-input readonly value="{{ getHistory('address') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_DealAddress || 'Địa chỉ giao dịch' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.dealAddress }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('dealAddress')" nz-input readonly value="{{ getHistory('dealAddress') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_License || 'Giấy phép ĐKKD' }}</td>
                  <td>
                    <a *ngIf="dataObject.fileMST" href="{{ dataObject.fileMST }}" target="_blank">
                      <span nz-icon nzType="file-text"></span> {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}</a
                    >
                  </td>
                  <td *ngIf="isEnableBtnHistory"></td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_Represent || 'Đại diện pháp luật' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.represen }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('represen')" nz-input readonly value="{{ getHistory('represen') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_CEO_Name || 'Tên giám đốc' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.chief }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('chief')" nz-input readonly value="{{ getHistory('chief') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_BankNumber || 'Số tài khoản' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.bankNumber }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('bankNumber')" nz-input readonly value="{{ getHistory('bankNumber') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_Bank || 'Ngân hàng' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.bankname }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('bankname')" nz-input readonly value="{{ getHistory('bankname') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_Branch || 'Chi nhánh' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.bankBrand }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('bankBrand')" nz-input readonly value="{{ getHistory('bankBrand') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_OpenAccountNotification || 'Thông báo mở TK' }}</td>
                  <td>
                    <a *ngIf="dataObject.fileAccount" href="{{ dataObject.fileAccount }}" target="_blank"
                      ><span nz-icon nzType="file-text"></span> {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}</a
                    >
                  </td>
                  <td *ngIf="isEnableBtnHistory"></td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_ContactName || 'Người liên hệ' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.contactName }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('contactName')" nz-input readonly value="{{ getHistory('contactName') }}" />
                  </td>
                </tr>

                <tr>
                  <td>Email</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.email }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('email')" nz-input readonly value="{{ getHistory('email') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_Phone || 'Điện thoại' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.phone }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('phone')" nz-input readonly value="{{ getHistory('phone') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_EstablishYear || 'Năm thành lập' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.createYear }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('createYear')" nz-input readonly value="{{ getHistory('createYear') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_Capital || 'Vốn điều lệ' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.capital }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('capital')" nz-input readonly value="{{ getHistory('capital') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_Asset || 'Tài sản cố định' }}</td>
                  <td>
                    <input nz-input readonly value="{{ dataObject.assets }}" />
                  </td>
                  <td *ngIf="isEnableBtnHistory">
                    <input *ngIf="getHistory('assets')" nz-input readonly value="{{ getHistory('assets') }}" />
                  </td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_FileBill || 'HĐ mẫu/phiếu thu/biên lai' }}</td>
                  <td>
                    <a *ngIf="dataObject.fileBill" href="{{ dataObject.fileBill }}" target="_blank"
                      ><span nz-icon nzType="file-text"></span> {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}</a
                    >
                  </td>
                  <td *ngIf="isEnableBtnHistory"></td>
                </tr>

                <tr>
                  <td>{{ language_key?.Evaluation_BillInfo || 'Thông tin phát hành HĐ' }}</td>
                  <td>
                    <a *ngIf="dataObject.fileInfoBill" href="{{ dataObject.fileInfoBill }}" target="_blank"
                      ><span nz-icon nzType="file-text"></span> {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}</a
                    >
                  </td>
                  <td *ngIf="isEnableBtnHistory"></td>
                </tr>
              </tbody>
            </nz-table>
          </div>
          <div *ngIf="dataSuggest">
            <h4>{{ language_key?.Evaluation_InformationNote || 'Ghi chú thông tin pháp lý:' }}</h4>
            <div nz-col [nzSpan]="24" class="text-center">
              <textarea nz-input rows="2" readonly auto
                >{{ dataHistory.comment ? dataHistory.comment : '' }}
            </textarea
              >
            </div>
          </div>
        </nz-tab>
        <nz-tab [nzTitle]="language_key?.Evaluation_AccessInformation || 'Thông tin năng lực'">
          <h2>{{ service.serviceName }}</h2>
          <div *ngIf="dataCapacity">
            <nz-table
              nz-col
              nzSpan="24"
              class="mb-3"
              #expandTable
              [nzData]="dataCapacity"
              [(nzPageSize)]="pageSize"
              [nzLoading]="loading"
              [nzShowPagination]="false"
            >
              <thead>
                <tr class="text-wrap">
                  <th>{{ language_key?.Evaluation_Criteria || 'Tiêu chí' }}</th>
                  <th>{{ language_key?.Evaluation_Subcribed || 'Đã đăng ký' }}</th>
                  <th *ngIf="isEnableBtnHistory">{{ language_key?.Evaluation_EditRequest || 'Yêu cầu điều chỉnh' }}</th>
                  <th>{{ language_key?.Evaluation_Note || 'Ghi chú' }}</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let data of expandTable.data">
                  <tr>
                    <td class="mw-25">
                      <span>{{ data.name }}</span>
                    </td>
                    <td class="mw-25">
                      <div *ngIf="!data.isChangeByYear; else changeByYear1">
                        <input nz-input readonly *ngIf="data.type === dataType.String.code && data.value" value="{{ data.value }}" />
                        <input nz-input currencyMask readonly *ngIf="data.type === dataType.Number.code && data.value" value="{{ data.value }}" />
                        <input
                          nz-input
                          readonly
                          *ngIf="data.type === dataType.Date.code && data.value"
                          value="{{ data.value | date : 'dd/MM/yyyy' }}"
                        />
                        <input
                          nz-input
                          readonly
                          *ngIf="data.type === dataType.List.code && convertTypeList(data.__supplierCapacityListDetails__) !== ''"
                          value="{{ convertTypeList(data.__supplierCapacityListDetails__) }}"
                        />
                        <span *ngIf="data.type === dataType.File.code">
                          <a *ngIf="data.value" href="{{ data.value }}" target="_blank"
                            ><span nz-icon nzType="file-text"></span> {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}
                          </a>
                        </span>
                      </div>
                      <ng-template #changeByYear1>
                        <span nz-popover [nzPopoverContent]="contentTemplate"> {{ language_key?.Evaluation_Detail || 'Chi tiết' }} </span>
                        <ng-template #contentTemplate>
                          <div *ngFor="let itemYear of data.__supplierCapacityYearValue__">
                            <p *ngIf="data.type === dataType.String.code">
                              {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value }}
                            </p>
                            <p *ngIf="data.type === dataType.Number.code">
                              {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value | number }}
                            </p>
                            <p *ngIf="data.type === dataType.File.code">
                              <a href="{{ itemYear.value }}" target="_blank">
                                {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} -
                                {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}
                              </a>
                            </p>
                          </div>
                        </ng-template>
                      </ng-template>
                    </td>
                    <td class="mw-25" *ngIf="isEnableBtnHistory">
                      <div *ngIf="!data.isChangeByYear; else changeByYear2">
                        <input
                          nz-input
                          readonly
                          *ngIf="data.type === dataType.String.code && getHistoryCapacity(data)"
                          value="{{ getHistoryCapacity(data) }}"
                        />
                        <input
                          nz-input
                          currencyMask
                          readonly
                          *ngIf="data.type === dataType.Number.code && getHistoryCapacity(data)"
                          value="{{ getHistoryCapacity(data) }}"
                        />
                        <input
                          nz-input
                          readonly
                          *ngIf="data.type === dataType.Date.code && getHistoryCapacity(data)"
                          value="{{ getHistoryCapacity(data) | date : 'dd/MM/yyyy' }}"
                        />
                        <input
                          nz-input
                          readonly
                          *ngIf="data.type === dataType.List.code && getHistoryCapacity(data)"
                          value="{{ getHistoryCapacity(data) }}"
                        />
                        <span *ngIf="data.type === dataType.File.code">
                          <a *ngIf="getHistoryCapacity(data)" href="{{ getHistoryCapacity(data) }}" target="_blank"
                            ><span nz-icon nzType="file-text"></span> {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}</a
                          ></span
                        >
                      </div>
                      <ng-template #changeByYear2>
                        <div *ngIf="getYearHistory(data).length > 0">
                          <span nz-popover [nzPopoverContent]="contentTemplate"> {{ language_key?.Evaluation_Detail || 'Chi tiết' }} </span>
                          <ng-template #contentTemplate>
                            <div *ngFor="let itemYear of getYearHistory(data)">
                              <p *ngIf="data.type === dataType.String.code">
                                {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value }}
                              </p>
                              <p *ngIf="data.type === dataType.Number.code">
                                {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value | number }}
                              </p>
                              <p *ngIf="data.type === dataType.File.code">
                                <a href="{{ itemYear.value }}" target="_blank">
                                  {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} -
                                  {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}
                                </a>
                              </p>
                            </div>
                          </ng-template>
                        </div>
                      </ng-template>
                    </td>
                    <td>
                      <input nz-input readonly *ngIf="getComment(data)" value="{{ getComment(data) }}" />
                    </td>
                  </tr>
                  <ng-container>
                    <tr *ngFor="let item of data.__childs__">
                      <td class="mw-25" [nzIndentSize]="20">
                        <span>{{ item.name }}</span>
                      </td>
                      <td>
                        <div *ngIf="!item.isChangeByYear; else changeByYear1">
                          <input nz-input readonly *ngIf="item.type === dataType.String.code" value="{{ item.value }}" />
                          <input nz-input currencyMask readonly *ngIf="item.type === dataType.Number.code" value="{{ item.value }}" />
                          <input nz-input readonly *ngIf="item.type === dataType.Date.code" value="{{ item.value | date : 'dd/MM/yyyy' }}" />
                          <input
                            nz-input
                            readonly
                            *ngIf="item.type === dataType.List.code"
                            value="{{ convertTypeList(item.__supplierCapacityListDetails__) }}"
                          />
                          <span *ngIf="item.type === dataType.File.code">
                            <a *ngIf="item.value" href="{{ item.value }}" target="_blank"
                              ><span nz-icon nzType="file-text"></span> {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}
                            </a>
                          </span>
                        </div>
                        <ng-template #changeByYear1>
                          <span nz-popover [nzPopoverContent]="contentTemplateC"> {{ language_key?.Evaluation_Detail || 'Chi tiết' }} </span>
                          <ng-template #contentTemplateC>
                            <div *ngFor="let itemYear of item.__supplierCapacityYearValue__">
                              <p *ngIf="item.type === dataType.String.code">
                                {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value }}
                              </p>
                              <p *ngIf="item.type === dataType.Number.code">
                                {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value | number }}
                              </p>
                              <p *ngIf="item.type === dataType.File.code">
                                <a href="{{ itemYear.value }}" target="_blank">
                                  {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} -
                                  {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}
                                </a>
                              </p>
                            </div>
                          </ng-template>
                        </ng-template>
                      </td>
                      <td *ngIf="isEnableBtnHistory">
                        <div *ngIf="!item.isChangeByYear; else changeByYear2">
                          <input
                            nz-input
                            readonly
                            *ngIf="item.type === dataType.String.code && getHistoryCapacity(item)"
                            value="{{ getHistoryCapacity(item) }}"
                          />
                          <input
                            nz-input
                            currencyMask
                            readonly
                            *ngIf="item.type === dataType.Number.code && getHistoryCapacity(item)"
                            value="{{ getHistoryCapacity(item) }}"
                          />
                          <input
                            nz-input
                            readonly
                            *ngIf="item.type === dataType.Date.code && getHistoryCapacity(item)"
                            value="{{ getHistoryCapacity(item) | date : 'dd/MM/yyyy' }}"
                          />
                          <input
                            nz-input
                            readonly
                            *ngIf="item.type === dataType.List.code && getHistoryCapacity(item)"
                            value="{{ getHistoryCapacity(item) }}"
                          />
                          <span *ngIf="item.type === dataType.File.code">
                            <a *ngIf="getHistoryCapacity(item)" href="{{ getHistoryCapacity(item) }}" target="_blank">
                              <span nz-icon nzType="file-text"></span>{{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}
                            </a>
                          </span>
                        </div>
                        <ng-template #changeByYear2>
                          <div *ngIf="getYearHistoryChild(data, item).length > 0">
                            <span nz-popover [nzPopoverContent]="contentTemplateC"> {{ language_key?.Evaluation_Detail || 'Chi tiết' }} </span>
                            <ng-template #contentTemplateC>
                              <div *ngFor="let itemYear of getYearHistoryChild(data, item)">
                                <p *ngIf="item.type === dataType.String.code">
                                  {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value }}
                                </p>
                                <p *ngIf="item.type === dataType.Number.code">
                                  {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} - {{ itemYear.value | number }}
                                </p>
                                <p *ngIf="item.type === dataType.File.code">
                                  <a href="{{ itemYear.value }}" target="_blank">
                                    {{ language_key?.Evaluation_Year || 'Năm' }} {{ itemYear.year }} -
                                    {{ language_key?.Evaluation_ViewAttachedFile || 'Xem file đính kèm' }}
                                  </a>
                                </p>
                              </div>
                            </ng-template>
                          </div>
                        </ng-template>
                      </td>
                      <td><input nz-input readonly *ngIf="getComment(item)" value="{{ getComment(item) }}" /></td>
                    </tr>
                  </ng-container>
                </ng-container>
              </tbody>
            </nz-table>
          </div>
          <div *ngIf="dataSuggest">
            <h4>{{ language_key?.Evaluation_AccessInformationNote || 'Ghi chú thông tin năng lực:' }}</h4>
            <div nz-col [nzSpan]="24" class="text-center">
              <textarea nz-input rows="2" readonly auto
                >{{ dataSuggest.commentCapacity ? dataSuggest.commentCapacity : '' }}
            </textarea
              >
            </div>
          </div>
        </nz-tab>
      </nz-tabset>
    </div>
  </div>
  <div class="accept-data">
    <button nz-button nzType="primary" (click)="acceptChangeData()">
      {{ language_key?.Evaluation_AgreeToUpdateInformation || 'Đồng ý cập nhật lại thông tin theo nội dung thẩm định' }}
    </button>
  </div>
</div>
