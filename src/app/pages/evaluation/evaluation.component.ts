import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ApiService, NotifyService, AuthenticationService } from '../../services'
import { User } from '../../models/user.model'
import { enumData } from '../../base/enumData'
import { LanguageService } from 'src/app/services/language.service'

@Component({ templateUrl: './evaluation.component.html', styleUrls: ['./evaluation.component.scss'] })
export class EvaluationComponent implements OnInit {
  supplierExpertiseId!: string
  user!: User
  notify!: string
  dataSuggest: any

  dictSupplierExpertiseDetails: any = {}
  service: any = {
    serviceName: '',
  }

  // Thông tin pháp lý
  pageSize = enumData.Page.pageSizeMax
  loading = false
  dataObject: any = {}
  dataHistory: any = {}
  isEnableBtnHistory = false
  dataType = enumData.DataType
  language_key: any = {}
  // Thông tin năng lực
  dataCapacity: any
  dataHistoryCapacity: any = []
  isEnableBtnHistoryCapacity = true

  constructor(
    private route: ActivatedRoute,
    private apiService: ApiService,
    private notifyService: NotifyService,
    private authenticationService: AuthenticationService,
    private router: Router,
    private languageService: LanguageService
  ) {
    this.route.params.subscribe((params) => {
      this.supplierExpertiseId = params['supplierExpertiseId']
    })
  }

  ngOnInit() {
    setTimeout(this.checkIsYourExpertise, 100)
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  checkIsYourExpertise = async () => {
    this.user = this.authenticationService.currentUserValue
    if (!this.user) {
      this.notify = this.language_key?.Evaluation_NoAccessGrant || 'Bạn không có quyền truy cập!'
      return
    }
    if (!this.supplierExpertiseId) {
      this.notify = this.language_key?.Evaluation_NotDetermineAppraisal || 'Không xác định lần thẩm định!'
      return
    }

    const res = await this.apiService.post(this.apiService.CLIENT_WEB.CHECK_IS_YOUR_EXPERTISE, { supplierExpertiseId: this.supplierExpertiseId })
    if (!res.isSupplierExpertise) {
      this.notify = this.language_key?.Evaluation_NoAccessGrant || 'Bạn không có quyền truy cập, vui lòng kiểm tra lại!'
      return
    }

    this.dataSuggest = await this.apiService.post(this.apiService.CLIENT_WEB.GET_DATA_SUGGEST, { supplierExpertiseId: this.supplierExpertiseId })

    this.mapData(this.dataSuggest)
  }

  getHistory(param: string | number) {
    let result = ''
    if (this.dataHistory && typeof this.dataHistory[param] !== 'undefined' && this.dataHistory[param] !== null) {
      if (this.dataObject[param] !== this.dataHistory[param]) {
        result = `${this.dataHistory[param]}`
      }
    }
    return result
  }

  checkIsEnableBtnHistory() {
    this.isEnableBtnHistory = !this.compareObject(this.dataHistory, this.dataObject)
  }

  public compareObject(obj1: any, obj2: any) {
    for (const key in obj1) {
      if (!obj2[key]) return false
      if (obj1[key] !== obj2[key]) return false
    }
    return true
  }

  mapData = (data: any) => {
    this.dataObject = data.__supplier__

    this.dataHistory = data.__supplierExpertiseLawDetails__[0]
    this.checkIsEnableBtnHistory()

    this.service.serviceName = data.__service__.name
    this.dataCapacity = data.__supplierService__.__capacities__
      .filter((p: any) => p.parentId === null)
      .sort((a: any, b: any) => parseFloat(a.sort) - parseFloat(b.sort))
    this.dataHistoryCapacity = data.__supplierExpertiseDetails__
  }

  //#region  capacity

  convertTypeList(list: any[]) {
    if (typeof list !== 'undefined') {
      const data = list.find((p: any) => p.isChosen)
      if (data) return data.name
    }

    return ''
  }

  convertTypeListHistory(list: any[], chosenId: any) {
    if (typeof list !== 'undefined') {
      const data = list.find((p: any) => p.id === chosenId)
      if (data) return data.name
    }

    return ''
  }

  getHistoryCapacity(obj: any) {
    let result = ''
    const findData = this.dataHistoryCapacity.find((p: any) => p.supplierCapacityId === obj.id)
    if (findData) {
      if (!obj.isChangeByYear) {
        if (obj.value !== findData.value) {
          if (obj.type === this.dataType.List.code) {
            result = this.convertTypeListHistory(obj.__supplierCapacityListDetails__, findData.value)
          } else {
            result = findData.value
          }
        }
      }
    }
    return result
  }

  getYearHistory(obj: any) {
    const findData = this.dataHistoryCapacity.find((p: any) => p.supplierCapacityId === obj.id)
    if (findData) {
      if (obj.isChangeByYear) {
        const check = this.checkDifferentArrayYear(obj.__supplierCapacityYearValue__, findData.__supplierCapacityYearValue__)
        if (check) {
          return findData.__supplierCapacityYearValue__
        }
      }
    }
    return []
  }

  getYearHistoryChild(obj: any, child: any) {
    const findData = this.dataHistoryCapacity.find((p: any) => p.supplierCapacityId === obj.id)
    if (findData && findData.__childs__) {
      const findDataChild = findData.__childs__.find((p: any) => p.id === child.id)
      if (findDataChild) {
        if (child.isChangeByYear) {
          const check = this.checkDifferentArrayYear(child.__supplierCapacityYearValue__, findData.__supplierCapacityYearValue__)
          if (check) {
            return findDataChild.__supplierCapacityYearValue__
          }
        }
      }
    }
    return []
  }

  checkDifferentArrayYear(parent: any, child: any[]) {
    if (parent.length !== child.length) return true

    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < parent.length; i++) {
      const itemP = parent[i]
      const itemC = child.find((p: any) => p.year === itemP.year)
      if (!itemC) return true
      if (itemP.value !== itemC.value) return true
    }
    return false
  }

  getComment(obj: any) {
    const findData = this.dataHistoryCapacity.find((p: any) => p.supplierCapacityId === obj.id)
    return findData?.comment || ''
  }

  //#endregion

  acceptChangeData = () => {
    this.apiService.post(this.apiService.CLIENT_WEB.SUPPLIER_ACCEPT_CHANGE_DATA, { supplierExpertiseId: this.supplierExpertiseId }).then((res) => {
      this.notifyService.showSuccess(this.language_key?.Evaluation_UpdateDataSuccessfully || 'Đã cập nhật dữ liệu thành công!')
      this.router.navigate([`home`])
    })
  }
}
