import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/base/enumData'
import { AuthenticationService, ApiService, CoreService, StorageService, NotifyService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'
import $ from 'jquery'

@Component({
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.scss'],
})
export class PaymentComponent {
  language_key: any = {}
  loading = false
  listOfData: any[] = []
  isVisibleRefuse = false
  enumData: any
  maxSizeUpload = enumData.maxSizeUpload
  subscriptions: Subscription = new Subscription()
  dataSearch: any = {}
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  enumStatus: any
  dataObject: any
  dataPaymentStatus = this.coreService.convertObjToArray(enumData.PaymentStatus)
  enumDataStatus = enumData.PaymentStatus
  constructor(
    private authenticationService: AuthenticationService,
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    private notifyService: NotifyService,
    private languageService: LanguageService,
    private router: Router
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()

    this.searchData()
  }
  ngAfterViewInit() {
    $('.input-enter').on('keyup', (e) => {
      if (e.key === 'Enter' || e.keyCode === 13) {
        this.searchData()
      }
    })
  }

  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    if (clearFilter) this.dataSearch = {}
    if (reset) this.pageIndex = 1
    this.loading = true

    const dataSearch: any = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.PAYMENT.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd() {
    this.router.navigate(['/payment/add'])
  }

  showDetail(data: any) {
    this.router.navigate(['/payment/detail', data.id])
  }

  clickEdit(object: any) {
    this.router.navigate(['/payment/edit', object.id])
  }

  onDelete(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT.CANCEL, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }

  onSend(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT.SEND, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }

  onApproved(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT.APPROVED, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }

  onChecking(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT.CHECKING, data).then(() => {
      this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
      this.searchData()
    })
  }
}
