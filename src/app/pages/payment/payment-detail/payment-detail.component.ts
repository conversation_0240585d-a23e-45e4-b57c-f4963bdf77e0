import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Router, ActivatedRoute } from '@angular/router'
import { enumData } from 'src/app/base/enumData'
import { NotifyService, CoreService, StorageService, ApiService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './payment-detail.component.html',
})
export class PaymentDetailComponent {
  dataObject: any = {}
  isEditItem = false
  modalTitle = 'Chi tiết thanh toán'
  language_key: any
  lstCompany: any = []
  id: any
  lstPO: any = []
  lstContract: any = []
  lstBill: any = []

  constructor(
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private router: Router,
    private route: ActivatedRoute,
    private languageService: LanguageService
  ) {}

  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.loadAllDataSelect()
    this.id = this.route.snapshot.paramMap.get('id')
    this.onLoadDetail(this.id)
  }

  async loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.BILL.FIND, {
        paymentStatus: enumData.BillPaymentStatus.NEW.code,
        status: enumData.BillStatus.CONFIRMED.code,
      }),
      this.apiService.post(this.apiService.BILL.LOAD_PO, {}),
      this.apiService.post(this.apiService.BILL.LOAD_CONTACT, {}),
    ]).then(async (res) => {
      this.lstBill = res[0]
      this.lstPO = res[1]
      this.lstContract = res[2]
    })
    this.notifyService.hideloading()
  }

  onLoadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT.FIND_DETAIL, { id: id }).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.dataObject = res
      }
    })
  }

  onBack() {
    this.router.navigate(['/payment'])
  }

  downloadFileFromS3(url: string) {
    window.open(url, '_blank')
  }
}
