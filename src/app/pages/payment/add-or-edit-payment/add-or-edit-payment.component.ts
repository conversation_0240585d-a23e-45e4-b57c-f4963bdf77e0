import { Component } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/base/enumData'
import { ApiService, NotifyService, CoreService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './add-or-edit-payment.component.html',
})
export class AddOrEditPaymentComponent {
  dataObject: any = {}
  isEditItem = false
  maxSizeUpload = enumData.maxSizeUpload
  lstPO: any = []
  lstContract: any = []
  lstBill: any = []

  lstCurrency: any = []
  id: any
  modalTitle = 'Tạo mới thanh toán'
  uploadUrl: string = this.apiService.UploadUrl
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private router: Router,
    private languageService: LanguageService,
    public coreService: CoreService,
    private activatedRoute: ActivatedRoute
  ) {}
  language_key: any
  ngOnInit() {
    this.loadAllDataSelect()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.id = this.activatedRoute.snapshot.paramMap.get('id')
    if (this.id) {
      this.isEditItem = true
      this.modalTitle = 'Chỉnh sửa thanh toán'
      this.onLoadDetail(this.id)
    }
  }

  onLoadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PAYMENT.FIND_DETAIL, { id: id }).then((res: any) => {
      if (res) {
        this.notifyService.hideloading()
        this.dataObject = res
      }
    })
  }

  async loadAllDataSelect() {
    this.notifyService.showloading()
    Promise.all([
      this.apiService.post(this.apiService.BILL.FIND_BILL, {
        // paymentStatus: enumData.BillPaymentStatus.NEW.code,
        // status: enumData.BillStatus.CONFIRMED.code,
      }),
      this.apiService.post(this.apiService.BILL.LOAD_PO, {}),
      this.apiService.post(this.apiService.BILL.LOAD_CONTACT, {}),
    ]).then(async (res) => {
      this.lstBill = res[0]
      this.lstPO = res[1]
      this.lstContract = res[2]
    })
    this.notifyService.hideloading()
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload && fileToUpload.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(
        `${this.language_key?.Button_MaximumSizeToUpload || 'Kích thước tối đa để upload là'} ${this.maxSizeUpload}MB, ${
          this.language_key?.Button_ChooseAnotherFile || 'vui lòng chọn file khác'
        }`
      )
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.dataObject[fieldName] = res[0]
        else this.dataObject[fieldName] = ''
      })
    }
  }

  closeDialog() {
    this.router.navigate(['/payment'])
  }

  onSave() {
    this.notifyService.showloading()
    if (this.dataObject.id && this.dataObject.id !== '') {
      this.updateObject()
      return
    }
    this.addObject()
  }

  addObject() {
    this.apiService.post(this.apiService.PAYMENT.CREATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      }
    })
  }

  updateObject() {
    this.apiService.post(this.apiService.PAYMENT.UPDATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog()
      }
    })
  }

  onChangeBill(billIds: any) {
    Promise.all([
      this.apiService.post(this.apiService.BILL.FIND_PO, {
        listBillId: billIds,
      }),
      this.apiService.post(this.apiService.BILL.FIND_CONTRACT, {
        listBillId: billIds,
      }),
    ]).then(([listPO, listContract]) => {
      this.dataObject.poIds = listPO
      this.dataObject.contractIds = listContract
    })
  }

  backToPayment() {
    this.router.navigate(['/payment'])
  }
}
