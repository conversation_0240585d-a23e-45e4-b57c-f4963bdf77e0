<nz-row>
  <nz-col nzSpan="24" class="text-center fs-24 fw-600">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<form nz-form #frmAdd="ngForm">
  <nz-row nzGutter="32  ">
    <!-- Mã hóa đơn -->
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.Payment_PaymentCode || 'Mã hồ sơ thanh toán' }}</nz-form-label>
        <nz-form-control nzSpan="24">
          <input disabled nz-input [(ngModel)]="dataObject.code" name="code" required pattern=".{1,100}" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Tiêu đề-->
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.Payment_PaymentName || 'Ti<PERSON>u đề hồ sơ thanh toán' }}</nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Payment_PleaseEnterPaymentsProfile || 'Vui lòng nhập tiêu đề hồ sơ thanh toán!'">
          <input
            nz-input
            [placeholder]="language_key?.Payment_Enter1To250Characters || 'Nhập 1-250 kí tự'"
            [(ngModel)]="dataObject.name"
            name="name"
            required
            pattern=".{1,250}"
            autocomplete="off"
          />
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>
  <nz-row nzGutter="32">
    <!-- Danh sách hóa đơn -->
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.Payment_Bill || 'Hóa đơn' }}</nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Payment_PleaseSelectBill || 'Vui lòng chọn danh sách hóa đơn !'">
          <nz-select
            nzMode="multiple"
            required
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataObject.billIds"
            name="billIds"
            [nzPlaceHolder]="language_key?.Payment_SelectBill || 'Chọn danh sách hóa đơn'"
            (ngModelChange)="onChangeBill($event)"
          >
            <nz-option *ngFor="let item of lstBill" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Po -->
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">{{ language_key?.Payment_Po || 'PO' }}</nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Payment_PleaseSelectPo || 'Vui lòng chọn PO!'">
          <nz-select disabled nzMode="multiple" nzShowSearch nzAllowClear [(ngModel)]="dataObject.poIds" name="poIds" nzPlaceHolder="PO">
            <nz-option *ngFor="let item of lstPO" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Hợp đồng -->
    <nz-col nzSpan="8">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">{{ language_key?.Payment_Contract || 'Hợp đồng' }}</nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Payment_PleaseSelectContract || 'Vui lòng chọn hợp đồng !'" nzErrorTip="">
          <nz-select
            disabled
            nzMode="multiple"
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataObject.contractIds"
            name="contractIds"
            [nzPlaceHolder]="language_key?.Payment_SelectContract || 'Chọn hợp đồng'"
          >
            <nz-option *ngFor="let item of lstContract" [nzLabel]="item.name" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="fileAttach" nzRequired class="text-left"
          >{{ language_key?.Payment_AttachedFiles || 'File đính kèm' }}
        </nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Payment_PleaseAttachedFiles || 'Vui lòng upload file đính kèm'">
          <label for="fileAttach" class="custom-file-upload">
            <span nz-icon nzType="upload"></span> {{ language_key?.Button_UploadFile || 'Upload File' }}
          </label>
          <input
            class="hidden"
            type="file"
            id="fileAttach"
            [(ngModel)]="dataObject.fileAttach"
            name="fileAttach"
            (change)="handleFileInput($event, 'fileAttach')"
          />
          <div class="tooltip" *ngIf="dataObject.fileAttach && dataObject.fileAttach.length > 0">
            <a href="{{ dataObject.fileAttach }}" target="_blank"> {{ language_key?.Button_ViewFile || 'Xem file' }} </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="filePaymentRequest" nzRequired class="text-left"
          >{{ language_key?.Payment_PaymentRequestFile || 'File đề nghị thanh toán' }}
        </nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Payment_PleaseUploadPaymentRequestFile || 'Vui lòng upload file đề nghị thanh toán'">
          <label for="filePaymentRequest" class="custom-file-upload">
            <span nz-icon nzType="upload"></span> {{ language_key?.Button_UploadFile || 'Upload File' }}
          </label>
          <input
            class="hidden"
            type="file"
            id="filePaymentRequest"
            [(ngModel)]="dataObject.filePaymentRequest"
            name="filePaymentRequest"
            (change)="handleFileInput($event, 'filePaymentRequest')"
          />
          <div class="tooltip" *ngIf="dataObject.filePaymentRequest && dataObject.filePaymentRequest.length > 0">
            <a href="{{ dataObject.filePaymentRequest }}" target="_blank"> {{ language_key?.Button_ViewFile || 'Xem file' }} </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="8">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="fileAcceptanceReport" nzRequired class="text-left"
          >{{ language_key?.Payment_AcceptanceRecordFile || 'File biên bản nghiệm thu' }}
        </nz-form-label>
        <nz-form-control
          nzSpan="24"
          [nzErrorTip]="language_key?.Payment_PleaseUploadAcceptanceRecordFile || 'Vui lòng upload Giấy phép kinh doanh có điều kiện'"
        >
          <label for="fileAcceptanceReport" class="custom-file-upload">
            <span nz-icon nzType="upload"></span> {{ language_key?.Button_UploadFile || 'Upload File' }}
          </label>
          <input
            class="hidden"
            type="file"
            id="fileAcceptanceReport"
            [(ngModel)]="dataObject.fileAcceptanceReport"
            name="fileAcceptanceReport"
            (change)="handleFileInput($event, 'fileAcceptanceReport')"
          />
          <div class="tooltip" *ngIf="dataObject.fileAcceptanceReport && dataObject.fileAcceptanceReport.length > 0">
            <a href="{{ dataObject.fileAcceptanceReport }}" target="_blank">
              {{ language_key?.Button_ViewFile || 'Xem file' }}
            </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Ghi chú -->
    <nz-col [nzSpan]="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" class="text-left">{{ language_key?.Payment_Note || 'Ghi chú' }}</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea nz-input [placeholder]="language_key?.Payment_EnterNote || 'Nhập ghi chú'" [(ngModel)]="dataObject.note" name="note" rows="5">
          </textarea>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-row class="mt-2">
    <nz-col nzSpan="24" class="text-center">
      <button (click)="closeDialog()" nz-button class="mr-4 button-exit">
        <span nz-icon nzType="close"></span>{{ language_key?.Button_Exit || 'Thoát' }}
      </button>

      <button
        class="mr-4 button-save"
        nz-button
        [disabled]="!frmAdd.form.valid || !dataObject.fileAttach || !dataObject.filePaymentRequest || !dataObject.fileAcceptanceReport"
        nzType="primary"
        (click)="onSave()"
      >
        <span nz-icon nzType="save"></span>{{ language_key?.Button_Save || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>
