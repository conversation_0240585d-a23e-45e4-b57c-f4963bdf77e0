<nz-row class="mt-3">
  <h2>{{ language_key?.Payment_ListPayment || 'DANH SÁCH HỒ SƠ THANH TOÁN' }}</h2>
</nz-row>

<nz-collapse>
  <nz-collapse-panel [nzHeader]="language_key?.Button_Search || 'Tìm kiếm'">
    <nz-row class="mt-3" nzGutter="32">
      <nz-col nzSpan="12">
        <input
          nz-input
          [(ngModel)]="dataSearch.code"
          name="code"
          [placeholder]="language_key?.Payment_SearchPaymentsProfileCode || 'Tìm theo mã hồ sơ thanh toán'"
          class="input-enter"
        />
      </nz-col>

      <nz-col nzSpan="12">
        <nz-select
          nzShowSearch
          nzAllowClear
          [(ngModel)]="dataSearch.status"
          name="status"
          [nzPlaceHolder]="language_key?.Payment_SelectPaymentStatus || 'Chọn trạng thái thanh toán'"
        >
          <nz-option *ngFor="let item of dataPaymentStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
        </nz-select>
      </nz-col>

      <nz-col nzSpan="24" class="text-center mt-4">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true, true)" class="mr-2">
          <span nz-icon nzType="redo"></span> {{ language_key?.Button_ClearSearch || 'Xóa bộ lọc' }}
        </button>
        <button nzShape="round" nz-button (click)="searchData(true)" nzType="primary" nzGhost>
          <span nz-icon nzType="search"></span>{{ language_key?.Button_Search || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3" nzJustify="space-between" nzAlign="middle">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" nzGhost (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus"></span>
      {{ language_key?.Button_Add || 'Thêm mới' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th nzWidth="150px">{{ language_key?.Button_STT || 'STT' }}</th>
        <th>{{ language_key?.Payment_PaymentCode || 'Mã hồ sơ thanh toán' }}</th>
        <th>{{ language_key?.Payment_PaymentName || 'Tiêu đề hồ sơ thanh toán' }}</th>
        <th>{{ language_key?.Bidding_Status || 'Trạng thái' }}</th>
        <th nzWidth="150px" nzRight>{{ language_key?.Column_Action || 'Tác vụ' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData; let i = index">
        <td>{{ i + 1 }}</td>

        <td>{{ data.code }}</td>
        <td>{{ data.name }}</td>

        <td class="text-center">
          <nz-tag
            [ngStyle]="{
              color: data.statusColor,
              border: '1px solid ' + data.statusBorderColor
            }"
            [nzColor]="data.statusBgColor"
            style="width: 180px; font-weight: 600; border-radius: 30px"
          >
            <div style="display: flex; align-items: center; justify-content: center">
              <div
                [ngStyle]="{
                  background: data.statusColor,
                }"
                class="dot"
              ></div>
              <span class="ml-1"> {{ data.statusName }}</span>
            </div>
          </nz-tag>
        </td>

        <td class="text-center">
          <button
            nz-tooltip
            (click)="showDetail(data)"
            [nzTooltipTitle]="language_key?.Payment_ViewDetail || 'Xem chi tiết'"
            class="btn-primary mb-2 mt-2 mr-1"
            nz-button
          >
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="
              data.status === enumDataStatus.NEW.code ||
              data.status === enumDataStatus.REQUEST_RECHECK.code ||
              data.status === enumDataStatus.REQUEST_CONFIRM.code
            "
            nz-tooltip
            (click)="clickEdit(data)"
            [nzTooltipTitle]="language_key?.Payment_Update || 'Chỉnh sửa'"
            class="btn-primary mb-2 mt-2 mr-1"
            nz-button
          >
            <span nz-icon nzType="edit"></span>
          </button>

          <button
            *ngIf="data.status === enumDataStatus.NEW.code || data.status === enumDataStatus.REQUEST_RECHECK.code"
            class="mr-1 width-button"
            (nzOnConfirm)="onChecking(data)"
            nz-tooltip
            nzShape="circle"
            [nzTooltipTitle]="language_key?.Payment_CheckProfile || 'Kiểm tra hồ sơ'"
            nz-button
            nz-popconfirm
            [nzPopconfirmTitle]="language_key?.Payment_SureCheckProfile || 'Bạn có chắc kiểm tra hồ sơ ?'"
            nzPopconfirmPlacement="bottom"
          >
            <i nz-icon nzType="check" class="text-icon"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="text-center">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>
