import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, NotifyService } from 'src/app/services'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ActivatedRoute, Params } from '@angular/router'
import { LanguageService } from 'src/app/services/language.service'
@Component({ templateUrl: './faq.component.html' })
export class FaqComponent implements OnInit {
  dataObject: any
  language_key: any = {}
  id: any
  constructor(
    private route: ActivatedRoute,
    private notifyService: NotifyService,
    private apiService: ApiService,
    private languageService: LanguageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.id = params['id']
    })
  }

  title = this.language_key?.FAQ_Title || 'Tiêu đề FAQ'
  description = this.language_key?.FAQ_Content || 'Nội dung FAQ'

  ngOnInit() {
    this.loadFAQ()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  loadFAQ() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.CLIENT_WEB.GET_FAQ(this.id), {}).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
      this.title = this.dataObject.title
      this.description = this.dataObject.description
    })
  }
}
