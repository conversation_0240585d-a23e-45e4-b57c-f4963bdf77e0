<!-- title -->
<nz-row>
  <nz-col nzSpan="24" class="text-center fs-24 fw-600 mt-4">
    {{ language_key?.Button_ViewDetail || 'Xem chi tiết' | uppercase }}
  </nz-col>
</nz-row>

<!-- tab thông tin chi tiết inbound-->
<form nz-form #frmAdd="ngForm" class="form-deco-full">
  <nz-row nzGutter="8">
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.IB_Code || 'Mã inbound' }}</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.code }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.IB_Title || 'Tiêu đề inbound' }}</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.name }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" nzRequired>{{ language_key?.IB_Po || 'PO' }}</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.poNumber }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" nzRequired>{{ language_key?.Reference_Number || 'Chứng từ tham chiếu' }}</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.referenceCode }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.IB_Delivery || 'Ngày giao hàng' }}</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24">
          {{ dataObject.deliveryDate | date : 'dd/MM/yyyy: hh:mm' }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Người phụ trách -->
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{
          language_key?.IB_Date_Arrival_Port || 'Thời gian dự kiến về cảng'
        }}</nz-form-label>
        <nz-form-control class="input-color">
          {{ dataObject.dateArrivalPort | date : 'dd/MM/yyyy: hh:mm' }}
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="'Danh sách hàng hóa'" [nzActive]="true">
      <!-- DS Items -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstProduct.length > 0 ? [''] : []" class="mb-3" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Mã vật tư</th>
                <th>Vật tư</th>
                <th>Tên hàng hóa</th>
                <th>Mô tả hàng hóa</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Thành tiền</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstProduct; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ data.itemCode }}</td>
                <td>{{ data.serviceName }}</td>
                <td>{{ data.name }}</td>
                <td>{{ data.description }}</td>
                <td>{{ data.unit }}</td>
                <td>{{ data.quantity | number }}</td>
                <td>{{ data.price | number }}</td>
                <td>{{ data.money | number }}</td>
                <td>{{ data.note }}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="language_key?.Container_List || 'Danh sách Container'" [nzActive]="true">
      <!-- DS Container -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="['']" [nzShowPagination]="false" #ajaxTable nzBordered>
            <thead>
              <tr>
                <th>
                  {{ language_key?.Button_STT || 'STT' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_ContNumber || 'Số Cont' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_ShippingSealNumber || 'Số seal hãng tàu' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_SealNumber || 'Số seal' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_ContType || 'Loại cont' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_PackagesContNumber || 'Số kiện trên cont' }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let containerItem of dataObject.listContainer; let idx = index">
                <td>{{ idx + 1 }}</td>
                <td>
                  {{ containerItem.containerNumber }}
                </td>
                <td>
                  {{ containerItem.sealNumber }}
                </td>
                <td>
                  {{ containerItem.sealNumber }}
                </td>
                <td>
                  {{ containerItem.containerType }}
                </td>
                <td>
                  {{ containerItem.packageQuantity }}
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
  <!-- form action -->
  <nz-row matDialogActions class="mt-5">
    <nz-col nzSpan="24" class="text-center">
      <button (click)="closeDialog()" nz-button class="mr-4 button-exit">
        <span nz-icon nzType="close"></span>{{ language_key?.Button_Exit || 'Thoát' }}
      </button>
    </nz-col>
  </nz-row>
</form>
