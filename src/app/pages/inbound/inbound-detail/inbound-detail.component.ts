import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/base/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './inbound-detail.component.html',
  styleUrls: ['./inbound-detail.component.scss'],
})
export class InboundDetailComponent {
  passwordVisible = false
  passwordVisible2 = false
  validateForm: any
  dataObject: any = {}
  listData = []

  maxSizeUpload = enumData.maxSizeUpload
  isVisiblePopupChooseAddress = false
  isLoadCountry = false
  isLoadRegion = false
  dataCountry: any[] = []
  dataContract: any[] = []
  dataPO: any[] = []
  dataEmployeeIncharge: any[] = []
  dataExpectWarehouse: any[] = []
  dataDistrict: any[] = []
  dataWard: any[] = []
  dataFactorySupplier: any = {}
  regionId!: string
  districtId!: string
  wardId!: string
  address!: string
  fieldCurrent: any
  language_key: any = {}
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumData: any
  enumProject: any
  enumRole: any

  action: any
  lst: any[] = []

  formatterNull = (value: any) => `${value ? value : ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // Parser chặn nhập chữ
  parser = (value: string) => value.replace(/[^0-9]/g, '')

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private route: ActivatedRoute,
    private router: Router,
    private storageService: StorageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService,
    private languageService: LanguageService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  async ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()

    this.dataObject.isVietNam = true
    this.dataObject.lstProduct = []
    this.dataObject = { ...history.state.data }
    this.loadDetail(this.dataObject.id)
  }

  loadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.INBOUND_CLIENT.LOAD_DETAIL, { id: id }).then((result) => {
      this.notifyService.hideloading()
      this.dataObject = result
    })
  }

  submitForm() {
    this.notifyService.showloading()
    if (!this.dataObject.id) {
      this.apiService.post(this.apiService.INBOUND_CLIENT.CREATE_DATA, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      })
    } else {
      this.apiService.post(this.apiService.INBOUND_CLIENT.UPDATE_DATA, this.dataObject).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog()
      })
    }
    //#endregion
  }

  closeDialog() {
    this.router.navigate(['/inbound'])
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.dataObject[fieldName] = res[0]
        else this.dataObject[fieldName] = ''
      })
    }
  }
}
