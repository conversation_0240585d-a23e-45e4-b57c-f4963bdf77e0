<nz-row class="mt-3">
  <h2>{{ language_key?.Inbound_List || 'DANH SÁCH THÔNG BÁO GIAO HÀNG' | uppercase }}</h2>
</nz-row>

<nz-collapse class="mt-3">
  <nz-collapse-panel [nzHeader]="language_key?.Button_Search || 'Tìm kiếm'">
    <nz-row nzGutter="8">
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> {{ language_key?.Inbound_PMS_Number || 'Số IB PMS' }} </nz-form-label>
          <input nz-input [placeholder]="language_key?.Inbound_PMS_Number || 'Số IB PMS'" [(ngModel)]="dataSearch.code" name="code" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">{{ language_key?.PurchaseOrder_POCode || 'Số PO' }}</nz-form-label>
          <input nz-input [placeholder]="language_key?.PurchaseOrder_POCode || 'Số PO'" [(ngModel)]="dataSearch.poCode" name="poCode" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> {{ language_key?.Shipment_Number || 'Số shipment' }} </nz-form-label>
          <input nz-input [placeholder]="language_key?.Shipment_Number || 'Số shipment'" [(ngModel)]="dataSearch.shipmentCode" name="shipmentCode" />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> {{ language_key?.Shipment_Cost_Number || 'Số shipment cost' }} </nz-form-label>
          <input
            nz-input
            [placeholder]="language_key?.Shipment_Cost_Number || 'Số shipment cost'"
            [(ngModel)]="dataSearch.shipmentCostCode"
            name="shipmentCostCode"
          />
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">
            {{ language_key?.STATUS || 'Trạng thái' }}
          </nz-form-label>
          <nz-select nzShowSearch nzAllowClear [(ngModel)]="dataSearch.status" name="status" [nzPlaceHolder]="language_key?.STATUS || 'Trạng thái'">
            <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
          </nz-select>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24"> {{ language_key?.IB_Delivery_From || 'Ngày giao hàng - Từ ngày' }} </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker
              nzFormat="dd-MM-yyyy"
              [(ngModel)]="dataSearch.deliveryDateFrom"
              [nzPlaceHolder]="language_key?.Choose_FromDate || 'Nhập Từ ngày'"
            >
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">{{ language_key?.IB_Delivery_To || 'Ngày giao hàng - Đến ngày' }} </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker
              nzFormat="dd-MM-yyyy"
              [(ngModel)]="dataSearch.deliveryDateTo"
              [nzPlaceHolder]="language_key?.Choose_ToDate || 'Nhập Đến ngày'"
            >
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">{{ language_key?.IB_CreatedAt_From || 'Ngày tạo - Từ ngày' }} </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker
              nzFormat="dd-MM-yyyy"
              [(ngModel)]="dataSearch.createdAtFrom"
              [nzPlaceHolder]="language_key?.Choose_FromDate || 'Nhập Từ ngày'"
            >
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
      <nz-col nzSpan="6">
        <nz-form-item>
          <nz-form-label class="text-left" nzSpan="24">{{ language_key?.IB_CreatedAt_To || 'Ngày tạo - Đến ngày' }} </nz-form-label>
          <nz-form-control nzSpan="24">
            <nz-date-picker
              nzFormat="dd-MM-yyyy"
              [(ngModel)]="dataSearch.createdAtTo"
              [nzPlaceHolder]="language_key?.Choose_ToDate || 'Nhập Đến ngày'"
            >
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24" class="text-center">
        <button nzShape="round" nz-button nzType="primary" nzGhost (click)="searchData(true, true)" class="mr-2">
          <span nz-icon nzType="redo"></span>{{ language_key?.Button_ClearSearch || 'Xóa bộ lọc' }}
        </button>
        <button nzShape="round" nz-button (click)="searchData(true)" nzType="primary" nzGhost>
          <span nz-icon nzType="search"></span>{{ language_key?.Button_Search || 'Tìm kiếm' }}
        </button>
      </nz-col>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-col nzSpan="24">
    <button nz-button nzType="primary" nzGhost (click)="clickAdd()" class="mr-2">
      <span nz-icon nzType="plus"></span>
      {{ language_key?.Button_Add || 'Thêm mới' }}
    </button>

    <button nz-button nzType="primary" nzGhost class="mr-2" (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>
      {{ language_key?.Button_DownloadExcel || 'Tải excel' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    #basicTable
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    [nzScroll]="{ x: '3000px' }"
    nzTableLayout="fixed"
  >
    <thead>
      <tr class="text-nowrap">
        <th nzLeft nzWidth="130px">{{ 'Mã' }}</th>
        <th nzWidth="120px">{{ language_key?.PurchaseOrder_POCode || 'Số PO' }}</th>
        <th nzWidth="180px">{{ language_key?.Search_Supplier || 'Nhà cung cấp' }}</th>
        <th nzWidth="150px">{{ language_key?.Purchase_Request || 'PR' }}</th>
        <th nzBreakWord nzWidth="160px">{{ language_key?.IB_Delivery || 'Ngày giao hàng' }}</th>
        <th nzBreakWord nzWidth="160px">{{ language_key?.IB_Date_Arrival_Warehouse || 'Ngày về kho dự kiến' }}</th>
        <th nzBreakWord nzWidth="160px">{{ language_key?.IB_Date_Arrival_Port || 'Thời gian dự kiến về cảng' }}</th>
        <th nzWidth="180px">{{ language_key?.CreatedAt_Date || 'Ngày tạo' }}</th>
        <th nzWidth="180px">{{ language_key?.Creator || 'Người tạo' }}</th>
        <th nzWidth="180px">{{ language_key?.IB_Person_Charge || 'Người phụ trách' }}</th>
        <th class="text-center" nzWidth="200px">{{ language_key?.Button_Status || 'Trạng thái' }}</th>
        <th nzRight nzWidth="100px">{{ language_key?.Button_Action || 'Tác vụ' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td nzLeft>{{ data.code }}</td>
        <td>{{ data.poNumber }}</td>
        <td>{{ data.supplierName }}</td>
        <td>{{ data.prCode }}</td>
        <td>{{ data.deliveryDate | date : 'dd/MM/yyyy hh:mm' }}</td>
        <td>{{ data.dateArrivalWarehouse | date : 'dd/MM/yyyy hh:mm' }}</td>
        <td>{{ data.dateArrivalPort | date : 'dd/MM/yyyy hh:mm' }}</td>

        <td nzAlign="center">{{ data.createdAt | date : 'dd/MM/yyyy hh:mm' }}</td>
        <td>{{ data.createdByName || data.supplierName }}</td>

        <td>{{ data.employeeInchargeName }}</td>
        <td nzAlign="center">
          <nz-tag class="status-tag" [ngStyle]="data.statusStyle" [nzColor]="data.tagStatusColor">
            <div class="status-tag__container">
              <span class="ml-1">{{ data.statusName }}</span>
            </div>
          </nz-tag>
        </td>
        <td class="text-nowrap text-center" nzRight>
          <button
            nz-button
            nzShape="circle"
            (click)="viewDetailInfo(data)"
            class="mr-2 mt-2 btn-primary"
            [nzTooltipTitle]="language_key?.Button_ViewDetail || 'Xem chi tiết'"
            nzTooltipPlacement="top"
            nz-tooltip
            nzType="primary"
            nzGhost
          >
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="data.isSupplierCreate && data.status === enumData.InboundStatus.NEW.code"
            nz-button
            nzShape="circle"
            class="mt-1 text-btn"
            [nzTooltipTitle]="language_key?.Button_Edit || 'Chỉnh sửa'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="clickEdit(data)"
          >
            <span nz-icon nzType="edit"></span>
          </button>

          <!-- Huỷ -->
          <button
            *ngIf="data.isSupplierCreate && data.status === enumData.InboundStatus.NEW.code"
            nz-button
            nzShape="circle"
            class="mt-1 text-btn"
            [nzTooltipTitle]="language_key?.Cancel_Text || 'Huỷ'"
            nzTooltipPlacement="top"
            nz-tooltip
            (click)="destroy(data)"
          >
            <span nz-icon nzType="play-circle"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-row>

<nz-row>
  <nz-col nzSpan="24" class="text-center">
    <nz-pagination
      [nzTotal]="total"
      [(nzPageIndex)]="pageIndex"
      [(nzPageSize)]="pageSize"
      (nzPageIndexChange)="this.searchData()"
      (nzPageSizeChange)="this.searchData(true)"
      [nzShowTotal]="rangeTemplate"
      nzShowSizeChanger
    >
    </nz-pagination>
    <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} </ng-template>
  </nz-col>
</nz-row>
