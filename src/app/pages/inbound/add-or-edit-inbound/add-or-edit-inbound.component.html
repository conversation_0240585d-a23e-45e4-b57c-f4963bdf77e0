<!-- title -->
<nz-row>
  <nz-col nzSpan="24" class="text-center fs-24 fw-600 mt-4">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>

<form nz-form #frmAdd="ngForm" class="form-deco-full">
  <nz-row nzGutter="32">
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left">{{ language_key?.IB_Code || 'Mã ' }}</nz-form-label>
        <nz-form-control class="input-color" nzSpan="24" [nzErrorTip]="language_key?.Error_IB_Code || 'Vui lòng nhập mã !'">
          <input nz-input name="code" [(ngModel)]="dataObject.code" required disabled />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Ti<PERSON>u đề  -->
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.IB_Title || 'Tên ' }} </nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Error_IB_Title || 'Vui lòng nhập tên !'">
          <input nz-input name="name" [(ngModel)]="dataObject.name" required />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24" nzRequired>{{ language_key?.IB_Po || 'PO' }}</nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Error_IB_Po || 'Vui lòng chọn PO!'">
          <nz-select
            nzShowSearch
            nzAllowClear
            [(ngModel)]="dataObject.poId"
            name="poId"
            [nzPlaceHolder]="language_key?.IB_SelectPo || 'Chọn PO'"
            required
            (ngModelChange)="onChangePO($event)"
          >
            <nz-option *ngFor="let item of dataPO" [nzLabel]="item.code" [nzValue]="item.id"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <!-- Ngày giao hàng -->
    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired>{{ language_key?.IB_Delivery || 'Ngày giao hàng' }} </nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Error_IB_Delivery || 'Vui lòng nhập ngày giao hàng!'">
          <nz-date-picker
            nzFormat="dd-MM-yyyy"
            [(ngModel)]="dataObject.deliveryDate"
            [nzPlaceHolder]="language_key?.IB_Delivery || 'Ngày giao hàng'"
            name="deliveryDate"
            required
          >
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="6">
      <nz-form-item>
        <nz-form-label nzSpan="24" class="text-left" nzRequired
          >{{ language_key?.IB_Date_Arrival_Port || 'Thời gian dự kiến về cảng' }}
        </nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.Error_IB_Date_Arrival_Port || 'Vui lòng nhập Thời gian dự kiến về cảng!'">
          <nz-date-picker
            nzFormat="dd-MM-yyyy"
            [(ngModel)]="dataObject.dateArrivalPort"
            [nzPlaceHolder]="language_key?.IB_Date_Arrival_Port || 'Thời gian dự kiến về cảng'"
            name="dateArrivalWarehouse"
            required
          >
          </nz-date-picker>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="'Danh sách Items'" [nzActive]="true">
      <!-- DS Items -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="dataObject.lstProduct.length > 0 ? [''] : []" class="mb-3" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>STT</th>
                <th>Mã vật tư</th>
                <th>Vật tư</th>
                <th>Tên hàng hóa</th>
                <th>Mô tả hàng hóa</th>
                <th>Đơn vị tính</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Thành tiền</th>
                <th>Ghi chú</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataObject.lstProduct; let i = index">
                <td>{{ i + 1 }}</td>
                <td>{{ data.itemCode }}</td>
                <td>{{ data.serviceName }}</td>
                <td>{{ data.name }}</td>
                <td>{{ data.description }}</td>
                <td>{{ data.unit }}</td>
                <td>{{ data.quantity | number }}</td>
                <td>{{ data.price | number }}</td>
                <td>{{ data.money | number }}</td>
                <td>{{ data.note }}</td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <nz-collapse class="mt-2">
    <nz-collapse-panel [nzHeader]="language_key?.Container_List || 'Danh sách Container'" [nzActive]="true">
      <!-- Add mới -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24" class="my-3">
          <button nz-button [disabled]="" class="button-add mr-4" (click)="onAddListContainer()" nzType="primary">
            <span nz-icon nzType="plus"></span>
            {{ language_key?.Button_Add || 'Thêm mới' }}
          </button>
          <button nz-button (click)="onDeleteSelectedContainers()" nzType="primary" class="mr-2 button-exit" [disabled]="!hasSelectedContainers()">
            <span nz-icon nzType="delete"></span> {{ language_key?.Button_Delete || 'Xoá' }}
          </button>
        </nz-col>
      </nz-row>

      <!-- DS Container -->
      <nz-row nzGutter="8">
        <nz-col nzSpan="24">
          <nz-table [nzData]="['']" [nzShowPagination]="false" #ajaxTable nzBordered>
            <thead>
              <tr>
                <th class="text-center">
                  <input type="checkbox" [ngModelOptions]="{ standalone: true }" [(ngModel)]="selectAll" (change)="toggleSelectAll()" />
                </th>
                <th>
                  {{ language_key?.Button_STT || 'STT' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_ContNumber || 'Số Cont' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_ShippingSealNumber || 'Số seal hãng tàu' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_SealNumber || 'Số seal' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_ContType || 'Loại cont' }}
                </th>
                <th>
                  {{ language_key?.PurchaseOrder_DeliveryDate_PackagesContNumber || 'Số kiện trên cont' }}
                </th>
                <th>
                  {{ language_key?.Column_Action || 'Tác vụ' }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let containerItem of dataObject.listContainer; let idx = index">
                <td class="text-center"><input type="checkbox" [(ngModel)]="containerItem.isChecked" [ngModelOptions]="{ standalone: true }" /></td>

                <td>{{ idx + 1 }}</td>
                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.containerNumber"
                    [name]="'containerNumber' + idx"
                    [placeholder]="language_key?.PurchaseOrder_DeliveryDate_ContNumber || 'Số cont'"
                    class="input-table"
                  />
                </td>
                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.shipSealNumber"
                    [name]="'shipSealNumber' + idx"
                    [placeholder]="language_key?.PurchaseOrder_DeliveryDate_ShippingSealNumber || 'Số seal hãng tàu'"
                    class="input-table"
                  />
                </td>

                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.sealNumber"
                    [name]="'sealNumber' + idx"
                    [placeholder]="language_key?.PurchaseOrder_DeliveryDate_SealNumber || 'Số seal'"
                    class="input-table"
                  />
                </td>
                <td>
                  <input
                    style="width: 100%"
                    [(ngModel)]="containerItem.containerType"
                    [name]="'containerType' + idx"
                    [placeholder]="language_key?.PurchaseOrder_DeliveryDate_ContType || 'Loại cont'"
                    class="input-table"
                  />
                </td>
                <td>
                  <nz-input-number
                    style="width: 100%"
                    [nzMin]="1"
                    [nzFormatter]="formatterNull"
                    [nzParser]="parser"
                    [(ngModel)]="containerItem.packageQuantity"
                    [ngModelOptions]="{ standalone: true }"
                    [nzPlaceHolder]="language_key?.PurchaseOrder_DeliveryDate_PackagesContNumber || 'Số kiện trên cont'"
                    required
                  ></nz-input-number>
                </td>
                <td>
                  <button nzShape="circle" nz-button (click)="onDeleteListContainer(idx)" nzDanger>
                    <span nz-icon nzType="delete"></span>
                  </button>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <!-- form action -->

  <nz-row class="mt-4">
    <nz-col nzSpan="24" class="text-center">
      <button (click)="closeDialog()" nz-button class="mr-4 button-exit">
        <span nz-icon nzType="close"></span>{{ language_key?.Button_Exit || 'Thoát' }}
      </button>

      <button class="mr-4 button-save" nz-button [disabled]="!frmAdd.form.valid" nzType="primary" (click)="submitForm()">
        <span nz-icon nzType="save"></span>{{ language_key?.Button_Save || 'Lưu' }}
      </button>
    </nz-col>
  </nz-row>
</form>
