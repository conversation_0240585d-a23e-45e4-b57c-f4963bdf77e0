import { Component, Inject, Optional } from '@angular/core'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/base/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './add-or-edit-inbound.component.html',
  styleUrls: ['./add-or-edit-inbound.component.scss'],
})
export class AddOrEditInboundComponent {
  modalTitle = ''
  passwordVisible = false
  passwordVisible2 = false
  validateForm: any
  dataObject: any = {}
  listData = []

  maxSizeUpload = enumData.maxSizeUpload
  dataContract: any[] = []
  dataPO: any[] = []
  dataEmployeeIncharge: any[] = []
  dataExpectWarehouse: any[] = []
  dataDistrict: any[] = []
  language_key: any = {}
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumData: any
  enumProject: any
  enumRole: any

  action: any
  lst: any[] = []
  dataReference: any[] = []
  selectAll: boolean = false

  formatterNull = (value: any) => `${value ? value : ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // Parser chặn nhập chữ
  parser = (value: string) => value.replace(/[^0-9]/g, '')

  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private route: ActivatedRoute,
    private router: Router,
    private storageService: StorageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService,
    private languageService: LanguageService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  async ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()

    this.dataObject = { ...history.state.data }
    if (this.dataObject.id) {
      this.dataObject.listContainer = []
      this.dataObject.lstProduct = []
      this.modalTitle = this.language_key?.IB_Update || 'Chỉnh sửa thông báo giao hàng'
      this.loadDetail(this.dataObject.id)
    } else {
      this.dataObject.listContainer = []
      this.dataObject.lstProduct = []
      this.modalTitle = this.language_key?.IB_Add || 'Thêm mới thông báo giao hàng'
    }
    this.loadDataSelect()
  }

  async loadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.INBOUND_CLIENT.LOAD_DETAIL, { id: id }).then((result) => {
      this.dataObject = result
    })
  }

  submitForm() {
    /**Kiểm tra đã nhập đủ thông tin cho danh sách item chưa */

    this.dataObject.dateArrivalPort = new Date(this.dataObject.dateArrivalPort)
    this.dataObject.deliveryDate = new Date(this.dataObject.deliveryDate)
    /** Ngày dự kiến về cảng  phải lớn hơn ngày giao hàng */
    if (this.dataObject.dateArrivalPort.getTime() < this.dataObject.deliveryDate.getTime()) {
      this.notifyService.showError(this.language_key?.IB_Check_Delivery_Date || 'Ngày dự kiến về cảng phải lớn hơn ngày giao hàng')
      return
    }

    this.notifyService.showloading()
    if (!this.dataObject.id) {
      this.apiService.post(this.apiService.INBOUND_CLIENT.CREATE_DATA, { ...this.dataObject, isSupplierCreate: true }).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.closeDialog()
      })
    } else {
      this.apiService.post(this.apiService.INBOUND_CLIENT.UPDATE_DATA, { ...this.dataObject, isSupplierCreate: true }).then((res) => {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.closeDialog()
      })
    }
    //#endregion
  }

  closeDialog() {
    this.router.navigate(['/inbound'])
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(
        this.language_key?.Error_Upload_File_Size + this.maxSizeUpload + 'MB, ' + this.language_key?.Error_Upload_File ||
          `Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`
      )
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) {
          this.dataObject[fieldName] = res[0]
          this.dataObject.fileName = fileToUpload.name
        } else {
          this.dataObject[fieldName] = ''
          this.dataObject.fileName = ''
        }
      })
    }
  }

  onDeleteListContainer(index: number) {
    this.dataObject.listContainer = this.dataObject.listContainer.filter((_: any, idx: any) => idx !== index)
  }

  onDeleteAllListContainer() {
    this.dataObject.listContainer = []
  }

  // Hàm thêm ngân hàng vào dataObject
  onAddListContainer() {
    if (!this.dataObject.listContainer) {
      this.dataObject.listContainer = []
    }

    this.dataObject.listContainer = [
      ...this.dataObject.listContainer,
      {
        containerNumber: null,
        shipSealNumber: null,
        sealNumber: null,
        containerType: null,
        packageQuantity: null,
        isChecked: false,
      },
    ]
  }

  // Hàm xóa ngân hàng từ dataObject
  onDeleteShipCost(index: number) {
    this.dataObject.listCost = this.dataObject.listCost.filter((_: any, idx: any) => idx !== index)
  }

  onChangePO(poId: any) {
    if (!poId) return
    this.dataObject.poId = poId
    Promise.all([this.apiService.post(this.apiService.PURCHASE_ORDER.LOAD_PO_PRODUCT, { poId: poId })]).then(async (res) => {
      this.dataObject.lstProduct = res[0]
    })
  }

  back() {
    this.router.navigate(['/inbound'])
  }

  onDeleteSelectedContainers() {
    this.dataObject.listContainer = this.dataObject.listContainer.filter((item: any) => !item.isChecked)
    this.selectAll = false
  }

  toggleSelectAll() {
    this.dataObject.listContainer.forEach((item: any) => (item.isChecked = this.selectAll))
  }

  hasSelectedContainers() {
    return this.dataObject.listContainer.some((item: any) => item.isChecked)
  }

  loadDataSelect() {
    Promise.all([this.apiService.post(this.apiService.PURCHASE_ORDER.FIND, {})]).then((res) => {
      this.dataPO = res[0]
    })
  }
}
