import { Component } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Router } from '@angular/router'
import { Subscription } from 'rxjs'
import * as fs from 'file-saver'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Workbook } from 'exceljs'
import { enumData } from 'src/app/base/enumData'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './inbound.component.html',
  styleUrls: ['./inbound.component.scss'],
})
export class InboundComponent {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataSearch: any = {}
  listOfData: any[] = []
  errorString!: string
  isExporting = false
  lstErrorImport: any[] = []
  isVisibleError = false
  isVisibleActive = false
  placeholder = ['Từ ngày', 'Đến ngày']
  isVisibleChangePw = false
  supplierChoose: any

  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumData: any
  dataActive: any = {}
  currentSupplierData: any = {}
  dataStatus: any
  dataExpectWarehouse: any[] = []
  language_key: any = {}
  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private storageService: StorageService,
    private router: Router,
    public authenticationService: AuthenticationService,
    private languageService: LanguageService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()

    this.dataStatus = this.coreService.convertObjToArray(enumData.InboundStatus)
    this.searchData()
  }

  async searchData(reset: boolean = false, clearFilter: boolean = false) {
    this.loading = true
    if (clearFilter) this.dataSearch = {}
    if (reset) this.pageIndex = 1

    const dataSearch = {
      where: this.dataSearch,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    if (this.dataSearch.createdAt) {
      if (this.dataSearch.createdAt[0]) this.dataSearch.dateStart = this.dataSearch.createdAt[0]
      if (this.dataSearch.createdAt[1]) this.dataSearch.dateEnd = this.dataSearch.createdAt[1]
    }

    this.apiService.post(this.apiService.INBOUND_CLIENT.PAGINATION, dataSearch).then((res) => {
      this.loading = false
      this.listOfData = res[0]
      this.total = res[1]
    })
  }

  clickExportExcel() {
    this.loading = true
    this.notifyService.showloading()
    const where = this.dataSearch
    const dataSearch = {
      where,
      skip: 0,
      take: enumData.Page.pageSizeMax,
      order: { createdAt: 'DESC' },
    }

    this.apiService.post(this.apiService.INBOUND_CLIENT.PAGINATION, dataSearch).then((res: any) => {
      if (res) {
        this.loading = false
        this.notifyService.hideloading()

        const workbook = new Workbook()
        const worksheet = workbook.addWorksheet('Danh sách inbound')
        //#region Body Table
        const header = [
          'Mã',
          'Số PO',

          'Nhà cung cấp',
          'Ngày giao hàng',
          'Ngày về kho dự kiến',
          'Thời gian dự kiến về cảng',
          'Ngày tạo',
          'Người phụ trách',
          'Trạng thái',
        ]
        const headerRow = worksheet.addRow(header)

        // Cell Style : Fill and Border
        headerRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3085FE' },
            bgColor: { argb: '3085FE' },
          }
          cell.alignment = { horizontal: 'center' }
          cell.font = { bold: true, color: { argb: 'FFFFFF' }, size: 13 }
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          }

          switch (colNumber) {
            case 1:
              worksheet.getColumn(colNumber).width = 30
              break
            case 2:
              worksheet.getColumn(colNumber).width = 50
              break
            default:
              worksheet.getColumn(colNumber).width = 30
              worksheet.getColumn(colNumber).numFmt = '#,##0;[Red]-#,##0'
              break
          }
        })

        // Add Data and Conditional Formatting
        for (let data of res[0]) {
          const rowData = [
            data.code || '',

            data.poNumber || '',

            data.supplierName || '',
            data.prCode || '',
            data.deliveryDate ? moment(data.deliveryDate).format('DD/MM/YYYY') : '',
            data.dateArrivalWarehouse ? moment(data.dateArrivalWarehouse).format('DD/MM/YYYY') : '',
            data.dateArrivalPort ? moment(data.dateArrivalPort).format('DD/MM/YYYY') : '',
            moment(data.createdAt).format('DD/MM/YYYY') || '',
            data.employeeInchargeName || '',
            data.statusName,
          ]
          const row = worksheet.addRow(rowData)
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            }
          })
        }
        //#endregion

        //#region Save File
        workbook.xlsx.writeBuffer().then((data: any) => {
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          let date = new Date().toISOString()
          const fileName = `INBOUND_${date}.xlsx`
          fs.saveAs(blob, fileName)
          this.notifyService.hideloading()
        })
        //#endregion
      }
    })
  }

  clickAdd() {
    this.router.navigate(['/inbound/add'])
  }

  clickEdit(data: any) {
    this.router.navigate(['/inbound/edit'], {
      state: {
        status: 'edit',
        data,
      },
    })
  }

  destroy(data: any) {
    this.loading = true
    this.apiService
      .post(this.apiService.INBOUND_CLIENT.UPDATE_STATUS, {
        id: data.id,
        status: this.enumData.InboundStatus.CANCEL.code,
      })
      .then((res: any) => {
        this.notifyService.showSuccess('Huỷ thành công')
        this.loading = false
        this.searchData()
      })
  }

  viewDetailInfo(data: any) {
    this.router.navigate(['/inbound/detail'], {
      state: {
        data,
      },
    })
  }
}
