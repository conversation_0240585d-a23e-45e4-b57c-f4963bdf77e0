import { Component, OnInit } from '@angular/core'
import { Router, ActivatedRoute, Params } from '@angular/router'
import { LanguageService } from '../../services/language.service'
@Component({ templateUrl: './welcome-new-user.component.html' })
export class WelcomeNewUserComponent implements OnInit {
  language_key: any = {}
  email = ''
  title = this.language_key?.WelcomeNewUser_PleaseWaitForEmailResponse ||"Đã tạo thành công, vui lòng đợi mail phản hồi"

  constructor(private router: Router, private route: ActivatedRoute, private languageService: LanguageService) {
    this.route.queryParams.subscribe((params: Params) => {
      this.email = params['email'] || ''
    })
  }

  ngOnInit() {
    this.title = `${this.language_key?.WelcomeNewUser_NoticeToEmail || 'Thông tin đăng ký của quý công ty sẽ được gửi đến email'} ${this.email}. ${this.language_key?.WelcomeNewUser_IfNotInfor ||' Nếu không nhận được thông tin, Quý công ty vui lòng liên hệ bộ phận mua hàng của APE'}`
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  gotoHomePage() {
    this.router.navigate([`home`])
  }
}
