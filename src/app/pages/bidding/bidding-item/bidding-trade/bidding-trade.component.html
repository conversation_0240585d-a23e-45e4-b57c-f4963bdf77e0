<div *ngIf="bidid">
  <nz-row class="mt-2">
    <button class="mr-2" nz-button (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>{{ language_key?.BiddingTrade_ExportExcel || 'Xuất excel' }}
    </button>
    <input
      class="hidden"
      type="file"
      id="fileTrade"
      (change)="clickImportExcel($event)"
      onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <label nz-button for="fileTrade" class="lable-custom-file">
      <span nz-icon nzType="upload"></span>{{ language_key?.BiddingTrade_ImportExcel || 'Nhập excel' }}</label
    >
  </nz-row>
  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" nzBordered [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false">
      <thead>
        <tr>
          <th class="hidden">ID</th>
          <th>{{ language_key?.BiddingTrade_Criteria || 'Tiêu chí' }}</th>
          <th>{{ language_key?.BiddingTrade_DataType || 'Kiểu dữ liệu' }}</th>
          <th>{{ language_key?.BiddingTrade_Value || 'Giá trị' }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let data1 of listOfData">
          <tr>
            <td class="hidden">{{ data1.id }}</td>
            <td class="w-50">
              <span *ngIf="data1.isRequired" class="text-danger">*</span>
              <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
            </td>
            <td>{{ data1.type }}</td>
            <td (click)="resetError(data1)">
              <div *ngIf="data1.__childs__.length === 0">
                <div *ngIf="data1.type === dataType.String.code">
                  <input nz-input [(ngModel)]="data1.value" name="value" placeholder="" />
                </div>
                <div *ngIf="data1.type === dataType.Number.code">
                  <input nz-input currencyMask [(ngModel)]="data1.value" name="value" placeholder="" />
                </div>
                <div *ngIf="data1.type === dataType.Date.code">
                  <nz-date-picker [(ngModel)]="data1.value" name="value"> </nz-date-picker>
                </div>
                <div *ngIf="data1.type === dataType.List.code">
                  <nz-select nz-col nzSpan="24" class="w-100" nzShowSearch nzAllowClear nzPlaceHolder="" [(ngModel)]="data1.value" name="value">
                    <nz-option *ngFor="let itemList of data1.__bidTradeListDetails__" [nzLabel]="itemList.name" [nzValue]="itemList.id"></nz-option>
                  </nz-select>
                </div>
                <div *ngIf="data1.type === dataType.File.code">
                  <nz-input-group nzSearch>
                    <input class="passenger-input" nz-input [(ngModel)]="data1.value" name="value" placeholder="" disabled />
                    <button type="button" nzType="primary" (click)="handleClearFile(data1)" nz-button nzSearch>
                      <span nz-icon nzType="delete"></span>
                    </button>
                  </nz-input-group>
                  <label [for]="'zen' + data1.id" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                  <input class="hidden" type="file" [id]="'zen' + data1.id" (change)="handleFileInput($event, data1)" />
                </div>
              </div>

              <span *ngIf="data1.isError" class="text-danger">{{ data1.errorText }}</span>
            </td>
          </tr>
          <ng-container>
            <tr *ngFor="let data2 of data1.__childs__">
              <td class="hidden">{{ data2.id }}</td>
              <td [nzIndentSize]="20" class="w-50">
                <span *ngIf="data2.isRequired" class="text-danger">*</span>
                <span nz-tooltip [nzTooltipTitle]="data2.name">{{ data2.name }}</span>
              </td>
              <td>{{ data2.type }}</td>
              <td (click)="resetError(data2)">
                <div *ngIf="data2.type === dataType.String.code">
                  <input nz-input [(ngModel)]="data2.value" name="value" placeholder="" />
                </div>
                <div *ngIf="data2.type === dataType.Number.code">
                  <input nz-input currencyMask [(ngModel)]="data2.value" name="value" placeholder="" />
                </div>
                <div *ngIf="data2.type === dataType.Date.code">
                  <nz-date-picker [(ngModel)]="data2.value" name="value"> </nz-date-picker>
                </div>
                <div *ngIf="data2.type === dataType.List.code">
                  <nz-select nz-col nzSpan="24" nzShowSearch nzAllowClear nzPlaceHolder="" [(ngModel)]="data2.value" name="value">
                    <nz-option *ngFor="let itemList of data2.__bidTradeListDetails__" [nzLabel]="itemList.name" [nzValue]="itemList.id"></nz-option>
                  </nz-select>
                </div>
                <div *ngIf="data2.type === dataType.File.code">
                  <nz-input-group nzSearch>
                    <input class="passenger-input" nz-input [(ngModel)]="data2.value" name="value" placeholder="" disabled />
                    <button type="button" nzType="primary" (click)="handleClearFile(data2)" nz-button nzSearch>
                      <span nz-icon nzType="delete"></span>
                    </button>
                  </nz-input-group>
                  <label [for]="'zen' + data2.id" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
                  <input class="hidden" type="file" [id]="'zen' + data2.id" (change)="handleFileInput($event, data2)" />
                </div>
                <span *ngIf="data2.isError" class="text-danger">{{ data2.errorText }}</span>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-row>
  <nz-row *ngIf="listOfData.length == 0" class="text-danger mt-3">
    <i>{{ language_key?.BiddingTrade_RequestAdditionTradeCondition || 'Yêu cầu điều kiện thương mại bổ sung sau' }}</i>
  </nz-row>
</div>
