<div *ngIf="bidid">
  <nz-row class="mt-2">
    <button nz-button (click)="clickAddCustomPrice()" nzType="primary" class="mr-2">
      {{ language_key?.BiddingCustomPrice_AddIndexConfigPrice || 'Thêm hạng mục cơ cấu giá' }}
    </button>
    <button class="mr-2" nz-button (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>{{ language_key?.BiddingCustomPrice_ExportExcel || 'Xuất excel' }}
    </button>
    <input
      class="hidden"
      type="file"
      id="fileCustomPrice"
      (change)="clickImportExcel($event)"
      onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <label nz-button for="fileCustomPrice" class="lable-custom-file">
      <span nz-icon nzType="upload"></span>{{ language_key?.BiddingCustomPrice_ImportExcel || 'Nhập excel' }}</label
    >
  </nz-row>

  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" nzBordered [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading" [nzShowPagination]="false">
      <thead>
        <tr>
          <th class="hidden">ID</th>
          <th>{{ language_key?.BiddingCustomPrice_IndexName || 'Tên hạng mục' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_CountUnit || 'Đơn vị tính' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_CurrencyUnit || 'Đơn vị tiền tệ' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_Quantity || 'Số lượng' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_UnitPrice || 'Đơn giá' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_Select || 'Tuỳ chọn' }}</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data1 of listOfData">
          <td class="hidden">{{ data1.id }}</td>
          <td class="mw-25" (click)="clickEditCustomPrice(data1)">
            <span *ngIf="data1.isRequired" class="text-danger">*</span>
            <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
          </td>
          <td (click)="clickEditCustomPrice(data1)">{{ data1.unit }}</td>
          <td (click)="clickEditCustomPrice(data1)">{{ data1.currency }}</td>
          <td (click)="clickEditCustomPrice(data1)" class="text-right">{{ data1.number | number }}</td>
          <td (click)="resetError(data1)">
            <input nz-input currencyMask [(ngModel)]="data1.value" name="value" placeholder="" />
            <span *ngIf="data1.isError" class="text-danger">{{ data1.errorText }}</span>
          </td>
          <td>
            <button
              (click)="clickDeleteCustomPrice(data1)"
              nz-tooltip
              [nzTooltipTitle]="language_key?.BiddingCustomPrice_DeleteIndex || 'Xoá hạng mục'"
              nz-button
              nzDanger
            >
              <span nz-icon nzType="delete"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
  <nz-row *ngIf="listOfData.length == 0" class="text-danger mt-3">
    <i>{{ language_key?.BiddingCustomPrice_RequestConfigAdditionalPrice || 'Yêu cầu cơ cấu giá bổ sung sau' }}</i>
  </nz-row>
</div>
