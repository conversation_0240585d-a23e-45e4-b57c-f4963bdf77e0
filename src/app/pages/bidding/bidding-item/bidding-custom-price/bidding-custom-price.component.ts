import { Component, OnInit, Input } from '@angular/core'
import { ApiService, NotifyService } from '../../../../services'
import { AddBiddingPriceModalComponent } from '../../../modal/add-bidding-price-model/add-bidding-price-modal.component'
import { MatDialog } from '@angular/material/dialog'
import * as uuid from 'uuid'
import { enumData } from 'src/app/base/enumData'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Borders, Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  selector: 'app-bidding-custom-price',
  templateUrl: './bidding-custom-price.component.html',
})
export class BiddingCustomPriceComponent implements OnInit {
  lstUnit: any[] = []
  lstCurrency: any[] = []
  isLoadData: any
  language_key: any = {}
  constructor(
    private apiService: ApiService,
    private languageService: LanguageService,
    private notifyService: NotifyService,
    private dialog: MatDialog
  ) {}
  @Input()
  bidid!: string
  listOfData: any[] = []
  pageSize = enumData.Page.pageSizeMax
  loading = false

  ngOnInit() {
    this.loadData()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  loadData = () => {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CLIENT_WEB.LOAD_DATA_BID_CUSTOMPRICE, { bidId: this.bidid }).then((res) => {
      this.notifyService.hideloading()
      this.listOfData = res || []
    })
  }

  getDataSave = () => {
    return this.listOfData
  }

  clickAddCustomPrice() {
    const item = {
      id: uuid.v4(),
      name: '',
      value: '',
      unit: null,
      currency: null,
      number: 0,
      isNew: true,
      sort: 0,
      isRequired: true,
      childs: [],
    }

    this.dialog
      .open(AddBiddingPriceModalComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((data) => {
        if (data && data.isNew) {
          this.listOfData.push(data)
          this.listOfData = this.listOfData.filter((c) => c.id !== '')
        }
      })
  }

  clickEditCustomPrice(item: any) {
    const itemOld = { ...item }
    item.isNew = false
    this.dialog
      .open(AddBiddingPriceModalComponent, { disableClose: false, data: item })
      .afterClosed()
      .subscribe((data) => {
        if (!data) {
          item.name = itemOld.name
          item.value = itemOld.value
          item.number = itemOld.number
          item.unit = itemOld.unit
          item.currency = itemOld.currency
          item.sort = itemOld.sort
          item.isRequired = itemOld.isRequired
          item.isNew = itemOld.isNew
        }
      })
  }

  clickDeleteCustomPrice(item: any) {
    this.listOfData = this.listOfData.filter((c) => c.id !== item.id)
  }

  resetError(item: any) {
    item.isError = false
    item.errorText = ''
  }

  //#region excel cơ cấu giá
  async loadAllList() {
    const res = await Promise.all([
      this.apiService.post(this.apiService.SETTINGSTRING.FIND, { type: enumData.SettingStringType.unit }),
      this.apiService.post(this.apiService.SETTINGSTRING.FIND, { type: enumData.SettingStringType.currency }),
    ])
    this.lstUnit = res[0]
    this.lstCurrency = res[1]
    this.isLoadData = true
  }

  clickExportExcel() {
    this.notifyService.showloading()

    //#region Body Table
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')
    const headerRow = worksheet.addRow(['ID', 'Tên hạng mục', 'Đơn vị tính', 'Đơn vị tiền tệ', 'Số lượng', 'Bắt buộc?', 'Đơn giá'])

    // Cell Style : Fill and Border
    const border: Partial<Borders> = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    }
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '08298A' },
      }
      cell.border = border
      cell.font = { name: 'Calibri', family: 4, size: 11, bold: true, color: { argb: 'FFFFFF' } }
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 70
          worksheet.getColumn(colNumber).hidden = true // ẩn cột
          break
        case 2:
          worksheet.getColumn(colNumber).width = 50
          break
        default:
          worksheet.getColumn(colNumber).width = 20
          break
      }
    })

    // Màu theo 2 level
    const arrColor = ['ebebeb', 'ffffff']
    for (const data of this.listOfData) {
      const rowData = [
        data.id || '', //'ID',
        data.name || '', //'Tên hạng mục',
        data.unit || '', //'Đơn vị tính',
        data.currency || '', //'Đơn vị tiền tệ',
        data.number || '', //'Số lượng',
        data.isRequired ? 'x' : '', //'Bắt buộc?',
        data.value || '', //'Đơn giá',
      ]
      const row = worksheet.addRow(rowData)
      row.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: arrColor[data.level - 1] },
        }
        cell.border = border
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
        cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
      })
    }
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName =
        `[${moment(new Date()).format('YYYY-MM-DD')}]` +
        (this.language_key?.BiddingCustomPrice_TemplateDocumentConfigPrice || 'Template nhập hồ sơ cơ cấu giá') +
        '.xlsx'
      fs.saveAs(blob, fileName)
      setTimeout(() => {
        this.notifyService.showSuccess(this.language_key?.BiddingCustomPrice_DownloadTemplateExcelSuccessfully || 'Tải template thành công!')
      }, 300)
    })
    //#endregion
  }

  async clickImportExcel(ev: any) {
    this.notifyService.showloading()
    if (!this.isLoadData) {
      await this.loadAllList()
    }
    let workBook = null
    let jsonData: any[] = []
    const lstHeader = ['id', 'name', 'unit', 'currency', 'number', 'isRequired', 'value']

    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      //#region check template
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== (this.language_key?.BiddingCustomPrice_IndexName || 'Tên hạng mục') ||
        header.unit !== (this.language_key?.BiddingCustomPrice_CountUnit || 'Đơn vị tính') ||
        header.currency !== (this.language_key?.BiddingCustomPrice_CurrencyUnit || 'Đơn vị tiền tệ') ||
        header.number !== (this.language_key?.BiddingCustomPrice_Quantity || 'Số lượng') ||
        header.maxPrice !== (this.language_key?.BiddingCustomPrice_IsRequired || 'Bắt buộc?') ||
        header.value !== (this.language_key?.BiddingCustomPrice_UnitPrice || 'Đơn giá')
      ) {
        this.notifyService.showError(this.language_key?.BiddingCustomPrice_FileTemplateNotCorrect || 'File không đúng template (cột đã bị thay đổi)!')
        return
      }
      //#endregion

      //#region check value
      for (const row of jsonData) {
        row.sort = 0
        if (!row.id) row.id = uuid.v4()

        if (row.isRequired == 'x' || row.isRequired == 'X') {
          row.isRequired = true
        } else row.isRequired = false
        if (row.name == null || row.name === '') {
          this.notifyService.showError(this.language_key?.BiddingCustomPrice_IndexNameMustNotBeNull || 'Tên hạng mục không được để trống')
        }
        row.unit = (row.unit || '') + ''
        if (row.unit?.length > 0) {
          const objUnit = this.lstUnit.find((c: any) => c.code === row.unit)
          if (!objUnit) {
            this.notifyService.showError(
              (this.language_key?.BiddingCustomPrice_CountUnit || 'Đơn vị tính') +
                ` [${row.unit}]` +
                (this.language_key?.BiddingCustomPrice_NotExist || 'không tồn tại')
            )
          }
        }
        row.currency = (row.currency || '') + ''
        if (row.currency != null && row.currency.length > 0) {
          const objCurrency = this.lstCurrency.find((c: any) => c.code === row.currency)
          if (!objCurrency) {
            this.notifyService.showError(
              (this.language_key?.BiddingCustomPrice_CurrencyUnit || 'Đơn vị tiền tệ') +
                ` ${row.currency}` +
                (this.language_key?.BiddingCustomPrice_NotExist || 'không tồn tại')
            )
          }
        }
        if (row.number == null || typeof row.number !== 'number') {
          this.notifyService.showError(this.language_key?.BiddingCustomPrice_InputNumber || 'Số lượng là số, không được để trống')
        }

        if (row.isRequired && (row.value == null || row.value === '')) {
          this.notifyService.showError(
            (this.language_key?.BiddingCustomPrice_UnitPrice || 'Đơn giá') +
              `[${row.name}]` +
              (this.language_key?.BiddingCustomPrice_NotNull || 'là bắt buộc không được để trống!')
          )
          return
        }
        if (row.value != null && row.value !== '') {
          const value = +row.value
          if (isNaN(value) || !isFinite(value)) {
            this.notifyService.showError(
              (this.language_key?.BiddingCustomPrice_UnitPrice || 'Đơn giá') +
                `[${row.value}]` +
                (this.language_key?.BiddingCustomPrice_NotNumber || 'không phải kiểu Number')
            )
            return
          }
        }
      }
      this.listOfData = jsonData
      //#endregion

      //#region fill value
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
      //#endregion
    }
  }
  //#endregion
}
