<div *ngIf="bidid && !isError && !isSuccess">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      <b class="fs-18">{{ modalTitle | uppercase }}</b>
    </nz-col>
  </nz-row>

  <nz-row matDialogContent class="mt-2">
    <nz-table nz-col nzSpan="24" [nzData]="bidSupplier.listItem" [(nzPageSize)]="pageSizeMax" [nzShowPagination]="false" nzBordered>
      <thead>
        <tr>
          <th nzWidth="50px">{{ language_key?.Bidding_IndexNumber || 'STT' }}</th>
          <th>{{ language_key?.Bidding_ItemName || 'Tên Item' }}</th>
          <th>{{ language_key?.Bidding_Quantity || 'Số lượng' }}</th>
          <th>{{ language_key?.Bidding_Status || 'Trạng thái' }}</th>
          <th nzWidth="170px">{{ language_key?.Bidding_SubmitDocumentPackage || 'Nhập hồ sơ thầu' }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data1 of bidSupplier.listItem; let i = index">
          <td class="text-center">{{ i + 1 }}</td>
          <td>{{ data1.itemName }}</td>
          <td>{{ data1.quantityItem }}</td>
          <td>{{ data1.statusName }}</td>
          <td class="text-center">
            <button
              nz-tooltip
              [nzTooltipTitle]="language_key?.Bidding_SubmitDocumentPackage || 'Nhập hồ sơ thầu'"
              (click)="chooseItem(data1)"
              nz-button
              nzType="primary"
            >
              <span nz-icon nzType="profile"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>

<div *ngIf="isError">
  <nz-result nzStatus="error" [nzTitle]="language_key?.Bidding_NoAccessGrant || 'ông có quyền truy cập'">
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.Bidding_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
</div>

<div *ngIf="isSuccess">
  <nz-result nzStatus="success" [nzTitle]="language_key?.Bidding_SubmitDocumentSuccessfully || 'Nộp hồ sơ thành công'">
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.Bidding_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
</div>
