import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Params, Router } from '@angular/router'
import { enumData } from 'src/app/base/enumData'
import { ApiService, NotifyService } from 'src/app/services'
import { BiddingItemComponent } from './bidding-item/bidding-item.component'
import { LanguageService } from 'src/app/services/language.service'

@Component({ templateUrl: './bidding.component.html' })
export class BiddingComponent implements OnInit {
  language_key: any = {}
  modalTitle = this.language_key?.Bidding_SelectItemForPackage || 'Chọn Item để nhập hồ sơ gói thầu'
  bidid!: string
  isError = false
  isSuccess = false
  bidSupplier: any = { listItem: [] }
  pageSizeMax = enumData.Page.pageSizeMax

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private apiService: ApiService,
    private notifyService: NotifyService,
    private dialog: MatDialog,
    private languageService: LanguageService
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.bidid = params['bidid']
    })
  }

  ngOnInit() {
    this.loadData()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  loadData() {
    this.notifyService.showloading()
    if (!this.bidid) {
      this.notifyService.showError(this.language_key?.Bidding_CannotDetectPackage || 'Không xác định được gói thầu!')
      return
    }
    this.apiService.post(this.apiService.CLIENT_WEB.LOAD_DATA_BIDDING, { bidId: this.bidid }).then((res) => {
      this.notifyService.hideloading()
      this.bidSupplier = res || {}
      this.modalTitle = (this.language_key?.Bidding_SelectItemForPackage || 'Chọn Item để nhập hồ sơ gói thầu') + `${this.bidSupplier.bidCode}`
    })
  }

  /** Khi chọn Item thì màn hình nộp hồ sơ gói thầu */
  chooseItem(data: any) {
    this.dialog
      .open(BiddingItemComponent, {
        disableClose: false,
        data: {
          bidId: data.id,
          supplierId: this.bidSupplier.supplierId,
          bidCode: this.bidSupplier.bidCode,
          itemName: data.itemName,
        },
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) this.loadData()
      })
  }

  gotoHomePage() {
    this.router.navigate([`home`])
  }
}
