import { Component, OnInit, Optional, Inject } from '@angular/core'
import { ApiService, NotifyService } from 'src/app/services'
import { MAT_DIALOG_DATA } from '@angular/material/dialog'

@Component({ templateUrl: './supplier-notify.component.html' })
export class SupplierNotifyComponent implements OnInit {
  dataObject: any
  constructor(private notifyService: NotifyService, private apiService: ApiService, @Optional() @Inject(MAT_DIALOG_DATA) public data: any) {}

  title = this.data.message

  ngOnInit() {
    this.loadNotify()
  }

  loadNotify() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.HOMEPAGE.GET_NOTIFY(this.data.id), {}).then((res) => {
      this.notifyService.hideloading()
      this.dataObject = res
    })
  }
}
