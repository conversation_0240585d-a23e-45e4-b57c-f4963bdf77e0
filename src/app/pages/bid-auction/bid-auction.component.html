<div *ngIf="!data.isSuccess && !data.isError; else showResult">
  <h1>{{ language_key?.BidAuction_BiddingPackage || 'Đấu giá gói thầu:' }} {{ data.bidName || '' }}</h1>
  <nz-collapse nzBordered="false" *ngFor="let child of data.listChild; let i = index" class="mt-2">
    <nz-collapse-panel [nzHeader]="'Item ' + child.itemName" class="ant-bg-antiquewhite" [nzActive]="!dicActiveCollapse[child.id]" (nzActiveChange)="dicActiveCollapse[child.id] = $event">
      <nz-row class="mt-2">
        <nz-descriptions [nzColumn]="1">
          <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.BidAuction_CurrentStatus || 'Thứ hạng hiện tại'">
            {{ child.currentRank > 0 ? child.currentRank : language_key?.BidAuction_NotParticipated || 'Chưa tham gia' }}
          </nz-descriptions-item>
          <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.BidAuction_NumberOfCompaniesParticipated || 'Tổng công ty tham gia'">
            {{ child.numSupplierAuction }}
          </nz-descriptions-item>
        </nz-descriptions>
      </nz-row>
      <nz-row class="mt-2">
        <button class="mr-2" nz-button (click)="clickExportExcel(i)">
          <span nz-icon nzType="download"></span>{{ language_key?.BidAuction_ExportExcel || 'Xuất excel' }}
        </button>
        <input
          class="hidden"
          type="file"
          [id]="'file' + i"
          (change)="clickImportExcel($event, child)"
          onclick="this.value=null"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        />
        <label nz-button [for]="'file' + i" class="lable-custom-file">
          <span nz-icon nzType="upload"></span>{{ language_key?.BidAuction_ImportExcel || 'Nhập excel' }}
        </label>
      </nz-row>
      <nz-row class="mt-2">
        <nz-table
          nz-col
          nzSpan="24"
          [id]="'test-html-table' + i"
          nzBordered
          [nzData]="child.lstAuctionPrice"
          [nzFrontPagination]="false"
          [nzShowPagination]="false"
        >
          <thead>
            <tr>
              <th class="hidden">ID</th>
              <th>{{ language_key?.BidAuction_IndexName || 'Tên hạng mục' }}</th>
              <th
                *ngFor="let col of child.lstPriceCol"
                [ngClass]="{ 'dynamic-col-mpo': col.colType === mpoType, 'dynamic-col-supplier': col.colType === supType }"
              >
                {{ col.name }}
              </th>
              <th>{{ language_key?.BidAuction_CountUnit || 'Đơn vị tính' }}</th>
              <th>{{ language_key?.BidAuction_CurrencyUnit || 'Đơn vị tiền tệ' }}</th>
              <th>{{ language_key?.BidAuction_Quantity || 'Số lượng' }}</th>
              <th>{{ language_key?.BidAuction_MaximumPrice || 'Giá tối đa' }}</th>
              <th>{{ language_key?.BidAuction_InputedValue || 'Giá trị đã nhập' }}</th>
              <th>{{ language_key?.BidAuction_UnitPrice || 'Đơn giá' }}</th>
              <th *ngIf="!isExporting">{{ language_key?.BidAuction_DetailInformation || 'Thông tin chi tiết' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of child.lstAuctionPrice">
              <td class="hidden">{{ row.bidPriceId }}</td>
              <td class="mw-25">
                <span *ngIf="row.__bidPrice__.isRequired" class="text-danger">*</span>
                <span nz-tooltip [nzTooltipTitle]="row.__bidPrice__.name">{{ row.__bidPrice__.name }}</span>
              </td>
              <td *ngFor="let col of child.lstPriceCol">
                {{ row[col.id] }}
              </td>
              <td>{{ row.__bidPrice__.unit }}</td>
              <td>{{ row.__bidPrice__.currency }}</td>
              <td class="text-right">{{ row.__bidPrice__.number | number }}</td>
              <td class="text-right">{{ row.maxPrice | number }}</td>
              <td class="text-right">{{ row.oldValue | number }}</td>
              <td>
                <input nz-input currencyMask [(ngModel)]="row.value" />
              </td>
              <td *ngIf="!isExporting">
                <span
                  *ngIf="row.__bidPrice__.__bidPriceListDetails__?.length"
                  nz-icon
                  nzType="exclamation-circle"
                  nz-popover
                  [nzPopoverContent]="contentTemplate"
                  nzPopoverTrigger="hover"
                ></span>
                <ng-template #contentTemplate>
                  <div *ngFor="let item of row.__bidPrice__.__bidPriceListDetails__">
                    <p>{{ item.name }} - {{ item.value }}</p>
                  </div>
                </ng-template>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <div class="text-center mt-2">
    <button nzType="primary" (click)="saveAuction()" nz-button>{{ language_key?.BidAuction_SaveAuction || 'Lưu đấu giá' }}</button>
  </div>
</div>

<ng-template #showResult>
  <nz-result *ngIf="data.isSuccess" [nzTitle]="data.message" nzStatus="success">
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.BidAuction_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
  <nz-result *ngIf="data.isError" [nzTitle]="data.message" nzStatus="error">
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.BidAuction_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
</ng-template>
