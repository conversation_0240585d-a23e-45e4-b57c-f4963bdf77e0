import { Component, OnInit } from '@angular/core'
import { ApiService, NotifyService } from '../../services'
import { enumData } from '../../base/enumData'
import { NzModalService } from 'ng-zorro-antd/modal'
import { ActivatedRoute, Params, Router } from '@angular/router'
import * as XLSX from 'xlsx'
import { LanguageService } from 'src/app/services/language.service'

@Component({ templateUrl: './bid-auction.component.html' })
export class BidAuctionComponent implements OnInit {
  bidAuctionId = ''
  data: any = {}
  mpoType = enumData.ColType.MPO.code
  supType = enumData.ColType.Supplier.code

  /** <PERSON><PERSON>ch thước tối đa tính bằng MB */
  maxSizeUpload = enumData.maxSizeUpload

  checkResultMessage!: { check: boolean; message: string }
  isExporting = false
  dicActiveCollapse: any = {}
  language_key: any = {}

  constructor(
    private route: ActivatedRoute,
    private apiService: ApiService,
    private router: Router,
    private notifyService: NotifyService,
    private modal: NzModalService,
    private languageService: LanguageService
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.bidAuctionId = params['bidauctionid']
    })
  }

  ngOnInit() {
    this.getBidAuction()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  getBidAuction = () => {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.CLIENT_WEB.GET_BID_AUCTION(this.bidAuctionId), {}).then((res) => {
      this.notifyService.hideloading()
      this.data = res
    })
  }

  saveAuction() {
    let strErr = ''
    for (const child of this.data.listChild) {
      for (const item of child.lstAuctionPrice) {
        if (item.__bidPrice__.isRequired) {
          let value = +item.value
          if (isNaN(value) || !isFinite(value)) {
            strErr +=
              (this.language_key?.BidAuction_Index || 'Hạng mục') +
              `[${item.__bidPrice__.name}]` +
              (this.language_key?.BidAuction_InputNotCorrect || 'nhập đơn giá không hợp lệ.') +
              '<br>'
            continue
          }
          if (item.maxPrice != null && value > item.maxPrice) {
            strErr +=
              (this.language_key?.BidAuction_Index || 'Hạng mục') +
              `[${item.__bidPrice__.name}]` +
              (this.language_key?.BidAuction_InputOverMaximumPrice || 'nhập đơn giá vượt quá giá tối đa.') +
              '<br>'
            continue
          }
          if (value <= 0) {
            strErr +=
              (this.language_key?.BidAuction_Index || 'Hạng mục') +
              `[${item.__bidPrice__.name}]` +
              (this.language_key?.BidAuction_InputMustGreaterThanZero || 'nhập đơn giá phải lớn hơn 0.') +
              '<br>'
            continue
          }
        }
      }
    }

    if (strErr.length > 0) {
      this.notifyService.showError(strErr)
      return
    }
    this.modal.confirm({
      nzTitle: '<i>' + (this.language_key?.BidAuction_DoYouWantToFinish || 'Bạn có thực sự muốn hoàn tất hồ sơ đã điền?') + '</i>',
      nzContent:
        `<b>` +
        (this.language_key?.BidAuction_MakeSureAllTheInformationIsCorrectBeforeSubmit ||
          'Hồ sơ đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận') +
        `</b>`,
      nzOnOk: () => {
        this.notifyService.showloading()
        this.apiService.post(this.apiService.CLIENT_WEB.SUPPLIER_SAVE_AUCTION, this.data).then(() => {
          this.notifyService.showSuccess(this.language_key?.BidAuction_SendAuctionSuccess || 'Gửi đấu giá thành công!')
          this.gotoHomePage()
        })
      },
    })
  }

  gotoHomePage() {
    this.router.navigate([`home`])
  }

  clickExportExcel(i: any) {
    this.notifyService.showloading()
    this.isExporting = true

    const itemName = this.data.listChild[i].itemName
    setTimeout(() => {
      const tbl = document.getElementById('test-html-table' + i)
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = (this.language_key?.BidAuction_TemplateInputItem || 'Template nhập đấu giá Item') + `[${itemName}].xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
      this.isExporting = false
      this.notifyService.hideloading()
    }, 300)
  }

  clickImportExcel(ev: any, item: any) {
    this.notifyService.showloading()
    let workBook = null
    let jsonData: any = null
    const lstColId = item.lstPriceCol.map((c: any) => c.id)
    let lstHeader = ['id', 'name', ...lstColId, 'unit', 'currency', 'number', 'maxPrice', 'history', 'value']
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = async (): Promise<any> => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      // bỏ dòng đầu tiên
      let isErr = false
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== (this.language_key?.BidAuction_IndexName || 'Tên hạng mục') ||
        header.unit !== (this.language_key?.BidAuction_CountUnit || 'Đơn vị tính') ||
        header.currency !== (this.language_key?.BidAuction_CurrencyUnit || 'Đơn vị tiền tệ') ||
        header.number !== (this.language_key?.BidAuction_Quantity || 'Số lượng') ||
        header.maxPrice !== (this.language_key?.BidAuction_MaximumPrice || 'Giá tối đa') ||
        header.history !== (this.language_key?.BidAuction_InputedValue || 'Giá trị đã nhập') ||
        header.value !== (this.language_key?.BidAuction_UnitPrice || 'Đơn giá')
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError(this.language_key?.BidAuction_FileTemplateNotCorrect || 'File không đúng template đấu giá của gói thầu')
        return false
      }

      for (const row of jsonData) {
        const itemNew = item.lstAuctionPrice.find((c: any) => c.bidPriceId === row.id)
        if (itemNew && row.value != null && row.value !== '') {
          itemNew.value = row.value
        }
      }
      setTimeout(() => {
        this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
      }, 100)
    }
  }
}
