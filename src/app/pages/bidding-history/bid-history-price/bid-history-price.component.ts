import { Component, OnInit, Input } from '@angular/core'
import { ApiService, CoreService, StorageService, AuthenticationService } from '../../../services'
import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import * as moment from 'moment'

@Component({
  selector: 'app-bid-history-price',
  templateUrl: './bid-history-price.component.html',
})
export class BidHistoryPriceComponent implements OnInit {
  loading = false
  dataType: any
  pageSize: any
  mpoType: any
  supType: any
  bidSupplier: any
  lstResult: any[] = []
  dataPrice: any
  todate = new Date()
  isShowListBidDeal = false

  bid: any

  enumData: any
  language_key: any
  subscriptions: Subscription = new Subscription()

  @Input()
  bidId!: string
  constructor(
    private authenticationService: AuthenticationService,
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) {
        this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Client.BiddingHistory.data.Price.code)
      }
    })
    this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Client.BiddingHistory.data.Price.code)

    this.dataType = this.enumData.DataType
    this.pageSize = this.enumData.Page.pageSizeMax
    this.mpoType = this.enumData.ColType.MPO.code
    this.supType = this.enumData.ColType.Supplier.code
    this.loadDataList()
  }

  loadDataList() {
    if (!this.bidId) return

    this.loading = true
    this.apiService.get(this.apiService.CLIENT_WEB.GET_BID_HISTORY_PRICE(this.bidId), {}).then((res) => {
      this.loading = false
      this.bid = res.bid
      this.bidSupplier = res.bidSupplier
      this.lstResult = res.lstResult || []
      if (this.lstResult.length > 0) {
        this.dataPrice = this.lstResult[0]
        this.dataPrice.totalPrice = 0
        for (const data1 of this.dataPrice.lstPrice) {
          const value = +data1.value
          data1.price = data1.number * value
          this.dataPrice.totalPrice += data1.price
          for (const data2 of data1.__childs__) {
            const value = +data2.value
            data2.price = data2.number * value
            for (const data3 of data2.__childs__) {
              const value = +data3.value
              data3.price = data3.number * value
            }
          }
        }
      }
    })
  }

  clickExportExcel() {
    setTimeout(() => {
      const tbl = document.getElementById('price-detail-table')
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Bảng chào giá gói thầu [${this.bidSupplier.bidName}].xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }

  clickExportExcelByTable(table: string) {
    setTimeout(() => {
      const tbl = document.getElementById(table)
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = `[${moment(new Date()).format('YYYY-MM-DD')}] Lịch sử nộp giá gói thầu [${this.bidSupplier.bidName}].xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
    }, 100)
  }
}
