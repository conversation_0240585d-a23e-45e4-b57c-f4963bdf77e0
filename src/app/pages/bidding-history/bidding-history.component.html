<nz-row class="mt-3">
  <h2>{{ language_key?.BID_HISTORY || 'LỊCH SỬ ĐẤU THẦU' }}</h2>
</nz-row>

<nz-row class="mt-3" nzGutter="8">
  <nz-col nzSpan="16">
    <input nz-input [(ngModel)]="dataSearch.textFilter"
      [placeholder]="language_key?.SEARCH_BID_CODE_NAME || 'Tìm theo mã hoặc tên gói thầu'" class="input-enter" />
  </nz-col>
  <nz-col nzSpan="8">
    <button nz-button nzType="primary" (click)="searchData(true)">
      {{ language_key?.SEARCH || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table nz-col nzSpan="24" class="mb-3" #ajaxTable [nzData]="listOfData" [(nzPageSize)]="pageSize"
    [nzLoading]="loading" [nzShowPagination]="false" nzBordered>
    <thead>
      <tr>
        <th>{{ language_key?.BID_CODE || 'Mã gói thầu' }}</th>
        <th>{{ language_key?.BID_NAME || 'Tên gói thầu' }}</th>
        <th>{{ language_key?.JOIN_DATE || 'Ngày tham gia' }}</th>
        <th>{{ language_key?.BID_STATUS || 'Trạng thái gói thầu' }}</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of ajaxTable.data">
        <td>{{ data.bidCode }}</td>
        <td>{{ data.bidName }}</td>
        <td>{{ data.createdAt | date: 'dd/MM/yyyy HH:mm' }}</td>
        <td>{{ data.bidStatusName }}</td>
        <td>
          <button nz-tooltip nzTooltipTitle="DS Item" nz-button (click)="showItem(data)" class="mr-2">
            <span nz-icon nzType="unordered-list"></span>
          </button>
          <button *ngIf="data.isShowResult && data.isSuccessBid" nz-tooltip
            [nzTooltipTitle]="language_key?.BID_PASS_LETTER || 'Thư thông báo trúng thầu'"
            (click)="showSuccessBid(data)" class="ant-btn-success" nz-button>
            <span nz-icon nzType="mail"></span>
          </button>
          <button *ngIf="data.isShowResult && !data.isSuccessBid" nz-tooltip
            [nzTooltipTitle]="language_key?.THANK_LETTER || 'Thư cảm ơn'" (click)="showFailBid(data)"
            class="ant-btn-blue" nz-button>
            <span nz-icon nzType="mail"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination [nzTotal]="total" [(nzPageIndex)]="pageIndex" [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()" (nzPageSizeChange)="searchData(true)" [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger>
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items
  </ng-template>
</nz-row>

<nz-modal [(nzVisible)]="isVisibleItem" [nzTitle]="titleDetail" (nzOnCancel)="isVisibleItem=false;bidSupplier=null;"
  [nzWidth]="'70vw'" [nzFooter]="null" *ngIf="bidSupplier">
  <ng-container *nzModalContent>
    <nz-row matDialogContent class="mt-2">
      <nz-table nz-col nzSpan="24" [nzData]="bidSupplier.listItem" [(nzPageSize)]="pageSizeMax"
        [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th nzWidth="50px">STT</th>
            <th>Tên Item</th>
            <th>Số lượng</th>
            <th>Trạng thái nộp thầu</th>
            <th>Trạng thái hồ sơ</th>
            <th>Duyệt chọn thầu</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data1 of bidSupplier.listItem; let i = index">
            <td (click)="showDetail(data1)" class="text-center">{{ i + 1 }}</td>
            <td (click)="showDetail(data1)">{{ data1.itemName }}</td>
            <td (click)="showDetail(data1)">{{ data1.quantityItem }}</td>
            <td (click)="showDetail(data1)">{{ data1.statusName }}</td>
            <td (click)="showDetail(data1)">{{ data1.statusFileName }}</td>
            <td (click)="showDetail(data1)">{{ data1.successBidStatus }}</td>
          </tr>
        </tbody>
      </nz-table>
    </nz-row>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleDetail" [nzTitle]="titleDetail" (nzOnCancel)="isVisibleDetail=false;itemChoose=null;"
  [nzWidth]="'70vw'" [nzFooter]="null" *ngIf="itemChoose">
  <ng-container *nzModalContent>
    <app-bid-history-price [bidId]="itemChoose.id"></app-bid-history-price>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleNotifySuccessBid" [nzTitle]="titleDetail"
  (nzOnCancel)="isVisibleNotifySuccessBid=false;bidSupplier=null;" [nzWidth]="'600px'" [nzFooter]="null"
  *ngIf="bidSupplier">
  <ng-container *nzModalContent>
    <div id="print-section" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
      <img src="../../../../../assets/img/logo.png" alt="logo_ape" height="40" width="120" style="float: right;">
      <br>
      <div style="text-align:left; font-weight: 700; font-size: 14px; width: 70%;">
        <div>{{ bidSupplier.companyInvite }}</div>
        <div>{{ bidSupplier.addressSubmit }}</div>
      </div>
      <br>
      <p style="text-indent: 40px;">
        <i style="margin-right: 40px;">
          <u>{{ language_key?.DEAR || 'Kính gửi : ' }}</u>
        </i>{{ bidSupplier.supplierName }}
      </p>
      <br>
      <br>
      <p style="text-indent: 40px;">{{bidSupplier.companyInvite}} trân trọng cảm ơn {{bidSupplier.supplierName}} đã tham
        gia chào thầu "{{bidSupplier.bidName}}" theo thông báo mời thầu của {{bidSupplier.companyInvite}}.</p>
      <p style="text-indent: 40px;">Hồ sơ chào thầu của {{bidSupplier.supplierName}} đạt các tiêu chí năng lực, kỹ
        thuật, điều kiện thương mại và chào giá cạnh tranh. Chúng tôi trân trọng thông báo và chúc mừng Quý công ty đã
        được chọn thực hiện gói thầu "{{bidSupplier.bidName}}". Đề nghị Quý Công ty thực hiện đúng các tiêu chí cam kết
        như đã chào thầu.</p>
      <div style="text-indent: 40px;">Danh sách Item trúng thầu:</div>
      <div style="text-indent: 40px;" *ngFor="let item of bidSupplier.listItem; index as i2">
        ({{i2+1}}) Tên Item: {{item.itemName}} &nbsp;&nbsp;&nbsp; Số lượng: {{item.quantityItem | number:
        '1.0-2'}}
      </div>

      <p style="text-indent: 40px; margin-top: 20px;"><i>(Chi tiết hạng mục công việc và giá chọn thầu như Hồ sơ chào
          thầu).</i></p>
      <p style="text-indent: 40px;">Thay mặt {{bidSupplier.companyInvite}}, kính chúc sức khỏe, thành công và hạnh phúc
        đến {{bidSupplier.supplierName}}.</p>
      <div style="float:right; font-weight: 600;"><i>
          {{ language_key?.DATE || 'Ngày' }} {{ todate | date: 'dd' }}
          {{ language_key?.MONTH || 'tháng' }} {{ todate | date: 'MM' }}
          {{ language_key?.YEAR || 'năm' }} {{ todate | date: 'yyyy' }}
        </i>
      </div>
      <br>
      <p>{{ language_key?.WELCOME_REGARD || 'Trân trọng kính chào' }},</p>
      <div style="float:right;padding-right:5px;">
        <h4>{{ language_key?.BIDDING_BOARD || 'CT. HỘI ĐỒNG XÉT THẦU' }}</h4>
        <h4 style="text-indent: 70px;">{{ language_key?.SIGNED || 'ĐÃ KÝ' }}</h4>
      </div>
      <br>
      <br>
      <p><i>{{ language_key?.RECIPIENT || 'Nơi nhận' }}:</i></p>
      <p>- {{ language_key?.SUPPLIER || 'Nhà thầu' }}</p>
      <p>- {{ language_key?.INTERNAL_SAVE || 'Lưu nội bộ' }}</p>
    </div>
  </ng-container>
</nz-modal>

<nz-modal [(nzVisible)]="isVisibleNotifyFailBid" [nzTitle]="titleDetail"
  (nzOnCancel)="isVisibleNotifyFailBid=false;bidSupplier=null;" [nzWidth]="'600px'" [nzFooter]="null"
  *ngIf="bidSupplier">
  <ng-container *nzModalContent>
    <div id="print-section" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
      <img src="../../../../../assets/img/logo.png" alt="logo_ape" height="40" width="120" style="float: right;">
      <br>
      <div style="text-align:left; font-weight: 700; font-size: 14px; width: 70%;">
        <div>{{bidSupplier.companyInvite}}</div>
        <div>{{bidSupplier.addressSubmit}}</div>
      </div>
      <br>
      <p style="text-indent: 50px;">
        <i style="margin-right: 40px;">
          <u>{{ language_key?.DEAR || 'Kính gửi : ' }}</u>
        </i>{{ bidSupplier.supplierName }}
      </p>
      <br>
      <br>
      <p style="text-indent: 50px;">{{bidSupplier.companyInvite}} trân trọng cảm ơn {{bidSupplier.supplierName}} đã tham
        gia chào thầu "{{bidSupplier.bidName}}" theo thông báo mời thầu của {{bidSupplier.companyInvite}}.</p>
      <p style="text-indent: 50px;">Hồ sơ chào thầu của {{bidSupplier.supplierName}} không đạt các tiêu chí mời thầu.
        Chúng tôi rất tiếc thông báo Quý Công ty không được chọn thực hiện
        gói thầu "{{bidSupplier.bidName}}". Chúng tôi mong được hợp tác của Quý Công ty cho những lần mời thầu sau.</p>
      <br>
      <p style="text-indent: 50px;">Thay mặt {{bidSupplier.companyInvite}}, kính chúc sức khỏe, thành công và hạnh phúc
        đến
        {{bidSupplier.supplierName}}.</p>
      <div style="float:right; font-weight: 600;"><i>
          {{ language_key?.DATE || 'Ngày' }} {{ todate | date: 'dd' }}
          {{ language_key?.MONTH || 'tháng' }} {{ todate | date: 'MM' }}
          {{ language_key?.YEAR || 'năm' }} {{ todate | date: 'yyyy' }}
        </i>
      </div>
      <br>
      <p>{{ language_key?.THANK_REGARD || 'Trân trọng cảm ơn' }},</p>
      <div style="float:right;padding-right:5px;">
        <h4>{{ language_key?.BIDDING_BOARD || 'CT. HỘI ĐỒNG XÉT THẦU' }}</h4>
        <h4 style="text-indent: 70px;">{{ language_key?.SIGNED || 'ĐÃ KÝ' }}</h4>
      </div>
      <br>
      <br>
      <p><i>{{ language_key?.RECIPIENT || 'Nơi nhận' }}:</i></p>
      <p>- {{ language_key?.SUPPLIER || 'Nhà thầu' }}</p>
      <p>- {{ language_key?.INTERNAL_SAVE || 'Lưu nội bộ' }}</p>
    </div>
  </ng-container>
</nz-modal>