.w-h-75 {
  width: 75px;
  height: 75px;
}

.user-container {
  float: left;
  width: 100%;
}

.user-card-image {
  background-color: #ddd;
  background-size: cover;
  -webkit-background-size: cover;
  background-position: center center;
  border: 4px solid #fff;
  border-radius: 0;
  float: left;
  height: 95px;
  overflow: hidden;
  position: relative;
  transition: all linear 0.15s;
  width: 95px;
  z-index: 10;
  cursor: pointer;
}
.user-card-image span.tender-month {
  position: absolute;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  top: 7px;
  color: #fff !important;
  text-align: center;
}

.user-card-image span.tender-date {
  position: absolute;
  left: -5px;
  right: 0;
  top: 42px;
  color: #000;
  font-size: 33px;
  text-align: center;
}

.margin-left-85 {
  margin-left: 85px;
}

.name-item-list {
  font-size: 15px;
  color: #4197e1 !important;
  font-weight: bold;
  cursor: pointer;
  position: relative !important;
}

.item-listview-content {
  padding-top: 5px;
}

.tender-package-content {
  margin: 0 !important;
}

.user-card-image:before,
.user-banner-image:before {
  content: '';
  display: block;
  position: absolute;
  background-color: #f36e26;
  height: 30px;
  width: 95px;
}

.ant-list-split .ant-list-item {
  border-bottom: none;
}

.ant-list-item {
  padding: 1px;
}

.mark-notice {
  display: inline-block;
  font-size: 11px;
  background: #0d4ea3;
  color: #fff;
  font-weight: normal;
  padding: 3px 15px 5px;
  margin-left: 5px;
  position: relative;
  top: 0;
  right: -12px;
  position: absolute;
  background-size: 100% 100%;
}
.mark-notice::before {
  content: '';
  display: block;
  position: absolute;
  right: 100%;
  top: 11px;
  margin-top: -10px;
  width: 0;
  height: 0;
  border-top: 22px solid transparent;
  border-right: 10px solid #0d4ea3;
  border-bottom: 0 solid transparent;
  border-left: 10px solid transparent;
}
.mark-notice::after {
  content: '';
  display: block;
  position: absolute;
  right: -10px;
  top: 23px;
  width: 0;
  height: 0;
  border-top: 0 solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 12px solid #0d4ea3;
}

.mark-notice-process {
  display: inline-block;
  font-size: 11px;
  background: #4fb671;
  color: #fff;
  font-weight: normal;
  padding: 3px 15px 5px;
  margin-left: 5px;
  position: relative;
  top: 0;
  right: -12px;
  position: absolute;
  background-size: 100% 100%;
}
.mark-notice-process::before {
  content: '';
  display: block;
  position: absolute;
  right: 100%;
  top: 11px;
  margin-top: -10px;
  width: 0;
  height: 0;
  border-top: 22px solid transparent;
  border-right: 10px solid #4fb671;
  border-bottom: 0 solid transparent;
  border-left: 10px solid transparent;
}
.mark-notice-process::after {
  content: '';
  display: block;
  position: absolute;
  right: -10px;
  top: 23px;
  width: 0;
  height: 0;
  border-top: 0 solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 12px solid #4fb671;
}

.mark-notice-expired {
  display: inline-block;
  font-size: 11px;
  background: #ff0000;
  color: #fff;
  font-weight: normal;
  padding: 3px 15px 5px;
  margin-left: 5px;
  position: relative;
  top: 0;
  right: -12px;
  position: absolute;
  background-size: 100% 100%;
}
.mark-notice-expired::before {
  content: '';
  display: block;
  position: absolute;
  right: 100%;
  top: 11px;
  margin-top: -10px;
  width: 0;
  height: 0;
  border-top: 22px solid transparent;
  border-right: 10px solid #ff0000;
  border-bottom: 0 solid transparent;
  border-left: 10px solid transparent;
}
.mark-notice-expired::after {
  content: '';
  display: block;
  position: absolute;
  right: -10px;
  top: 23px;
  width: 0;
  height: 0;
  border-top: 0 solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 12px solid #ff0000;
}

h3.block_title {
  height: 43px;
  line-height: 26px;
  padding: 8px 0;
  /* margin: 0; */
  letter-spacing: 0;
  text-transform: uppercase;
  color: #f36e26;
  border-bottom: 3px solid #f36e26;
  font-size: 14px;
  font-weight: bold;
}

h3.block_title .view_all {
  font-size: 12px;
  text-transform: none;
  font-weight: normal !important;
  text-decoration: none;
  padding-left: 11px;
  zoom: 1;
  float: right;
  color: #868686;
}
.company-invite {
  color: rgb(0, 60, 255);
  /* font-size: 20px; */
}
.pagination {
  margin-top: 5px;
  text-align: center;
}

.border-left-custom {
  border-left: 3px solid #f36e26;
}
