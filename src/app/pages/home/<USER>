<h3 class="block_title">{{ language_key?.Home_SuggestingBidNotification || 'Thông báo mời thầu' }}</h3>

<nz-list [nzDataSource]="listOfData" [nzRenderItem]="item" [nzItemLayout]="'horizontal'" class="sider-right-items">
  <ng-template #item let-item>
    <nz-list-item class="ad-item">
      <nz-card class="user-container user-container-home border-left-custom mb-3">
        <div class="user-card padding-top-25">
          <div class="user-card-image w-h-75 mr-2" (click)="viewDetail(item)">
            <span class="tender-month">{{ language_key?.Home_Month || 'Tháng' }} {{ item.month }} </span>
            <span class="tender-date">{{ item.day }}</span>
          </div>
          <div class="margin-left-85">
            <div class="name-item-list" (click)="viewDetail(item)">{{ item.name }}({{ language_key?.Home_Code || 'Mã' }}: {{ item.code }})</div>
            <div class="item-listview-content">
              <p class="tender-package-content">
                <label
                  ><b>{{ language_key?.Home_ReleasePeriod || 'Thời gian phát hành' }}: </b> {{ item.begin_time | date : 'dd/MM/yyyy' }} -
                  {{ item.end_time | date : 'dd/MM/yyyy' }}</label
                >
              </p>
              <p class="tender-package-content">
                <label
                  ><b>{{ language_key?.Home_SuggestedBidSide || 'Bên mời thầu' }}: </b>{{ item.companyInvite }}</label
                >
              </p>
            </div>
          </div>
          <span class="mark-notice" *ngIf="item.submit === 0">{{ language_key?.Home_OpeningBid || 'Đang phát hành' }}</span>
          <span class="mark-notice-process" *ngIf="item.submit === 1">{{ language_key?.Home_OpenedBid || 'Đã mở thầu' }}</span>
          <span class="mark-notice-expired" *ngIf="item.submit === 2">{{ language_key?.Home_ClosedBid || 'Đóng thầu' }}</span>
        </div>
      </nz-card>
    </nz-list-item>
  </ng-template>
</nz-list>
<div class="pagination">
  <nz-pagination
    [(nzPageIndex)]="page"
    [nzTotal]="total"
    [(nzPageSize)]="pageSize"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
    [nzItemRender]="renderItemTemplate"
    (nzPageIndexChange)="pageIndexChange()"
    (nzPageSizeChange)="pageSizeChange()"
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total>
    {{ range[0] }}-{{ range[1] }} / {{ total }} {{ language_key?.Home_BiddingPackage || 'Gói thầu' }}
  </ng-template>
  <ng-template #renderItemTemplate let-type let-page="page">
    <ng-container [ngSwitch]="type">
      <a *ngSwitchCase="'page'">{{ page }}</a>
      <a *ngSwitchCase="'prev'">Previous</a>
      <a *ngSwitchCase="'next'">Next</a>
      <a *ngSwitchCase="'prev_5'">&lt&lt</a>
      <a *ngSwitchCase="'next_5'">&gt&gt</a>
    </ng-container>
  </ng-template>
</div>
