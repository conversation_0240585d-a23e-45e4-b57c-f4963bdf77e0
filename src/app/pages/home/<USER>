import { Component } from '@angular/core'
import { ActivatedRoute, Params, Router } from '@angular/router'
import { ApiService, AuthenticationService, NotifyService } from '../../services'
import { enumData } from '../../base/enumData'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent {
  listOfData: any[] = []
  language_key: any = {}
  page: any
  serviceId: any
  hasChild: any
  pageSize: any
  total = 0
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private apiService: ApiService,
    private languageService: LanguageService,
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.page = +params['page'] > 0 ? +params['page'] : 1
      this.pageSize = +params['pageSize'] > 0 ? +params['pageSize'] : 10
      this.serviceId = params['serviceId']

      this.hasChild = params['hasChild'] === 'true'

      setTimeout(this.loadData, 100)
    })
  }
  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }
  loadData = () => {
    if (this.hasChild) return
    this.notifyService.showloading()
    let url = this.apiService.CLIENT_WEB.PAGINATION_HOMEPAGE
    if (this.authenticationService.currentUserValue) {
      url = this.apiService.CLIENT_WEB.PAGINATION_HOMEPAGE_HAD_TOKEN
    }

    this.apiService.post(url, { where: { serviceId: this.serviceId }, skip: (this.page - 1) * this.pageSize, take: this.pageSize }).then((res) => {
      this.notifyService.hideloading()
      this.total = res[1]
      this.listOfData = res[0]
    })
  }

  pageIndexChange() {
    this.router.navigate([`home`], {
      queryParams: {
        serviceId: this.serviceId || '',
        page: this.page,
        pageSize: this.pageSize,
      },
    })
  }

  pageSizeChange() {
    this.router.navigate([`home`], {
      queryParams: {
        serviceId: this.serviceId || '',
        page: this.page,
        pageSize: this.pageSize,
      },
    })
  }

  viewDetail(item: any) {
    if (item.isAllowViewDetail) {
      this.router.navigate(['/detail'], {
        queryParams: { bidid: item.id },
      })
    }
  }
}
