<nz-row nzType="flex" nzAlign="middle" *ngIf="!service?.serviceId">
  <nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="12" nzXl="12">
    <h1>{{ language_key?.SupplierAdditionalCapacity_SuppliersAddCapacity || "Nhà cung cấp bổ sung năng lực:"}}</h1>
  </nz-col>
  <nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="12" nzXl="12">
    <nz-input-group nzCompact class="group-cascader">
      <nz-cascader [nzShowSearch]="true" [nzOptions]="cascaderDataOptions" [(ngModel)]="cascaderData"
        [nzPlaceHolder]="language_key?.SupplierAdditionalCapacity_SelectBusinessField || '<PERSON><PERSON><PERSON> lĩnh vực kinh doanh'"></nz-cascader>
      <button nz-button type="button" nzType="primary" (click)="addService()">
        <span nz-icon nzType="plus"></span>
      </button>
    </nz-input-group>
  </nz-col>
</nz-row>

<nz-collapse class="ant-bg-child mt-2" *ngFor="let itemService of lstService; index as i;">
  <nz-collapse-panel [nzHeader]="language_key?.SupplierAdditionalCapacity_ProvidingCapacityInformation || 'Cung cấp thông tin năng lực: ' + itemService.serviceName" nzActive="true"
    [nzExtra]="extraTpl">
    <ng-template #extraTpl>
      <button [nzTooltipTitle]="language_key?.SupplierAdditionalCapacity_DownloadTemplate || 'Tải template'" nz-button nz-tooltip (click)="clickExportExcel($event,itemService)">
        <span nz-icon nzType="download"></span>
      </button>
      <input class="hidden" type="file" [id]="'file'+i" (change)="clickImportExcel($event,itemService)"
        onclick="this.value=null"
        accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
      <label [nzTooltipTitle]="language_key?.SupplierAdditionalCapacity_UploadExcel || 'Upload Excel'" [for]="'file'+i" nz-button nz-tooltip class="icon-upload">
        <span nz-icon nzType="upload"></span>
      </label>
      <button [nzTooltipTitle]="language_key?.SupplierAdditionalCapacity_Delete || 'Xóa'" nz-button nz-tooltip (click)="deleteService(i)" class="icon-delete">
        <span nz-icon nzType="delete"></span>
      </button>
    </ng-template>

    <nz-row nzGutter="2">
      <nz-table nz-col nzSpan="24" [nzData]="itemService.serviceCapacity" [(nzPageSize)]="pageSize"
        [nzShowPagination]="false" nzBordered>
        <thead>
          <tr>
            <th>{{ language_key?.SupplierAdditionalCapacity_Criteria || "Tiêu chí"}}</th>
            <th>{{ language_key?.SupplierAdditionalCapacity_DataType || "Kiểu dữ liệu"}}</th>
            <th>{{ language_key?.SupplierAdditionalCapacity_Value || "Giá trị"}}</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let data1 of itemService.serviceCapacity">
            <tr>
              <td class="w-50">
                <span *ngIf="data1.isRequired" class="text-danger">*</span>
                <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
              </td>
              <td>{{data1.type}}</td>
              <td (click)="resetError(data1)">
                <div *ngIf="data1.__childs__.length == 0">
                  <div *ngIf="!data1.isChangeByYear">
                    <div *ngIf="data1.type === dataType.String.code">
                      <input nz-input [(ngModel)]="data1.value" name="value" />
                    </div>
                    <div *ngIf="data1.type === dataType.Number.code">
                      <input nz-input currencyMask [(ngModel)]="data1.value" name="value" />
                    </div>
                    <div *ngIf="data1.type === dataType.Date.code">
                      <nz-date-picker [(ngModel)]="data1.value" name="value"> </nz-date-picker>
                    </div>
                    <div *ngIf="data1.type === dataType.List.code">
                      <nz-select nzShowSearch nzAllowClear [(ngModel)]="data1.value" name="value">
                        <nz-option *ngFor="let itemList of data1.__serviceCapacityListDetails__"
                          [nzLabel]="itemList.name" [nzValue]="itemList.id"></nz-option>
                      </nz-select>
                    </div>
                    <div *ngIf="data1.type === dataType.File.code">
                      <nz-input-group nzSearch>
                        <input class="passenger-input" nz-input [(ngModel)]="data1.value" name="value" disabled />
                        <button nz-button nzType="primary" (click)="handleClearFile(data1)">
                          <span nz-icon nzType="delete"></span>
                        </button>
                      </nz-input-group>
                      <label [for]="'zen' + data1.id" class="custom-file-upload">
                        <span nz-icon nzType="upload"></span> {{ language_key?.SupplierAdditionalCapacity_UploadFile || "Upload File"}}
                      </label>
                      <input class="hidden" type="file" [id]="'zen' + data1.id"
                        (change)="handleFileInput1($event, data1)" />
                    </div>
                  </div>
                  <div *ngIf="data1.isChangeByYear">
                    <div *ngFor="let detailYear of data1.listDetailYear; index as i1;" nz-col nzSpan="24"
                      class="form-item">
                      <nz-input-group nzCompact class="change-by-year-item">
                        <input nz-input [placeholder]="language_key?.SupplierAdditionalCapacity_Year || 'Năm'" [(ngModel)]="detailYear.year"
                          [ngModelOptions]="{standalone: true}" class="year" />
                        <input *ngIf="data1.type === dataType.String.code" nz-input [(ngModel)]="detailYear.value"
                          [ngModelOptions]="{standalone: true}" class="value" [placeholder]="language_key?.SupplierAdditionalCapacity_Value || 'Giá trị'" />
                        <input *ngIf="data1.type === dataType.Number.code" nz-input currencyMask [placeholder]="language_key?.SupplierAdditionalCapacity_Value || 'Giá trị'"
                          [(ngModel)]="detailYear.value" [ngModelOptions]="{standalone: true}" class="value" />

                        <input *ngIf="data1.type === dataType.File.code" nz-input [placeholder]="language_key?.SupplierAdditionalCapacity_ChooseFile || 'Chọn file'" disabled
                          [ngModelOptions]="{standalone: true}" class="value" [(ngModel)]="detailYear.value" />
                        <button nz-button type="button" nzType="primary" (click)="deleteChangeByYear(data1, i1)">
                          <span nz-icon nzType="minus"></span>
                        </button>
                        <label *ngIf="data1.type === dataType.File.code" [for]="'zen' + data1.id + i1"
                          class="custom-file-upload">
                          <span nz-icon nzType="upload"></span> {{ language_key?.SupplierAdditionalCapacity_UploadFile || "Upload File"}}
                        </label>
                        <input *ngIf="data1.type === dataType.File.code" class="hidden" type="file"
                          [id]="'zen' + data1.id + i1" (change)="handleFileInput1($event, detailYear)">

                      </nz-input-group>
                    </div>
                    <nz-col nzSpan="24" class="form-item">
                      <button nz-button type="button" nzType="dashed" class="add-change-by-year"
                        (click)="addChangeByYear(data1)">
                        <span nz-icon nzType="plus"></span>
                        {{ language_key?.SupplierAdditionalCapacity_AddValue || "Thêm giá trị"}}
                      </button>
                    </nz-col>
                  </div>
                </div>

                <span *ngIf="data1.isError" class="text-danger">{{data1.errorText}}</span>
              </td>
            </tr>
            <ng-container>
              <tr *ngFor="let data2 of data1.__childs__">
                <td [nzIndentSize]="20" class="w-50">
                  <span *ngIf="data2.isRequired" class="text-danger">*</span>
                  <span nz-tooltip [nzTooltipTitle]="data2.name">
                    {{ data2.name }}</span>
                </td>
                <td>{{data2.type}}</td>
                <td (click)="resetError(data2)">
                  <div *ngIf="!data2.isChangeByYear">
                    <div *ngIf="data2.type === dataType.String.code">
                      <input nz-input [(ngModel)]="data2.value" name="value" />
                    </div>
                    <div *ngIf="data2.type === dataType.Number.code">
                      <input nz-input currencyMask [(ngModel)]="data2.value" name="value" />
                    </div>
                    <div *ngIf="data2.type === dataType.Date.code">
                      <nz-date-picker [(ngModel)]="data2.value" name="value"> </nz-date-picker>
                    </div>
                    <div *ngIf="data2.type === dataType.List.code">
                      <nz-select nzShowSearch nzAllowClear [(ngModel)]="data2.value" name="value">
                        <nz-option *ngFor="let itemList of data2.__serviceCapacityListDetails__"
                          [nzLabel]="itemList.name" [nzValue]="itemList.id"></nz-option>
                      </nz-select>
                    </div>
                    <div *ngIf="data2.type === dataType.File.code">
                      <nz-input-group nzSearch>
                        <input class="passenger-input" nz-input [(ngModel)]="data2.value" name="value" disabled />
                        <button nz-button nzType="primary" (click)="handleClearFile(data2)">
                          <span nz-icon nzType="delete"></span>
                        </button>
                      </nz-input-group>
                      <label [for]="'zen' + data2.id" class="custom-file-upload">
                        <span nz-icon nzType="upload"></span> {{ language_key?.SupplierAdditionalCapacity_UploadFile || "Upload File"}}
                      </label>
                      <input class="hidden" type="file" [id]="'zen' + data2.id"
                        (change)="handleFileInput1($event, data2)" />
                    </div>
                  </div>
                  <div *ngIf="data2.isChangeByYear">
                    <nz-col nzSpan="24" *ngFor="let detailYear of data2.listDetailYear; index as i2;" class="form-item">
                      <nz-input-group nzCompact class="change-by-year-item">
                        <input nz-input [placeholder]="language_key?.SupplierAdditionalCapacity_Year || 'Năm'" [(ngModel)]="detailYear.year"
                          [ngModelOptions]="{standalone: true}" class="year" />
                        <input *ngIf="data2.type === dataType.String.code" nz-input [(ngModel)]="detailYear.value"
                          [ngModelOptions]="{standalone: true}" class="value" [placeholder]="language_key?.SupplierAdditionalCapacity_Value || 'Giá trị'" />
                        <input *ngIf="data2.type === dataType.Number.code" nz-input currencyMask [placeholder]="language_key?.SupplierAdditionalCapacity_Value || 'Giá trị'"
                          [(ngModel)]="detailYear.value" [ngModelOptions]="{standalone: true}" class="value" />

                        <input *ngIf="data2.type === dataType.File.code" nz-input [placeholder]="language_key?.SupplierAdditionalCapacity_ChooseFile || 'Chọn file'" disabled
                          [ngModelOptions]="{standalone: true}" class="value" [(ngModel)]="detailYear.value" />
                        <button nz-button type="button" nzType="primary" (click)="deleteChangeByYear(data2, i2)">
                          <span nz-icon nzType="minus"></span>
                        </button>
                        <label *ngIf="data2.type === dataType.File.code" [for]="'zen' + data2.id + i2"
                          class="custom-file-upload">
                          <span nz-icon nzType="upload"></span> {{ language_key?.SupplierAdditionalCapacity_UploadFile || "Upload File"}}
                        </label>
                        <input *ngIf="data2.type === dataType.File.code" class="hidden" type="file"
                          [id]="'zen' + data2.id + i2" (change)="handleFileInput1($event, detailYear)">

                      </nz-input-group>
                    </nz-col>
                    <nz-col nzSpan="24" class="form-item">
                      <button nz-button type="button" nzType="dashed" class="add-change-by-year"
                        (click)="addChangeByYear(data2)">
                        <span nz-icon nzType="plus"></span>
                        {{ language_key?.SupplierAdditionalCapacity_AddValue || "Thêm giá trị"}}
                      </button>
                    </nz-col>
                  </div>
                  <span *ngIf="data2.isError" class="text-danger">{{data2.errorText}}</span>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </nz-table>
    </nz-row>
  </nz-collapse-panel>
</nz-collapse>

<nz-row class="mt-3">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button nzType="primary" (click)="saveSupplier()" [disabled]="lstService.length == 0">{{ language_key?.SupplierAdditionalCapacity_Save || "Lưu"}}</button>
  </nz-col>
</nz-row>