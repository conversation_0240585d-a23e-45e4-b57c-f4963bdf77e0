import { Component, OnInit } from '@angular/core'
import { Params, ActivatedRoute, Router } from '@angular/router'
import { ApiService, NotifyService } from '../../services'
import { enumData } from '../../base/enumData'
import * as XLSX from 'xlsx'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  selector: 'app-offer-deal',
  templateUrl: './offer-deal.component.html',
  styleUrls: ['./offer-deal.component.scss'],
})
export class OfferDealComponent implements OnInit {
  bidDealId = ''
  data: any = {}

  mpoType = enumData.ColType.MPO.code
  supType = enumData.ColType.Supplier.code

  /** <PERSON>ích thước tối đa tính bằng MB */
  maxSizeUpload = enumData.maxSizeUpload

  checkResultMessage!: { check: boolean; message: string }
  isExporting = false
  dicActiveCollapse: any = {}
  language_key: any = {}

  constructor(
    private route: ActivatedRoute,
    private languageService: LanguageService,
    private router: Router,
    private apiService: ApiService,
    private notifyService: NotifyService
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.bidDealId = params['biddealid']
    })
  }

  ngOnInit() {
    this.getBidDeal()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  getBidDeal() {
    this.notifyService.showloading()
    this.data.isDisplayBtnSavePrice = false
    this.apiService.get(this.apiService.CLIENT_WEB.GET_OFFER_DEAL(this.bidDealId), {}).then((res) => {
      this.notifyService.hideloading()
      this.data = res
      if (!this.data.isDisplayBtn && !this.data.isDisplayBtnSavePrice) {
        this.loadMessage()
      }
    })
  }

  loadMessage() {
    this.apiService.get(this.apiService.CLIENT_WEB.CHECK_RESULT_MESSAGE_OFFER(this.bidDealId), {}).then((data) => {
      this.checkResultMessage = data
    })
  }

  /** Đề nghị đàm phán giá/ Chấp nhận giá đề nghị/ Lưu */
  confirmAccept(flag = false) {
    this.notifyService.showloading()
    let strErr = ''
    for (const child of this.data.listChild) {
      if (!child.filePriceDetail) {
        strErr += this.language_key?.BidDeal_DetailFileNotUpload || `Chưa upload File chi tiết giá.<br>`
      }
      if (this.data.isRequireFileTechDetail && !child.fileTechDetail) {
        strErr += this.language_key?.BidDeal_TechnicalDetailFileNotUpload || `Chưa upload File chi tiết kỹ thuật.<br>`
      }
      for (const item of child.lstDealPrice) {
        if (this.data.isDisplayBtnSavePrice) {
          if (item.dealPrice == null || item.dealPrice === '') {
            strErr +=
              (this.language_key?.BidDeal_Index || 'Hạng mục') +
              `[${item.__bidPrice__.name}]` +
              (this.language_key?.BidDeal_DealPriceNotInput || `chưa nhập giá đàm phán.<br>`)
          }
        }
        if (item.dealPrice != null && item.dealPrice <= 0) {
          strErr +=
            (this.language_key?.BidDeal_Index || 'Hạng mục') +
            `[${item.__bidPrice__.name}]` +
            (this.language_key?.BidDeal_DealPriceMustGreaterThanZero || `cần chỉnh giá lớn hơn 0.<br>`)
        }
        if (item.offerPrice != null && item.dealPrice > item.offerPrice) {
          strErr +=
            (this.language_key?.BidDeal_Index || 'Hạng mục') +
            `[${item.__bidPrice__.name}]` +
            (this.language_key?.BidDeal_DealPriceMustNotExceedAgreePrice || `cần chỉnh giá đàm phán không vượt quá giá đã chào.<br>`)
        }
        if (item.maxPrice != null && item.dealPrice > item.maxPrice) {
          strErr +=
            (this.language_key?.BidDeal_Index || 'Hạng mục') +
            `[${item.__bidPrice__.name}]` +
            (this.language_key?.BidDeal_DealPriceMustNotExceedMaximumPrice || `cần chỉnh giá đàm phán không vượt quá giá tối đa.<br>`)
        }
      }
      if (this.data.isDisplayBtn) {
        // Đề nghị đàm phán giá
        if (flag) {
          // Nếu không có dòng nào nhập giá thì báo lỗi
          if (!child.lstDealPrice.some((c: any) => c.dealPrice > 0)) {
            strErr += this.language_key?.BidDeal_DealPriceNotInputCannotSave || `Chưa nhập giá đàm phán, không thể lưu.<br>`
          }
        } else {
          // Nếu có dòng nào đó nhập giá thì báo lỗi
          if (child.lstDealPrice.some((c: any) => c.dealPrice > 0)) {
            strErr +=
              this.language_key?.BidDeal_DealPriceInputAllowToActionDealPrice || `Đã nhập giá đàm phán, vui lòng thao tác "Đề nghị đàm phán giá".<br>`
          }
        }
      }
    }

    if (strErr.length > 0) {
      this.notifyService.showError(strErr)
      return
    }
    this.apiService.post(this.apiService.CLIENT_WEB.ACCEPT_OFFER, this.data).then((res) => {
      this.notifyService.hideloading()
      this.getBidDeal()
    })
  }

  confirmReject() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CLIENT_WEB.REJECT_OFFER, { bidDealId: this.bidDealId }).then((res) => {
      this.notifyService.hideloading()
      this.getBidDeal()
    })
  }

  gotoHomePage() {
    this.router.navigate([`home`])
  }

  clickExportExcel(i: any) {
    this.isExporting = true

    const itemName = this.data.listChild[i].itemName
    setTimeout(() => {
      const tbl = document.getElementById('test-html-table' + i)
      const wb = XLSX.utils.table_to_book(tbl)

      const fileName = (this.language_key?.BidDeal_TemplateInputDealPriceItem || 'Template nhập đàm phán giá Item') + ` [${itemName}].xlsx`
      /* save to file */
      XLSX.writeFile(wb, fileName)
      this.isExporting = false
    }, 100)
  }

  clickImportExcel(ev: any, item: any) {
    let workBook: any = null
    let jsonData: any = null
    const lstColId = item.lstPriceCol.map((c: any) => c.id)
    let lstHeader = ['id', 'name', ...lstColId, 'unit', 'currency', 'number', 'offerPrice', 'maxPrice', 'suggestPrice', 'dealPrice']
    if (this.data.isDisplayBtnSavePrice) {
      lstHeader = ['id', 'name', ...lstColId, 'unit', 'currency', 'number', 'offerPrice', 'maxPrice', 'dealPrice']
    }
    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = async (): Promise<any> => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      // bỏ dòng đầu tiên
      let isErr = false
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== (this.language_key?.BidDeal_IndexName || 'Tên hạng mục') ||
        header.unit !== (this.language_key?.BidDeal_CountUnit || 'Đơn vị tính') ||
        header.currency !== (this.language_key?.BidDeal_CurrencyUnit || 'Đơn vị tiền tệ') ||
        header.number !== (this.language_key?.BidDeal_Quantity || 'Số lượng') ||
        header.maxPrice !== (this.language_key?.BidDeal_MaximumPrice || 'Giá tối đa') ||
        header.history !== (this.language_key?.BidDeal_InputedValue || 'Giá trị đã nhập') ||
        header.value !== (this.language_key?.BidDeal_UnitPrice || 'Đơn giá')
      ) {
        isErr = true
      }
      if (!this.data.isDisplayBtnSavePrice && header.suggestPrice !== (this.language_key?.BidDeal_SuggestedPrice || 'Giá đề nghị')) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError(this.language_key?.BidDeal_FileTemplateNotCorrect || 'File không đúng template nhập đàm phán giá của gói thầu')
        return false
      }

      for (const row of jsonData) {
        const itemNew = item.lstDealPrice.find((c: any) => c.bidPriceId === row.id)
        if (itemNew) {
          itemNew.dealPrice = row.dealPrice
        }
      }
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
    }
  }

  handleFileInput(event: any, field: string, item: any) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload && fileToUpload.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showWarning(
        (this.language_key?.BidDeal_FileExceedMaxSize || 'Vượt qua kích thước tối đa.') +
          `Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`
      )
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) item[field] = res[0]
        else item[field] = ''
      })
    }
  }
}
