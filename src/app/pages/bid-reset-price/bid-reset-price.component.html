<div *ngIf="bidid && !isSuccess && !isError">
  <br />
  <nz-row class="mt-3">
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="filePriceDetail" class="text-left" [nzRequired]="true">{{
          language_key?.BidResetPrice_DetailPriceFile || 'File chi tiết giá'
        }}</nz-form-label>
        <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.BidResetPrice_PleaseUploadDetailPriceFile || 'Vui lòng upload File chi tiết giá!'">
          <label for="filePriceDetail" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
          <input class="hidden" type="file" id="filePriceDetail" (change)="handleFileInput($event, 'filePriceDetail')" title="" />
          <div class="tooltip" *ngIf="data.filePriceDetail && data.filePriceDetail.length > 0">
            <a href="{{ data.filePriceDetail }}" target="_blank">{{ language_key?.BidResetPrice_ViewAttachedFile || 'Xem file đính kèm' }}</a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="fileTechDetail" class="text-left" [nzRequired]="bid.isRequireFileTechDetail">
          {{ language_key?.BidResetPrice_TechincalDetailFile || 'File chi tiết kỹ thuật' }} {{ bid.isRequireFileTechDetail ? '' : '(Nếu có)' }}
        </nz-form-label>
        <nz-form-control
          nzSpan="24"
          [nzErrorTip]="language_key?.BidResetPrice_PleaseUploadTechnicalDetailFile || 'Vui lòng upload File chi tiết kỹ thuật!'"
        >
          <label for="fileTechDetail" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
          <input class="hidden" type="file" id="fileTechDetail" (change)="handleFileInput($event, 'fileTechDetail')" title="" />
          <div class="tooltip" *ngIf="data.fileTechDetail && data.fileTechDetail.length > 0">
            <a href="{{ data.fileTechDetail }}" target="_blank">{{ language_key?.BidResetPrice_ViewAttachedFile || 'Xem file đính kèm' }}</a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <nz-row class="mt-3">
    <button class="mr-2" nz-button (click)="clickExportExcel()">
      <span nz-icon nzType="download"></span>{{ language_key?.BidResetPrice_ExportExcel || 'Xuất excel' }}
    </button>
    <input
      class="hidden"
      type="file"
      id="file"
      (change)="clickImportExcel($event)"
      onclick="this.value=null"
      accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    />
    <label nz-button for="file" class="lable-custom-file">
      <span nz-icon nzType="upload"></span>{{ language_key?.BidResetPrice_ImportExcel || 'Nhập excel' }}</label
    >
  </nz-row>
  <nz-row class="mt-2">
    <nz-table
      nz-col
      nzSpan="24"
      [nzScroll]="{ x: '1250px' }"
      id="test-html-table"
      #basicTable
      nzBordered
      [nzData]="listOfData"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzShowPagination]="false"
    >
      <thead>
        <tr>
          <th class="hidden">ID</th>
          <th>{{ language_key?.BidResetPrice_IndexName || 'Tên hạng mục' }}</th>
          <th
            *ngFor="let col of bidPriceCol"
            [ngClass]="{ 'dynamic-col-mpo': col.colType === mpoType, 'dynamic-col-supplier': col.colType === supType }"
          >
            {{ col.name }}
          </th>
          <th class="text-nowrap">{{ language_key?.BidResetPrice_CountUnit || 'Đơn vị tính' }}</th>
          <th class="text-nowrap">{{ language_key?.BidResetPrice_CurrencyUnit || 'Đơn vị tiền tệ' }}</th>
          <th class="text-nowrap">{{ language_key?.BidResetPrice_Quantity || 'Số lượng' }}</th>
          <th class="mw-20 miw-15" *ngIf="!isExporting || !hasFomularValue">{{ language_key?.BidResetPrice_UnitPrice || 'Đơn giá' }}</th>
          <th *ngIf="!isExporting">{{ language_key?.BidResetPrice_DetailInformation || 'Thông tin chi tiết' }}</th>
        </tr>
      </thead>
      <tbody>
        <!-- level 1 -->
        <ng-container *ngFor="let data1 of basicTable.data">
          <tr>
            <td class="hidden">{{ data1.id }}</td>
            <td class="mw-20 miw-15">
              <span *ngIf="data1.isRequired" class="text-danger">*</span>
              <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
            </td>
            <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
              <span *ngIf="col.colType === mpoType && (col.fomular == null || col.fomular.length === 0)">{{ data1[col.id] }}</span>
              <span *ngIf="col.type === numberType && col.fomular?.length > 0">{{ data1[col.id] | number }}</span>
              <input
                *ngIf="col.colType === supType && col.type === numberType && (col.fomular == null || col.fomular.length === 0)"
                nz-input
                currencyMask
                [(ngModel)]="data1[col.id]"
                (ngModelChange)="onChangeCalFomular(data1)"
              />
              <input *ngIf="col.colType === supType && col.type === stringType" nz-input [(ngModel)]="data1[col.id]" />
            </td>
            <td class="mw-25">{{ data1.unit }}</td>
            <td class="mw-25">{{ data1.currency }}</td>
            <td class="mw-25 text-right">{{ data1.number | number }}</td>
            <td class="mw-20 miw-15" (click)="resetError(data1)">
              <input *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data1.value" name="value" placeholder="" />
              <span *ngIf="hasFomularValue">{{ data1.value | number }}</span>
              <span *ngIf="data1.isError" class="text-danger">{{ data1.errorText }}</span>
            </td>
            <td class="miw-15" *ngIf="!isExporting">
              <button
                nz-tooltip
                [nzTooltipTitle]="language_key?.BidResetPrice_DetailInformation || 'Thông tin chi tiết'"
                class="mr-2"
                nz-button
                *ngIf="data1.__bidPriceListDetails__?.length"
                nzType="dashed"
                (click)="viewDetail(data1)"
              >
                <span nz-icon nzType="exclamation-circle"></span>
              </button>
            </td>
          </tr>
          <!-- level 2 -->
          <ng-container *ngFor="let data2 of data1.__childs__">
            <tr>
              <td class="hidden">{{ data2.id }}</td>
              <td [nzIndentSize]="5" class="mw-20 miw-15">
                <span *ngIf="data2.isRequired" class="text-danger">*</span>
                <span nz-tooltip [nzTooltipTitle]="data2.name">{{ data2.name }}</span>
              </td>
              <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
                <span *ngIf="col.colType === mpoType && (col.fomular == null || col.fomular.length === 0)">{{ data2[col.id] }}</span>
                <span *ngIf="col.type === numberType && col.fomular?.length > 0">{{ data2[col.id] | number }}</span>
                <input
                  *ngIf="col.colType === supType && col.type === numberType && (col.fomular == null || col.fomular.length === 0)"
                  nz-input
                  currencyMask
                  [(ngModel)]="data2[col.id]"
                  (ngModelChange)="onChangeCalFomular(data2)"
                />
                <input *ngIf="col.colType === supType && col.type === stringType" nz-input [(ngModel)]="data2[col.id]" />
              </td>
              <td class="mw-25">{{ data2.unit }}</td>
              <td class="mw-25">{{ data2.currency }}</td>
              <td class="mw-25 text-right">{{ data2.number | number }}</td>
              <td class="mw-20 miw-15" (click)="resetError(data2)">
                <input *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data2.value" name="value" placeholder="" />
                <span *ngIf="hasFomularValue">{{ data2.value | number }}</span>
                <span *ngIf="data2.isError" class="text-danger">{{ data2.errorText }}</span>
              </td>
              <td class="miw-15" *ngIf="!isExporting">
                <button
                  nz-tooltip
                  [nzTooltipTitle]="language_key?.BidResetPrice_DetailInformation || 'Thông tin chi tiết'"
                  class="mr-2"
                  nz-button
                  *ngIf="data2.__bidPriceListDetails__?.length"
                  nzType="dashed"
                  (click)="viewDetail(data2)"
                >
                  <span nz-icon nzType="exclamation-circle"></span>
                </button>
              </td>
            </tr>
            <!-- level 3 -->
            <ng-container *ngFor="let data3 of data2.__childs__">
              <tr>
                <td class="hidden">{{ data3.id }}</td>
                <td [nzIndentSize]="30" class="mw-20 miw-15">
                  <span *ngIf="data3.isRequired" class="text-danger">*</span>
                  <span nz-tooltip [nzTooltipTitle]="data3.name">{{ data3.name }}</span>
                </td>
                <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
                  <span *ngIf="col.colType === mpoType && (col.fomular == null || col.fomular.length === 0)">{{ data3[col.id] }}</span>
                  <span *ngIf="col.type === numberType && col.fomular?.length > 0">{{ data3[col.id] | number }}</span>
                  <input
                    *ngIf="col.colType === supType && col.type === numberType && (col.fomular == null || col.fomular.length === 0)"
                    nz-input
                    currencyMask
                    [(ngModel)]="data3[col.id]"
                    (ngModelChange)="onChangeCalFomular(data3)"
                  />
                  <input *ngIf="col.colType === supType && col.type === stringType" nz-input [(ngModel)]="data3[col.id]" />
                </td>
                <td class="mw-25">{{ data3.unit }}</td>
                <td class="mw-25">{{ data3.currency }}</td>
                <td class="mw-25 text-right">{{ data3.number | number }}</td>
                <td class="mw-20 miw-15" (click)="resetError(data3)">
                  <input *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data3.value" name="value" placeholder="" />
                  <span *ngIf="hasFomularValue">{{ data3.value | number }}</span>
                  <span *ngIf="data3.isError" class="text-danger">{{ data3.errorText }}</span>
                </td>
                <td class="miw-15" *ngIf="!isExporting">
                  <button
                    nz-tooltip
                    [nzTooltipTitle]="language_key?.BidResetPrice_DetailInformation || 'Thông tin chi tiết'"
                    class="mr-2"
                    nz-button
                    *ngIf="data3.__bidPriceListDetails__?.length"
                    nzType="dashed"
                    (click)="viewDetail(data3)"
                  >
                    <span nz-icon nzType="exclamation-circle"></span>
                  </button>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-row>
  <nz-row [nzGutter]="2" *ngIf="listOfData.length == 0" class="text-danger mt-3"
    ><i>{{ language_key?.BidResetPrice_RequestAdditionalPrice || 'Yêu cầu bảng giá bổ sung sau' }}</i></nz-row
  >
  <nz-row class="mt-3 text-center">
    <button nz-button nzType="primary" (click)="savePrice()">
      <span>{{ language_key?.BidResetPrice_SubmitAdditionalPrice || 'Nộp chào giá bổ sung' }}</span>
    </button>
  </nz-row>
</div>

<app-bidding-price-list-detail-modal #biddingPriceListDetail> </app-bidding-price-list-detail-modal>

<div *ngIf="isError">
  <nz-result
    nzStatus="error"
    [nzTitle]="
      language_key?.BidResetPrice_NoAccessGrantOrTimeOutForPackage || 'Bạn không có quyền truy cập hoặc đã ngưng nộp giá bổ sung cho gói thầu.'
    "
  >
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.BidResetPrice_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
</div>

<div *ngIf="isSuccess">
  <nz-result nzStatus="success" [nzTitle]="language_key?.BidResetPrice_SubmitAdditionalSuccessfully || 'giá bổ sung thành công.'">
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.BidResetPrice_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
</div>
