import { Component, OnInit, ViewChild } from '@angular/core'
import { ActivatedRoute, Params, Router } from '@angular/router'
import { NzModalService } from 'ng-zorro-antd/modal'
import { enumData } from 'src/app/base/enumData'
import { ApiService, CoreService, NotifyService } from 'src/app/services'
import { BiddingPriceListDetailModalComponent } from '../modal/bidding-price-list-detail-modal/bidding-price-list-detail-modal.component'
import * as XLSX from 'xlsx'
import { LanguageService } from 'src/app/services/language.service'

@Component({ templateUrl: './bid-reset-price.component.html' })
export class BidResetPriceComponent implements OnInit {
  bidPriceCol: any[] = []
  bidPriceColId: any[] = []
  isExporting = false
  bidid = ''
  bid: any
  isError = false
  isSuccess = false
  data: any
  language_key: any = {}
  maxSizeUpload = enumData.maxSizeUpload
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private route: ActivatedRoute,
    private modal: NzModalService,
    private router: Router,
    private languageService: LanguageService
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.bidid = params['bidid']
    })
  }
  listOfData: any[] = []

  pageSize = enumData.Page.pageSizeMax
  loading = false
  hasFomularValue = false
  mpoType = enumData.ColType.MPO.code
  supType = enumData.ColType.Supplier.code
  stringType = enumData.DataType.String.code
  numberType = enumData.DataType.Number.code
  @ViewChild('biddingPriceListDetail')
  biddingPriceListDetail!: BiddingPriceListDetailModalComponent

  ngOnInit() {
    this.checkPermission()
    this.data = new Object()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  /** Kiểm tra quyền được bổ sung bảng chào giá mới */
  checkPermission = () => {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CLIENT_WEB.CHECK_PERMISSION_JOIN_RESET_PRICE, { bidId: this.bidid }).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        this.loadData()
      } else {
        this.isError = true
      }
    })
  }

  loadData = () => {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CLIENT_WEB.LOAD_DATA_BID_PRICE, { bidId: this.bidid }).then((res) => {
      this.notifyService.hideloading()
      this.listOfData = res[0]
      this.bidPriceCol = res[1]
      this.bid = res[2]
      if (this.bid.fomular && this.bid.fomular.length > 0) {
        this.hasFomularValue = true
      }
      if (this.listOfData.length > 0) {
        for (const data1 of this.listOfData) {
          data1.bidPriceId = data1.id
          data1.__childs__ = data1.__childs__.sort((a: any, b: any) => a.sort - b.sort)
          for (const data2 of data1.__childs__) {
            data2.bidPriceId = data2.id
            data2.__childs__ = data2.__childs__.sort((a: any, b: any) => a.sort - b.sort)
            for (const data3 of data2.__childs__) data3.bidPriceId = data3.id
          }
        }
      }
    })
  }

  getDataSave = () => {
    return this.listOfData
  }

  getCheckFomular = () => {
    return this.hasFomularValue
  }

  getDynamicColRequired = () => {
    const lstDynamicColRequired = this.bidPriceCol.filter(
      (c) => c.colType === enumData.ColType.Supplier.code && c.isRequired === true && (c.fomular == null || c.fomular.length === 0)
    )
    return lstDynamicColRequired
  }

  viewDetail(rowData: any) {
    this.biddingPriceListDetail.showModal(rowData)
  }

  resetError(rowData: any) {
    rowData.isError = false
    rowData.errorText = ''
  }
  clickExportExcel() {
    this.loading = true
    this.isExporting = true
    let lstColTemp = [...this.bidPriceCol]

    this.bidPriceCol = this.bidPriceCol.filter((c) => c.fomular == null || c.fomular.length === 0)

    setTimeout(() => {
      const tbl = document.getElementById('test-html-table')
      
      if (tbl) {
        const tempTable = tbl.cloneNode(true) as HTMLElement

        const inputs = tbl.querySelectorAll('input')

        const tempInputs = tempTable.querySelectorAll('input')

        tempInputs.forEach((input, index) => {
            const tempCell = input.closest('td')
            if (tempCell) {
                tempCell.textContent = inputs[index].value
            }
        })

        const wb = XLSX.utils.table_to_book(tempTable)
        const fileName = (this.language_key?.BidResetPrice_TemplateSuggestPrice || 'Template nhập bảng chào giá') + '.xlsx'
        XLSX.writeFile(wb, fileName)
      }

      this.bidPriceCol = [...lstColTemp]
      
      setTimeout(() => {
        this.isExporting = false
        this.loading = false
      }, 200)
    }, 200)
}

  clickImportExcel(ev: any) {
    let workBook = null
    let jsonData: any = null
    // lấy những cột không có cthuc
    const lstBidPriceColId = this.bidPriceCol.filter((c) => c.fomular == null || c.fomular.length === 0).map((c) => c.id)
    const lstHeader = ['id', 'name', ...lstBidPriceColId, 'unit', 'currency', 'number']
    if (!this.hasFomularValue) {
      lstHeader.push('value')
    }

    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = async (): Promise<any> => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      // bỏ dòng đầu tiên
      let isErr = false
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== (this.language_key?.BidResetPrice_IndexName || 'Tên hạng mục') ||
        header.unit !== (this.language_key?.BidResetPrice_CountUnit || 'Đơn vị tính') ||
        header.currency !== (this.language_key?.BidResetPrice_CurrencyUnit || 'Đơn vị tiền tệ') ||
        header.number !== (this.language_key?.BidResetPrice_Quantity || 'Số lượng') ||
        (!this.hasFomularValue && header.value !== (this.language_key?.BidResetPrice_UnitPrice || 'Đơn giá'))
      ) {
        isErr = true
      }

      if (isErr) {
        this.notifyService.showError(this.language_key?.BidResetPrice_FileTemplateNotCorrect || 'File không đúng template chào giá của gói thầu')
        return false
      }

      let strErr = ''
      const lstDynamicColTypeSup = this.bidPriceCol.filter((c) => c.colType === this.supType && (c.fomular == null || c.fomular.length === 0))
      const lstDynamicColFomular = this.bidPriceCol.filter((c) => c.fomular?.length > 0)
      for (const row of jsonData) {
        if (!this.hasFomularValue) {
          if (row.isRequired && (row.value == null || row.value == '')) {
            strErr += 'Hạng mục [' + row.name + '] cột [Đơn giá] không được để trống<br>'
          } else if (row.value && typeof row.value !== 'number') {
            strErr += 'Nhập số tiền cho hạng mục [' + row.name + '] cột [Đơn giá]<br>'
          }
        }

        for (const col of lstDynamicColTypeSup) {
          if (col.isRequired && (row[col.id] == null || row[col.id] == '')) {
            strErr += 'Hạng mục [' + row.name + '] cột [' + col.name + '] không được để trống<br>'
          } else if (row[col.id]) {
            if (col.type === this.numberType && typeof row[col.id] !== 'number') {
              strErr += 'Nhập số cho hạng mục [' + row.name + '] cột [' + col.name + ']<br>'
            }
          }
        }
      }
      if (strErr.length > 0) {
        this.notifyService.showError(strErr)
        return false
      }

      if (this.listOfData.length > 0) {
        for (const data1 of this.listOfData) {
          const json1 = jsonData.find((c: any) => c.id === data1.id)
          if (json1) {
            data1.value = json1.value
            for (const col of lstDynamicColTypeSup) data1[col.id] = json1[col.id]
          }

          for (const data2 of data1.__childs__) {
            const json2 = jsonData.find((c: any) => c.id === data2.id)
            if (json2) {
              data2.value = json2.value
              for (const col of lstDynamicColTypeSup) data2[col.id] = json2[col.id]
            }
            for (const data3 of data2.__childs__) {
              const json3 = jsonData.find((c: any) => c.id === data3.id)
              if (json3) {
                data3.value = json3.value
                for (const col of lstDynamicColTypeSup) data3[col.id] = json3[col.id]
              }
            }
          }
        }
      }

      // tính công thức
      for (const data1 of this.listOfData) {
        for (const col of lstDynamicColFomular) {
          data1[col.id] = this.coreService.calFomular(col.fomular, this.bidPriceCol, data1)
        }
        if (this.hasFomularValue) data1.value = this.coreService.calFomular(this.bid.fomular, this.bidPriceCol, data1)

        for (const data2 of data1.__childs__) {
          for (const col of lstDynamicColFomular) {
            data2[col.id] = this.coreService.calFomular(col.fomular, this.bidPriceCol, data2)
          }
          if (this.hasFomularValue) data2.value = this.coreService.calFomular(this.bid.fomular, this.bidPriceCol, data2)

          for (const data3 of data2.__childs__) {
            for (const col of lstDynamicColFomular) {
              data3[col.id] = this.coreService.calFomular(col.fomular, this.bidPriceCol, data3)
            }
            if (this.hasFomularValue) data3.value = this.coreService.calFomular(this.bid.fomular, this.bidPriceCol, data3)
          }
        }
      }
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
    }
  }

  onChangeCalFomular(rowData: any) {
    this.loading = true
    setTimeout(() => {
      const lstDynamicColFomular = this.bidPriceCol.filter((c) => c.fomular?.length > 0)
      for (const col of lstDynamicColFomular) {
        rowData[col.id] = this.coreService.calFomular(col.fomular, this.bidPriceCol, rowData)
      }
      if (this.hasFomularValue) rowData.value = this.coreService.calFomular(this.bid.fomular, this.bidPriceCol, rowData)

      this.loading = false
    }, 10)
  }

  checkDynamicColRequired(row: any, lstDynamicColRequired: any[]) {
    for (const col of lstDynamicColRequired) {
      // Nếu chưa nhập thì báo lỗi
      if (row[col.id]) row[col.id] += ''
      if (row[col.id] == null || row[col.id] === '' || row[col.id].trim() === '') {
        return true
      }
    }

    return false
  }

  checkContent() {
    let step = ''
    let flag = true
    const hasFomularValue = this.getCheckFomular()
    const lstDynamicColRequired = this.getDynamicColRequired()
    const checkData = this.getDataSave()

    for (const data1 of checkData) {
      data1.isError = false
      data1.errorText = ''
      if (hasFomularValue) {
        // không kiểm tra require
      } else if (data1.isRequired && (data1.__childs__ == null || data1.__childs__.length === 0)) {
        if (!data1.value) {
          data1.isError = true
          data1.errorText = this.language_key?.BidResetPrice_PleaseInput || 'Vui lòng nhập dữ liệu trước'
          this.notifyService.showWarning(
            `${step} -` + (this.language_key?.BidResetPrice_NotFilledInformation || 'Chưa nhập dữ liệu hạng mục') + ` [${data1.name}]`
          )
          flag = false
        }
      }

      if (data1.value != null && data1.value <= 0) {
        data1.isError = true
        data1.errorText = this.language_key?.BidResetPrice_PleaseInputValueGreaterThanZero || 'Vui lòng nhập giá lớn hơn 0'
        this.notifyService.showWarning(
          `${step} - ` +
            (this.language_key?.BidResetPrice_Index || 'Hạng mục') +
            ` [${data1.name}]` +
            (this.language_key?.BidResetPrice_PriceGreaterThanZero || 'giá phải lớn hơn 0')
        )
        flag = false
      }

      if (hasFomularValue) {
        // không kiểm tra require
      } else {
        if (data1.__childs__ && data1.__childs__.length > 0) {
          for (const data2 of data1.__childs__) {
            data2.isError = false
            data2.errorText = ''
            if (data2.isRequired && (data2.__childs__ == null || data2.__childs__.length === 0)) {
              if (!data2.value) {
                data2.isError = true
                data2.errorText = this.language_key?.BidResetPrice_PleaseInput || 'Vui lòng nhập dữ liệu trước'
                this.notifyService.showWarning(
                  `${step} -` + (this.language_key?.BidResetPrice_NotFilledInformation || 'Chưa nhập dữ liệu hạng mục') + ` [${data2.name}]`
                )
                flag = false
              }
            }

            if (data2.__childs__ && data2.__childs__.length > 0) {
              for (const data3 of data2.__childs__) {
                data3.isError = false
                data3.errorText = ''
                if (data3.isRequired) {
                  if (!data3.value) {
                    data3.isError = true
                    data3.errorText = this.language_key?.BidResetPrice_PleaseInput || 'Vui lòng nhập dữ liệu trước'
                    this.notifyService.showWarning(
                      `${step} -` + (this.language_key?.BidResetPrice_NotFilledInformation || 'Chưa nhập dữ liệu hạng mục') + ` [${data3.name}]`
                    )
                    flag = false
                  }
                }
              }
            }
          }
        }
      }

      // Kiểm tra bắt buộc nhập thông tin cột động bảng chào giá
      if (lstDynamicColRequired && lstDynamicColRequired.length > 0) {
        if (this.checkDynamicColRequired(data1, lstDynamicColRequired)) {
          data1.isError = true
          data1.errorText = this.language_key?.BidResetPrice_PleaseInputRequiredColumn || 'Vui lòng điền đủ thông tin các cột động bắt buộc'
          this.notifyService.showWarning(
            `${step} -` + (this.language_key?.BidResetPrice_NotFilledEnoughInformation || 'Chưa điền đủ thông tin hạng mục') + `[${data1.name}]`
          )
          flag = false
        }
        for (const data2 of data1.__childs__) {
          if (this.checkDynamicColRequired(data2, lstDynamicColRequired)) {
            data2.isError = true
            data2.errorText = this.language_key?.BidResetPrice_PleaseInputRequiredColumn || 'Vui lòng điền đủ thông tin các cột động bắt buộc'
            this.notifyService.showWarning(
              `${step} ` + (this.language_key?.BidResetPrice_NotFilledEnoughInformation || 'Chưa điền đủ thông tin hạng mục') + ` [${data2.name}]`
            )
            flag = false
          }
          for (const data3 of data2.__childs__) {
            if (this.checkDynamicColRequired(data3, lstDynamicColRequired)) {
              data3.isError = true
              data3.errorText = this.language_key?.BidResetPrice_PleaseInputRequiredColumn || 'Vui lòng điền đủ thông tin các cột động bắt buộc'
              this.notifyService.showWarning(
                `${step} - ` + (this.language_key?.BidResetPrice_NotFilledEnoughInformation || 'Chưa điền đủ thông tin hạng mục') + ` [${data3.name}]`
              )
              flag = false
            }
          }
        }
      }
    }

    return flag
  }

  savePrice() {
    let strErr = ``
    if (!this.data.filePriceDetail) {
      strErr += this.language_key?.BidResetPrice_DetailFileNotUpload || `Chưa upload File chi tiết giá.<br>`
    }
    if (this.bid.isRequireFileTechDetail && !this.data.fileTechDetail) {
      strErr += this.language_key?.BidResetPrice_TechnicalDetailFileNotUpload || `Chưa upload File chi tiết kỹ thuật.<br>`
    }
    if (strErr.length > 0) {
      this.notifyService.showError(strErr)
      this.notifyService.hideloading()
      return
    }

    const check = this.checkContent()
    if (!check) {
      this.notifyService.showError(this.language_key?.BidResetPrice_DataInputNotCorrect || 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại.')
      return
    }

    this.modal.confirm({
      nzTitle: '<i>' + (this.language_key?.BidResetPrice_DoYouWantToFinish || 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?') + '</i>',
      nzContent:
        `<b>` +
        (this.language_key?.BidResetPrice_MakeSureAllTheInformationIsCorrectBeforeSubmit ||
          'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận') +
        `</b>`,
      nzOnOk: () => {
        this.notifyService.showloading()
        const dataSave = {
          bidId: this.bidid,
          dataInfo: this.data,
          priceInfo: this.getDataSave(),
        }
        this.apiService.post(this.apiService.CLIENT_WEB.SAVE_RESET_PRICE, dataSave).then(() => {
          this.notifyService.showSuccess(this.language_key?.BidResetPrice_SubmitPriceSuccessfullly || 'Đã bổ sung bảng chào giá thành công!')
          this.isSuccess = true
        })
      },
    })
  }

  gotoHomePage() {
    this.router.navigate([`home`])
  }

  handleFileInput(event: any, field: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload && fileToUpload.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showWarning(
        (this.language_key?.BidResetPrice_FileExceedMaxSize || 'Vượt qua kích thước tối đa.') +
          `Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`
      )
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.data[field] = res[0]
        else this.data[field] = ''
      })
    }
  }
}
