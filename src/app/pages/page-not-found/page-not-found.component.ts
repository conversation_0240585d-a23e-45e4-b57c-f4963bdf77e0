import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { LanguageService } from '../../services/language.service'

@Component({ templateUrl: './page-not-found.component.html' })
export class PageNotFoundComponent implements OnInit {
  language_key: any = {}
  constructor(private router: Router, private languageService: LanguageService) {}

  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }
  gotoHomePage() {
    this.router.navigate([`home`])
  }
}
