<div *ngIf="data.isDisplayBtn || data.isDisplayBtnSavePrice; else showResult">
  <h1>{{ language_key?.BidDeal_DealBiddingPrice || 'Đàm phán giá gói thầu: ' }} {{ data.bidName || '' }}</h1>

  <nz-collapse nzBordered="false" *ngFor="let child of data.listChild; let i = index" class="mt-2">
    <nz-collapse-panel [nzHeader]="'Item ' + child.itemName" class="ant-bg-antiquewhite" [nzActive]="!dicActiveCollapse[child.id]" (nzActiveChange)="dicActiveCollapse[child.id] = $event">
      <nz-row class="mt-3">
        <nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label nzSpan="24" [nzFor]="'filePriceDetail' + i" class="text-left" [nzRequired]="true">
              {{ language_key?.BidDeal_DetailPriceFile || 'File chi tiết giá' }}
            </nz-form-label>
            <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.BidDeal_PleaseUploadDetailPriceFile || 'Vui lòng upload File chi tiết giá!'">
              <label [for]="'filePriceDetail' + i" class="custom-file-upload">
                <span nz-icon nzType="upload"></span>{{ language_key?.BidDeal_UploadFile || 'Upload File' }}
              </label>
              <input class="hidden" type="file" [id]="'filePriceDetail' + i" (change)="handleFileInput($event, 'filePriceDetail', child)" />
              <div class="tooltip" *ngIf="child.filePriceDetail && child.filePriceDetail.length > 0">
                <a href="{{ child.filePriceDetail }}" target="_blank">{{ language_key?.BidDeal_ViewAttachedFile || 'Xem file đính kèm' }}</a>
              </div>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label nzSpan="24" [nzFor]="'fileTechDetail' + i" class="text-left" [nzRequired]="data.isRequireFileTechDetail">
              {{ language_key?.BidDeal_TechincalDetailFile || 'File chi tiết kỹ thuật' }}
              {{ data.isRequireFileTechDetail ? '' : language_key?.BidDeal_IfHave || '(Nếu có)' }}
            </nz-form-label>
            <nz-form-control
              nzSpan="24"
              [nzErrorTip]="language_key?.BidDeal_PleaseUploadTechnicalDetailFile || 'Vui lòng upload File chi tiết kỹ thuật!'"
            >
              <label [for]="'fileTechDetail' + i" class="custom-file-upload"> <span nz-icon nzType="upload"></span> Upload File </label>
              <input class="hidden" type="file" [id]="'fileTechDetail' + i" (change)="handleFileInput($event, 'fileTechDetail', child)" />
              <div class="tooltip" *ngIf="child.fileTechDetail && child.fileTechDetail.length > 0">
                <a href="{{ child.fileTechDetail }}" target="_blank">{{ language_key?.BidDeal_ViewAttachedFile || 'Xem file đính kèm' }}</a>
              </div>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              >{{ language_key?.BidDeal_LinkDriverToAdditionalFile || 'Link driver các file bổ sung (Nếu có)' }}
            </nz-form-label>
            <nz-form-control nzSpan="24">
              <textarea nz-input [(ngModel)]="child.linkDriver" rows="2" auto></textarea>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>
      <nz-row class="mt-3">
        <button class="mr-2" nz-button (click)="clickExportExcel(i)">
          <span nz-icon nzType="download"></span>{{ language_key?.BidDeal_ExportExcel || 'Xuất excel' }}
        </button>
        <input
          class="hidden"
          type="file"
          [id]="'file' + i"
          (change)="clickImportExcel($event, child)"
          onclick="this.value=null"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        />
        <label nz-button [for]="'file' + i" class="lable-custom-file">
          <span nz-icon nzType="upload"></span>{{ language_key?.BidDeal_ImportExcel || 'Nhập excel' }}
        </label>
      </nz-row>
      <nz-row class="mt-2">
        <nz-table
          nz-col
          nzSpan="24"
          [id]="'test-html-table' + i"
          [nzData]="child.lstDealPrice"
          [nzFrontPagination]="false"
          [nzShowPagination]="false"
        >
          <thead>
            <tr>
              <th class="hidden">ID</th>
              <th>{{ language_key?.BidDeal_IndexName || 'Tên hạng mục' }}</th>
              <th
                *ngFor="let col of child.lstPriceCol"
                [ngClass]="{ 'dynamic-col-mpo': col.colType === mpoType, 'dynamic-col-supplier': col.colType === supType }"
              >
                {{ col.name }}
              </th>
              <th>{{ language_key?.BidDeal_CountUnit || 'Đơn vị tính' }}</th>
              <th>{{ language_key?.BidDeal_CurrencyUnit || 'Đơn vị tiền tệ' }}</th>
              <th>{{ language_key?.BidDeal_Quantity || 'Số lượng' }}</th>
              <th>{{ language_key?.BidDeal_MaximumPrice || 'Giá tối đa' }}</th>
              <th>{{ language_key?.BidDeal_AgreePrice || 'Giá đã chào' }}</th>
              <th *ngIf="!data.isDisplayBtnSavePrice">{{ language_key?.BidDeal_SuggestedPrice || 'Giá đề nghị' }}</th>
              <th>
                Giá đàm phán
                <span
                  *ngIf="!data.isDisplayBtnSavePrice"
                  nz-icon
                  nzType="exclamation-circle"
                  [nzTooltipTitle]="
                    language_key?.BidDeal_DealPriceIfNotInputThenAgree || 'Giá quý nhà cung cấp đàm phán, nếu không nhập thì chấp nhận giá đề nghị'
                  "
                  nz-tooltip
                >
                </span>
              </th>
              <th *ngIf="!isExporting">{{ language_key?.BidDeal_DetailInformation || 'Thông tin chi tiết' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of child.lstDealPrice">
              <td class="hidden">{{ row.bidPriceId }}</td>
              <td>
                <span *ngIf="row.__bidPrice__.isRequired" class="text-danger">*</span>
                <span nz-tooltip [nzTooltipTitle]="row.__bidPrice__.name">{{ row.__bidPrice__.name }}</span>
              </td>
              <td *ngFor="let col of child.lstPriceCol">
                {{ row[col.id] }}
              </td>
              <td>{{ row.__bidPrice__.unit }}</td>
              <td>{{ row.__bidPrice__.currency }}</td>
              <td class="text-right">{{ (row.number > 0 ? row.number : row.__bidPrice__.number) | number }}</td>
              <td class="text-right">{{ row.maxPrice | number }}</td>
              <td class="text-right">{{ row.offerPrice | number }}</td>
              <td *ngIf="!data.isDisplayBtnSavePrice" class="text-right">{{ row.suggestPrice | number }}</td>
              <td>
                <input
                  nz-input
                  currencyMask
                  [(ngModel)]="row.dealPrice"
                  name="dealPrice"
                  [placeholder]="language_key?.BidDeal_InputDealPrice || 'Nhập giá đàm phán'"
                />
              </td>
              <td *ngIf="!isExporting">
                <span
                  *ngIf="row.__bidPrice__.__bidPriceListDetails__?.length"
                  nz-icon
                  nzType="exclamation-circle"
                  nz-popover
                  [nzPopoverContent]="contentTemplate"
                  nzPopoverTrigger="hover"
                >
                </span>
                <ng-template #contentTemplate>
                  <div *ngFor="let item of row.__bidPrice__.__bidPriceListDetails__">
                    <p>{{ item.name }} - {{ item.value }}</p>
                  </div>
                </ng-template>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>

  <div class="text-center mt-2">
    <button
      *ngIf="data.isDisplayBtn"
      nz-popconfirm
      class="ant-btn-success"
      [nzPopconfirmTitle]="language_key?.BidDeal_SureToSuggestDealPrice || 'Bạn có chắc muốn đề nghị giá đàm phán?'"
      (nzOnConfirm)="confirmAccept(true)"
      nz-button
    >
      {{ language_key?.BidDeal_SuggestDealPrice || 'Đề nghị đàm phán giá' }}
    </button>

    <button
      *ngIf="data.isDisplayBtn"
      nz-popconfirm
      nzType="primary"
      [nzPopconfirmTitle]="language_key?.BidDeal_SureToAgreeDealPrice || 'Bạn có chắc muốn chấp nhận giá đề nghị?'"
      (nzOnConfirm)="confirmAccept(false)"
      nz-button
    >
      {{ language_key?.BidDeal_AgreeDealPrice || 'Chấp nhận giá đề nghị' }}
    </button>

    <button
      *ngIf="data.isDisplayBtn"
      nz-popconfirm
      [nzPopconfirmTitle]="
        language_key?.BidDeal_SureToDenyDealPriceAndEndBidding || 'Bạn có chắc muốn từ chối giá đề nghị và kết thúc tham gia gói thầu?'
      "
      (nzOnConfirm)="confirmReject()"
      nz-button
      nzDanger
    >
      {{ language_key?.BidDeal_DenyDealPrice || 'Từ chối giá đề nghị' }}
    </button>
    <button *ngIf="data.isDisplayBtnSavePrice" nzType="primary" (click)="confirmAccept()" nz-button>{{ language_key?.BidDeal_Save || 'Lưu' }}</button>
  </div>
</div>
<ng-template #showResult>
  <nz-result *ngIf="checkResultMessage && checkResultMessage.check" nzStatus="success" [nzTitle]="checkResultMessage.message">
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.BidDeal_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
  <nz-result *ngIf="checkResultMessage && !checkResultMessage.check" [nzTitle]="checkResultMessage.message" nzStatus="error">
    <div nz-result-extra>
      <button nz-button nzType="primary" (click)="gotoHomePage()">{{ language_key?.BidDeal_BackToHome || 'Đi tới trang chủ' }}</button>
    </div>
  </nz-result>
</ng-template>
