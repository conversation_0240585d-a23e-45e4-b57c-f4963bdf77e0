import { Component } from '@angular/core'
import { Router } from '@angular/router'
import { LanguageService } from '../../services/language.service'
@Component({ templateUrl: './page-unauthorized.component.html' })
export class PageUnauthorizedComponent {
  language_key: any = {}
  constructor(private router: Router,  private languageService: LanguageService) {}
  
  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  gotoHomePage() {
    this.router.navigate([`home`])
  }
}
