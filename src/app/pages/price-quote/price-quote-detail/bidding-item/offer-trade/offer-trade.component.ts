import { Component, OnInit, Input } from '@angular/core'

import * as $ from 'jquery'
import * as XLSX from 'xlsx'
import * as moment from 'moment'
import { Borders, Workbook } from 'exceljs'
import * as fs from 'file-saver'
import { LanguageService } from 'src/app/services/language.service'
import { ApiService, NotifyService } from 'src/app/services'
import { enumData } from 'src/app/base/enumData'

@Component({
  selector: 'app-offer-trade',
  templateUrl: './offer-trade.component.html',
})
export class OfferTradeComponent implements OnInit {
  language_key: any = {}
  constructor(private apiService: ApiService, private languageService: LanguageService, private notifyService: NotifyService) { }
  @Input()
  bidid!: string

  @Input()
  offerSupplierId?: string

  @Input() isView!: boolean

  @Input() supplierId!: string
  listOfData: any[] = []
  dataType = enumData.DataType

  /** <PERSON><PERSON><PERSON> thước tối đa tính bằng MB */
  maxSizeUpload = enumData.maxSizeUpload
  pageSize = enumData.Page.pageSizeMax
  loading = false
  ngOnInit() {
    this.loadData()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  getDataSave = () => {
    return this.listOfData
  }

  async handleFileInput(event: any, rowData: any) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload && fileToUpload.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showWarning(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) rowData.value = res[0]
        else rowData.value = ''
      })
    }
  }

  handleClearFile(rowData: any) {
    rowData.value = undefined
    $(`#zen${rowData.id}`).val('')
  }

  loadData = () => {
    if (!this.isView) {
      this.notifyService.showloading()
      this.loading = true

      this.apiService.post(this.apiService.CLIENT_WEB.LOAD_DATA_OFFER_TRADE, { bidId: this.bidid, offerSupplierId: this.offerSupplierId }).then((res) => {
        this.loading = false
        this.notifyService.hideloading()
        this.listOfData = res || []
        if (this.listOfData.length > 0) {
          for (const data1 of this.listOfData) {
            data1.bidTradeId = data1.id
            data1.__childs__ = data1.__childs__.sort((a: any, b: any) => a.sort - b.sort)
            for (const data2 of data1.__childs__) data2.bidTradeId = data2.id
          }
        }
      })
    } else {
      this.notifyService.showloading()
      this.loading = true

      this.apiService.post(this.apiService.CLIENT_WEB.LOAD_DATA_OFFER_TRADE_VIEW, { bidId: this.bidid, supplierId: this.supplierId }).then((res) => {
        this.loading = false
        this.notifyService.hideloading()
        this.listOfData = res || []
        if (this.listOfData.length > 0) {
          for (const data1 of this.listOfData) {
            data1.bidTradeId = data1.id
            data1.__childs__ = data1.__childs__.sort((a: any, b: any) => a.sort - b.sort)
            for (const data2 of data1.__childs__) data2.bidTradeId = data2.id
          }
        }
      })
    }
  }

  resetError(item: any) {
    item.isError = false
    item.errorText = ''
  }

  //#region excel ĐKTM
  dicExcel: any = {}
  clickExportExcel() {
    this.notifyService.showloading()
    //#region Kiểm tra data
    if (this.listOfData.length == 0) {
      this.notifyService.showError(this.language_key?.BiddingTrade_ItemNotHaveTradeCondition || 'Item chưa có thiết lập ĐKTM.')
      return
    }
    const lstOfDataExcel: any[] = []
    let isRemove = false
    for (const itemLv1 of this.listOfData) {
      itemLv1.level = 1
      if (itemLv1.type !== this.dataType.File.code || itemLv1.__childs__.length > 0) {
        lstOfDataExcel.push(itemLv1)
      } else isRemove = true
      for (const itemLv2 of itemLv1.__childs__) {
        itemLv2.level = 2
        if (itemLv2.type !== this.dataType.File.code) {
          lstOfDataExcel.push(itemLv2)
        } else isRemove = true
      }
    }
    if (isRemove) {
      if (lstOfDataExcel.length == 0) {
        this.notifyService.showError(
          this.language_key?.BiddingTrade_ItemNotHaveTradeCondition ||
          'Item chưa có thiết lập ĐKTM có thể up excel. Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.'
        )
        return
      }
      this.notifyService.showInfo(
        this.language_key?.BiddingTrade_NoticeAtFileField || 'Lưu ý: Các trường kiểu File, vui lòng thao tác trên view để cập nhật dữ liệu.'
      )
    }
    this.dicExcel[this.bidid] = lstOfDataExcel
    //#endregion

    //#region Body Table
    const workbook = new Workbook()
    const worksheet = workbook.addWorksheet('Sheet 1')
    const headerRow = worksheet.addRow(['ID', 'Tiêu chí', 'Kiểu dữ liệu', 'Bắt buộc?', 'Giá trị'])

    // Cell Style : Fill and Border
    const border: Partial<Borders> = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    }
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '08298A' },
      }
      cell.border = border
      cell.font = { name: 'Calibri', family: 4, size: 11, bold: true, color: { argb: 'FFFFFF' } }
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }

      switch (colNumber) {
        case 1:
          worksheet.getColumn(colNumber).width = 70
          worksheet.getColumn(colNumber).hidden = true // ẩn cột
          break
        case 2:
        case 5:
          worksheet.getColumn(colNumber).width = 50
          break
        default:
          worksheet.getColumn(colNumber).width = 20
          break
      }
    })

    // Màu theo 2 level
    const arrColor = ['ebebeb', 'ffffff']
    for (const data of lstOfDataExcel) {
      data.valueString = data.value
      if (data.type == this.dataType.Date.code && data.value) data.valueString = `'${moment(new Date(data.value)).format('YYYY-MM-DD')}`
      if (data.type == enumData.DataType.List.code) {
        for (const itemList of data.__bidTradeListDetails__) {
          if (itemList.id == data.value) data.valueString = itemList.name
        }
      }
      const rowData = [
        data.id || '', //'ID',
        data.name || '', //'Tiêu chí',
        data.type || '', //'Kiểu dữ liệu',
        data.isRequired ? 'x' : '', //'Bắt buộc?',
        data.valueString || '', //'Giá trị',
      ]
      const row = worksheet.addRow(rowData)
      row.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: arrColor[data.level - 1] },
        }
        cell.border = border
        cell.font = { name: 'Calibri', family: 4, size: 11, bold: false }
        cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
        if (colNumber == 5 && data.type == enumData.DataType.List.code) {
          cell.dataValidation = {
            type: 'list',
            formulae: ['"' + data.__bidTradeListDetails__.map((c: any) => c.name).join() + '"'],
          }
        }
      })
    }
    //#endregion

    //#region Save File
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName =
        `[${moment(new Date()).format('YYYY-MM-DD')}]` +
        (this.language_key?.BiddingTrade_TemplateTradeConditionDocument || `Template nhập hồ sơ ĐKTM.xlsx`)
      fs.saveAs(blob, fileName)
      setTimeout(() => {
        this.notifyService.showSuccess(this.language_key?.BiddingTrade_DownloadTemplateExcelSuccessfully || 'Tải template thành công!')
      }, 300)
    })
    //#endregion
  }

  clickImportExcel(ev: any) {
    this.notifyService.showloading()
    if (this.listOfData.length == 0) {
      this.notifyService.showError(this.language_key?.BiddingTrade_ItemNotHaveTradeCondition || 'Item chưa có thiết lập ĐKTM.')
      return
    }
    let workBook: any = null
    let jsonData: any[] = []
    const lstHeader = ['id', 'name', 'type', 'isRequired', 'value']
    let lstOfDataExcel: any[] = this.dicExcel[this.bidid]
    if (!lstOfDataExcel) {
      lstOfDataExcel = []
      for (const itemLv1 of this.listOfData) {
        if (itemLv1.type !== this.dataType.File.code || itemLv1.__childs__.length > 0) {
          lstOfDataExcel.push(itemLv1)
        }
        for (const itemLv2 of itemLv1.__childs__) {
          if (itemLv2.type !== this.dataType.File.code) {
            lstOfDataExcel.push(itemLv2)
          }
        }
      }
    }

    const reader = new FileReader()
    const file = ev.target.files[0]
    reader.readAsBinaryString(file)
    reader.onload = () => {
      workBook = XLSX.read(reader.result, { type: 'binary' })
      jsonData = XLSX.utils.sheet_to_json(workBook.Sheets[workBook.SheetNames[0]], {
        raw: true,
        defval: null,
        header: lstHeader,
      })

      //#region check template
      const header: any = jsonData.shift()
      if (
        header.id !== 'ID' ||
        header.name !== (this.language_key?.BiddingTrade_Criteria || 'Tiêu chí') ||
        header.type !== (this.language_key?.BiddingTrade_DataType || 'Kiểu dữ liệu') ||
        header.isRequired !== (this.language_key?.BiddingTrade_IsRequired || 'Bắt buộc?') ||
        header.value !== (this.language_key?.BiddingTrade_Value || 'Giá trị')
      ) {
        this.notifyService.showError(this.language_key?.BiddingTrade_FileTemplateNotCorrect || 'File không đúng template (cột đã bị thay đổi)!')
        return
      }
      if (jsonData.some((c) => !c.id) || lstOfDataExcel.length != jsonData.length) {
        this.notifyService.showError(this.language_key?.BiddingTrade_FileTemplateNotCorrect || `File không đúng template (dòng đã bị thay đổi)!`)
        return
      }
      //#endregion

      //#region check value
      const dicValue: any = {}
      for (const row of jsonData) {
        const item = lstOfDataExcel.find((c) => c.id == row.id && c.name == row.name)
        if (!item) {
          this.notifyService.showError(this.language_key?.BiddingTrade_FileTemplateNotCorrect || `File không đúng template (dòng đã bị thay đổi)!`)
          return
        }
        if (item.isRequired && (row.value == null || row.value === '') && !item.__childs__?.length) {
          this.notifyService.showError(
            (this.language_key?.BiddingTrade_Value || 'Giá trị') +
            (this.language_key?.BiddingTrade_Criteria || 'tiêu chí') +
            `[${row.name}]` +
            (this.language_key?.BiddingTrade_NotNull || 'bắt buộc không được để trống!')
          )
          return
        }
        if (row.value != null && row.value !== '') {
          if (item.type == this.dataType.Date.code) {
            const value = new Date(row.value)
            if (typeof row.value !== 'string' || isNaN(value.getTime())) {
              this.notifyService.showError(
                (this.language_key?.BiddingTrade_Value || 'Giá trị') +
                `[${row.value}]` +
                (this.language_key?.BiddingTrade_NotType || 'không phải kiểu') +
                `${item.type},` +
                (this.language_key?.BiddingTrade_DateFormatYYYY_MM_DD || ' ngày phải có định dạng yyyy-mm-dd')
              )
              return
            }
            dicValue[row.id] = value
          } else if (item.type == this.dataType.Number.code) {
            const value = +row.value
            if (isNaN(value) || !isFinite(value)) {
              this.notifyService.showError(
                (this.language_key?.BiddingTrade_Value || 'Giá trị') +
                `[${row.value}]` +
                (this.language_key?.BiddingTrade_NotType || 'không phải kiểu') +
                ` ${item.type}`
              )
              return
            }
            dicValue[row.id] = value
          } else if (item.type == this.dataType.List.code) {
            const itemChoose = item.__bidTradeListDetails__.find((c: any) => c.name == row.value)
            if (!itemChoose) {
              this.notifyService.showError(
                (this.language_key?.BiddingTrade_Value || 'Giá trị') +
                `[${row.value}]` +
                (this.language_key?.BiddingTrade_NotInList || ' không nằm trong List')
              )
              return
            }
            dicValue[row.id] = itemChoose.id
          } else dicValue[row.id] = row.value
        }
      }
      //#endregion

      //#region fill value
      for (const item of lstOfDataExcel) {
        item.value = dicValue[item.id]
      }
      this.notifyService.showSuccess(enumData.Constants.Message_Import_Success)
      //#endregion
    }
  }
  //#endregion
}
