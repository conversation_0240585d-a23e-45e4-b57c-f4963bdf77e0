<div>
  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" nzBordered [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false">
      <thead>
        <tr>
          <th class="hidden">ID</th>
          <th>{{ language_key?.BiddingCustomPrice_IndexName || 'Tên hạng mục' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_CountUnit || 'Đơn vị tính' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_CurrencyUnit || 'Đơn vị tiền tệ' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_Quantity || 'Số lượng' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_UnitPrice || 'Đơn giá' }}</th>
          <th>{{ language_key?.BiddingCustomPrice_Select || '<PERSON><PERSON> chọn' }}</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data1 of listOfData">
          <td class="hidden">{{ data1.id }}</td>
          <td class="mw-25" (click)="clickEditCustomPrice(data1)">
            <span *ngIf="data1.isRequired" class="text-danger">*</span>
            <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
          </td>
          <td (click)="clickEditCustomPrice(data1)">{{ data1.unit }}</td>
          <td (click)="clickEditCustomPrice(data1)">{{ data1.currency }}</td>
          <td (click)="clickEditCustomPrice(data1)" class="text-right">{{ data1.number | number }}</td>
          <td (click)="resetError(data1)">
            <input [disabled]="isView" nz-input currencyMask [(ngModel)]="data1.value" name="value" placeholder="" />
            <span *ngIf="data1.isError" class="text-danger">{{ data1.errorText }}</span>
          </td>
          <td>
            <button (click)="clickDeleteCustomPrice(data1)" nz-tooltip
              [nzTooltipTitle]="language_key?.BiddingCustomPrice_DeleteIndex || 'Xoá hạng mục'" nz-button nzDanger>
              <span nz-icon nzType="delete"></span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </nz-row>
</div>