<div>
  <nz-row class="mt-2">
    <nz-table nz-col nzSpan="24" nzBordered [nzData]="listOfData" [(nzPageSize)]="pageSize" [nzLoading]="loading"
      [nzShowPagination]="false">
      <thead>
        <tr>
          <th>{{ language_key?.BiddingPrice_IndexName || 'Tên hạng mục' }}</th>
          <th *ngFor="let col of bidPriceCol"
            [ngClass]="{ 'dynamic-col-mpo': col.colType === mpoType, 'dynamic-col-supplier': col.colType === supType }">
            {{ col.name }}
          </th>
          <th class="text-nowrap">{{ language_key?.BiddingPrice_CountUnit || 'Đơn vị tính' }}</th>
          <th class="text-nowrap">{{ language_key?.BiddingPrice_CurrencyUnit || 'Đơn vị tiền tệ' }}</th>
          <th class="text-nowrap">{{ language_key?.BiddingPrice_Quantity || 'Số lượng' }}</th>
          <th class="mw-20 miw-15" *ngIf="!hasFomularValue">{{ language_key?.BiddingPrice_UnitPrice || 'Đơn giá' }}</th>
          <th>{{ language_key?.BiddingPrice_DetailInformation || 'Thông tin chi tiết' }}</th>
        </tr>
      </thead>
      <tbody>
        <!-- level 1 -->
        <ng-container *ngFor="let data1 of listOfData">
          <tr>
            <td class="mw-20 miw-15">
              <span *ngIf="data1.isRequired" class="text-danger">*</span>
              <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
            </td>
            <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
              <span *ngIf="col.colType === mpoType && (col.fomular == null || col.fomular.length === 0)">{{
                data1[col.id] }}</span>
              <span *ngIf="col.type === numberType && col.fomular?.length > 0">{{ data1[col.id] | number }}</span>
              <input [disabled]="isView"
                *ngIf="col.colType === supType && col.type === numberType && (col.fomular == null || col.fomular.length === 0)"
                nz-input currencyMask [(ngModel)]="data1[col.id]" (ngModelChange)="onChangeCalFomular(data1)" />
              <input [disabled]="isView" *ngIf="col.colType === supType && col.type === stringType" nz-input
                [(ngModel)]="data1[col.id]" />
            </td>
            <td class="mw-25">{{ data1.unit }}</td>
            <td class="mw-25">{{ data1.currency }}</td>
            <td class="mw-25 text-right">{{ data1.number | number }}</td>
            <td class="mw-20 miw-15" (click)="resetError(data1)">
              <input [disabled]="isView" *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data1.value"
                name="value" placeholder="" />
              <span *ngIf="hasFomularValue">{{ data1.value | number }}</span>
              <span *ngIf="data1.isError" class="text-danger">{{ data1.errorText }}</span>
            </td>
            <td class="miw-15">
              <button nz-tooltip [nzTooltipTitle]="language_key?.BiddingPrice_DetailInformation || 'Thông tin chi tiết'"
                class="mr-2" nz-button *ngIf="data1.__bidPriceListDetails__?.length" nzType="dashed"
                (click)="viewDetail(data1)">
                <span nz-icon nzType="exclamation-circle"></span>
              </button>
            </td>
          </tr>
          <!-- level 2 -->
          <ng-container *ngFor="let data2 of data1.__childs__">
            <tr>
              <td [nzIndentSize]="5" class="mw-20 miw-15">
                <span *ngIf="data2.isRequired" class="text-danger">*</span>
                <span nz-tooltip [nzTooltipTitle]="data2.name">{{ data2.name }}</span>
              </td>
              <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
                <span *ngIf="col.colType === mpoType && (col.fomular == null || col.fomular.length === 0)">{{
                  data2[col.id] }}</span>
                <span *ngIf="col.type === numberType && col.fomular?.length > 0">{{ data2[col.id] | number }}</span>
                <input [disabled]="isView"
                  *ngIf="col.colType === supType && col.type === numberType && (col.fomular == null || col.fomular.length === 0)"
                  nz-input currencyMask [(ngModel)]="data2[col.id]" (ngModelChange)="onChangeCalFomular(data2)" />
                <input [disabled]="isView" *ngIf="col.colType === supType && col.type === stringType" nz-input
                  [(ngModel)]="data2[col.id]" />
              </td>
              <td class="mw-25">{{ data2.unit }}</td>
              <td class="mw-25">{{ data2.currency }}</td>
              <td class="mw-25 text-right">{{ data2.number | number }}</td>
              <td class="mw-20 miw-15" (click)="resetError(data2)">
                <input [disabled]="isView" *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data2.value"
                  name="value" placeholder="" />
                <span *ngIf="hasFomularValue">{{ data2.value | number }}</span>
                <span *ngIf="data2.isError" class="text-danger">{{ data2.errorText }}</span>
              </td>
              <td class="miw-15">
                <button nz-tooltip
                  [nzTooltipTitle]="language_key?.BiddingPrice_DetailInformation || 'Thông tin chi tiết'" class="mr-2"
                  nz-button *ngIf="data2.__bidPriceListDetails__?.length" nzType="dashed" (click)="viewDetail(data2)">
                  <span nz-icon nzType="exclamation-circle"></span>
                </button>
              </td>
            </tr>
            <!-- level 3 -->
            <ng-container *ngFor="let data3 of data2.__childs__">
              <tr>
                <td [nzIndentSize]="30" class="mw-20 miw-15">
                  <span *ngIf="data3.isRequired" class="text-danger">*</span>
                  <span nz-tooltip [nzTooltipTitle]="data3.name">{{ data3.name }}</span>
                </td>
                <td class="mw-20 miw-15" *ngFor="let col of bidPriceCol">
                  <span *ngIf="col.colType === mpoType && (col.fomular == null || col.fomular.length === 0)">{{
                    data3[col.id] }}</span>
                  <span *ngIf="col.type === numberType && col.fomular?.length > 0">{{ data3[col.id] | number }}</span>
                  <input [disabled]="isView"
                    *ngIf="col.colType === supType && col.type === numberType && (col.fomular == null || col.fomular.length === 0)"
                    nz-input currencyMask [(ngModel)]="data3[col.id]" (ngModelChange)="onChangeCalFomular(data3)" />
                  <input [disabled]="isView" *ngIf="col.colType === supType && col.type === stringType" nz-input
                    [(ngModel)]="data3[col.id]" />
                </td>
                <td class="mw-25">{{ data3.unit }}</td>
                <td class="mw-25">{{ data3.currency }}</td>
                <td class="mw-25 text-right">{{ data3.number | number }}</td>
                <td class="mw-20 miw-15" (click)="resetError(data3)">
                  <input [disabled]="isView" *ngIf="!hasFomularValue" nz-input currencyMask [(ngModel)]="data3.value"
                    name="value" placeholder="" />
                  <span *ngIf="hasFomularValue">{{ data3.value | number }}</span>
                  <span *ngIf="data3.isError" class="text-danger">{{ data3.errorText }}</span>
                </td>
                <td class="miw-15">
                  <button nz-tooltip
                    [nzTooltipTitle]="language_key?.BiddingPrice_DetailInformation || 'Thông tin chi tiết'" class="mr-2"
                    nz-button *ngIf="data3.__bidPriceListDetails__?.length" nzType="dashed" (click)="viewDetail(data3)">
                    <span nz-icon nzType="exclamation-circle"></span>
                  </button>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </ng-container>
      </tbody>
    </nz-table>
  </nz-row>

</div>

<app-bidding-price-list-detail-modal #biddingPriceListDetail> </app-bidding-price-list-detail-modal>