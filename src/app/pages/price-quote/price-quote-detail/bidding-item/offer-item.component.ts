import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core'
import { NzModalService } from 'ng-zorro-antd/modal'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { LanguageService } from 'src/app/services/language.service'
import { ApiService, NotifyService } from 'src/app/services'
import { OfferTradeComponent } from './offer-trade/offer-trade.component'
import { OfferPriceComponent } from './offer-price/offer-price.component'
import { OfferCustomPriceComponent } from './offer-custom-price/offer-custom-price.component'
import { ActivatedRoute, Router } from '@angular/router'
import { enumData } from 'src/app/base/enumData'

@Component({ templateUrl: './offer-item.component.html' })
export class OfferItemComponent implements OnInit {
  language_key: any = {}
  dataOffer: any
  dataType = enumData.DataType
  maxSizeUpload = enumData.maxSizeUpload
  isViewDetail: boolean = false
  constructor(
    private apiService: ApiService,
    private notifyService: NotifyService,
    private languageService: LanguageService,
    private route: ActivatedRoute,
    private router: Router,
    @Optional()
    @Inject(MAT_DIALOG_DATA)
    public data: {
      fileAttach: string
      fileTech: string
      filePrice: string
      linkDrive: string
      note: string
      bidId: string
      supplierId: string
      bidCode: string
      itemName: string
      isView: boolean
      offerSupplierId: string
    },
    private modal: NzModalService
  ) {}

  @ViewChild('offerTrade')
  biddingTrade!: OfferTradeComponent
  @ViewChild('offerPrice')
  biddingPrice!: OfferPriceComponent
  @ViewChild('offerCustomPrice')
  biddingCustomPrice!: OfferCustomPriceComponent

  async ngOnInit() {
    this.notifyService.showloading()
    const navigation = this.router.getCurrentNavigation()
    this.route.params.subscribe((params) => {
      if (params['data']) {
        this.data = JSON.parse(params['data'])
        if (this.data.offerSupplierId) {
          this.isViewDetail = true
          this.onLoadDetail(this.data.offerSupplierId)
        }
      }
    })

    // const bidId = this.route.snapshot.paramMap.get('bidId')
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    await this.loadOffer()
  }

  loadOffer() {
    this.apiService.post(this.apiService.OFFER.FIND, { id: this.data.bidId, offerSupplierId: this.data.offerSupplierId }).then((res) => {
      this.notifyService.hideloading()
      if (res) {
        this.dataOffer = res
        this.notifyService.hideloading()
      }
    })
  }

  saveAllData() {
    // this.saveData()
    // const check0 = this.checkContent(0)
    const check1 = true
    const check2 = true
    const check3 = true
    if (true && check1 && check2 && check3) {
      this.modal.confirm({
        nzTitle: '<i>' + (this.language_key?.BiddingItem_DoYouWantToFinish || 'Bạn có thực sự muốn hoàn tất bảng chào giá đã điền?') + '</i>',
        nzContent:
          `<b>` +
          (this.language_key?.BiddingItem_MakeSureAllTheInformationIsCorrectBeforeSubmit ||
            'Bảng chào giá đã điền sẽ ảnh hưởng trực tiếp đến kết quả đấu thầu. Bạn cần chắc chắn những thông tin đã điền là chính xác trước khi xác nhận') +
          `</b>`,
        nzOnOk: () => {
          this.saveData()
        },
      })
    } else {
      this.notifyService.showError(this.language_key?.BiddingItem_DataInputNotCorrect || 'Dữ liệu nhập vào không hợp lệ vui lòng kiểm tra lại.')
      if (!true) {
        this.jumpError(0)
      } else if (!check1) {
        this.jumpError(1)
      } else if (!check2) {
        this.jumpError(2)
      } else if (!check3) {
        this.jumpError(3)
      }
    }
  }

  saveData = () => {
    this.notifyService.showloading()
    console.log(this.biddingTrade)
    const dataSave: any = {
      bidId: this.data.bidId,
      fileTech: this.data.fileTech,
      fileAttach: this.data.fileAttach,
      filePrice: this.data.filePrice,
      linkDrive: this.data.linkDrive,
      note: this.data.note,
    }
    if (this.dataOffer.isShipment) {
      dataSave.priceShipmentInfo = this.dataOffer.tradeShipment
    } else {
      dataSave.priceInfo = this.biddingPrice.getDataSave()
      dataSave.tradeInfo = this.biddingTrade.getDataSave()
      dataSave.customPriceInfo = this.biddingCustomPrice.getDataSave()
    }
    this.apiService.post(this.apiService.CLIENT_WEB.CREATE_OFFER_SUPPLIER, dataSave).then(() => {
      this.notifyService.showSuccess(this.language_key?.BiddingItem_SubmitDocumentSuccessfully || 'Đã bổ sung hồ sơ thành công!')
      this.closeDialog(1)
    })
  }

  closeDialog(flag: any) {
    this.router.navigate(['/price-quote'])
  }

  // ------------------- step -----------------------------
  // tslint:disable-next-line: member-ordering
  current = 0

  // tslint:disable-next-line: member-ordering
  index = 0

  pre() {
    this.current -= 1
    this.changeContent()
  }

  next() {
    this.current += 1
    this.changeContent()
  }

  jumpError(pos: number) {
    this.current = pos
    this.changeContent()
  }

  changeContent() {
    switch (this.current) {
      case 0: {
        this.index = this.current
        break
      }
      case 1: {
        this.index = this.current
        break
      }
      case 2: {
        this.index = this.current
        break
      }
      case 3: {
        this.index = this.current
        break
      }
      default: {
        this.index = this.current
      }
    }
  }

  checkDynamicColRequired(row: any, lstDynamicColRequired: any[]) {
    for (const col of lstDynamicColRequired) {
      // Nếu chưa nhập thì báo lỗi
      if (row[col.id]) row[col.id] += ''
      if (row[col.id] == null || row[col.id] === '' || row[col.id].trim() === '') {
        return true
      }
    }

    return false
  }

  checkContent(key: number) {
    let checkData: any
    let step = ''
    let flag = true
    let hasFomularValue = false
    let lstDynamicColRequired: any = []
    if (!this.dataOffer.isShipment) {
      hasFomularValue = this.biddingPrice?.getCheckFomular()
      lstDynamicColRequired = this.biddingPrice?.getDynamicColRequired()
    }
    // if (key === 1) {
    //   step = this.language_key?.BiddingItem_SuggestPrice || 'Chào giá'
    //   checkData = this.biddingPrice.getDataSave()
    // } else if (key === 2) {
    //   step = this.language_key?.BiddingItem_ConfigPrice || 'Cơ cấu giá'
    //   checkData = this.biddingCustomPrice.getDataSave()
    // } else if (key === 3) {
    //   step = this.language_key?.BiddingItem_TradeCondition || 'Điều kiện thương mại'
    //   checkData = this.biddingTrade.getDataSave()
    // }

    for (const data1 of checkData) {
      data1.isError = false
      data1.errorText = ''
      if (hasFomularValue && key === 1) {
        // không kiểm tra require
      } else if (data1.isRequired && (data1.__childs__ == null || data1.__childs__.length === 0)) {
        if (!data1.value) {
          data1.isError = true
          data1.errorText = this.language_key?.BiddingItem_PleaseInput || 'Vui lòng nhập dữ liệu trước'
          this.notifyService.showWarning(
            `${step} -` + (this.language_key?.BiddingItem_NotFilledInformation || 'Chưa nhập dữ liệu hạng mục') + ` [${data1.name}]`
          )
          flag = false
        }
      }
      if (key === 1 || key === 2) {
        if (data1.value != null && data1.value <= 0) {
          data1.isError = true
          data1.errorText = this.language_key?.BiddingItem_PleaseInputValueGreaterThanZero || 'Vui lòng nhập giá lớn hơn 0'
          this.notifyService.showWarning(
            `${step} - ` +
              (this.language_key?.BiddingItem_Index || 'Hạng mục') +
              ` [${data1.name}]` +
              (this.language_key?.BiddingItem_PriceGreaterThanZero || 'giá phải lớn hơn 0')
          )
          flag = false
        }
      }

      if (hasFomularValue && key === 1) {
        // không kiểm tra require
      } else {
        if (data1.__childs__ && data1.__childs__.length > 0) {
          for (const data2 of data1.__childs__) {
            data2.isError = false
            data2.errorText = ''
            if (data2.isRequired && (data2.__childs__ == null || data2.__childs__.length === 0)) {
              if (!data2.value) {
                data2.isError = true
                data2.errorText = this.language_key?.BiddingItem_PleaseInput || 'Vui lòng nhập dữ liệu trước'
                this.notifyService.showWarning(
                  `${step} -` + (this.language_key?.BiddingItem_NotFilledInformation || 'Chưa nhập dữ liệu hạng mục') + ` [${data2.name}]`
                )
                flag = false
              }
            }

            if (data2.__childs__ && data2.__childs__.length > 0) {
              for (const data3 of data2.__childs__) {
                data3.isError = false
                data3.errorText = ''
                if (data3.isRequired) {
                  if (!data3.value) {
                    data3.isError = true
                    data3.errorText = this.language_key?.BiddingItem_PleaseInput || 'Vui lòng nhập dữ liệu trước'
                    this.notifyService.showWarning(
                      `${step} -` + (this.language_key?.BiddingItem_NotFilledInformation || 'Chưa nhập dữ liệu hạng mục') + ` [${data3.name}]`
                    )
                    flag = false
                  }
                }
              }
            }
          }
        }
      }

      // Kiểm tra bắt buộc nhập thông tin cột động bảng chào giá
      if (key == 1 && lstDynamicColRequired && lstDynamicColRequired.length > 0) {
        if (this.checkDynamicColRequired(data1, lstDynamicColRequired)) {
          data1.isError = true
          data1.errorText = this.language_key?.BiddingItem_PleaseInputRequiredColumn || 'Vui lòng điền đủ thông tin các cột động bắt buộc'
          this.notifyService.showWarning(
            `${step} -` + (this.language_key?.BiddingItem_NotFilledEnoughInformation || 'Chưa điền đủ thông tin hạng mục') + `[${data1.name}]`
          )
          flag = false
        }
        for (const data2 of data1.__childs__) {
          if (this.checkDynamicColRequired(data2, lstDynamicColRequired)) {
            data2.isError = true
            data2.errorText = this.language_key?.BiddingItem_PleaseInputRequiredColumn || 'Vui lòng điền đủ thông tin các cột động bắt buộc'
            this.notifyService.showWarning(
              `${step} ` + (this.language_key?.BiddingItem_NotFilledEnoughInformation || 'Chưa điền đủ thông tin hạng mục') + ` [${data2.name}]`
            )
            flag = false
          }
          for (const data3 of data2.__childs__) {
            if (this.checkDynamicColRequired(data3, lstDynamicColRequired)) {
              data3.isError = true
              data3.errorText = this.language_key?.BiddingItem_PleaseInputRequiredColumn || 'Vui lòng điền đủ thông tin các cột động bắt buộc'
              this.notifyService.showWarning(
                `${step} - ` + (this.language_key?.BiddingItem_NotFilledEnoughInformation || 'Chưa điền đủ thông tin hạng mục') + ` [${data3.name}]`
              )
              flag = false
            }
          }
        }
      }
    }

    return flag
  }

  handleFileInput(event: any, fieldName: string) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload?.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showError(`Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`)
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) this.data[fieldName] = res[0]
        else this.data[fieldName] = ''
      })
    }
  }

  onLoadDetail(id: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.OFFER.FIND_DETAIL_OFFER_SUPPLIER, { id: id }).then((res) => {
      // this.notifyService.hideloading()
      if (res) {
        this.data = res
        // this.notifyService.hideloading()
      }
    })
  }
}
