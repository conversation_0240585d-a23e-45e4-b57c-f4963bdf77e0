<div>
  <div class="text-center fs-18 fw-700 mt-4">
    <h2>TẠO MỚI BÁO GIÁ - [{{ data.bidCode }}]</h2>
  </div>
  <nz-row>
    <H2 nzSpan="24" class="text-left fw-700"> Thông tin chung </H2>
  </nz-row>
  <nz-row class="mt-2" nzGutter="32">
    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="fileTech" class="text-left">File chi tiết kĩ thuật</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file chi tiết kỹ thuật ">
          <input
            [disabled]="isViewDetail"
            type="file"
            name="fileTech"
            id="fileTech"
            (change)="handleFileInput($event, 'fileTech')"
            [(ngModel)]="data.fileTech"
          />
          <div class="tooltip" *ngIf="data.fileTech">
            <a href="{{ data.fileTech }}" target="_blank"> Xem file </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="fileAttach" class="text-left">File đính kèm</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file đính kèm ">
          <input
            [disabled]="isViewDetail"
            type="file"
            name="fileAttach"
            id="fileAttach"
            (change)="handleFileInput($event, 'fileAttach')"
            [(ngModel)]="data.fileAttach"
          />
          <div class="tooltip" *ngIf="data.fileAttach">
            <a href="{{ data.fileAttach }}" target="_blank"> Xem file </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="12">
      <nz-form-item>
        <nz-form-label nzSpan="24" nzFor="filePrice" class="text-left">File chi tiết giá</nz-form-label>
        <nz-form-control nzSpan="24" nzErrorTip="Vui lòng upload file đính kèm ">
          <input
            [disabled]="isViewDetail"
            type="file"
            name="filePrice"
            id="filePrice"
            (change)="handleFileInput($event, 'filePrice')"
            [(ngModel)]="data.filePrice"
          />
          <div class="tooltip" *ngIf="data.filePrice">
            <a href="{{ data.filePrice }}" target="_blank"> Xem file </a>
          </div>
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="12">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24">Link drive cho các file đính kèm</nz-form-label>
        <nz-form-control nzSpan="24">
          <input [disabled]="isViewDetail" nz-input placeholder="Nhập 1-400 kí tự" [(ngModel)]="data.linkDrive" name="title" pattern=".{1,400}" />
        </nz-form-control>
      </nz-form-item>
    </nz-col>

    <nz-col nzSpan="24">
      <nz-form-item nzFlex>
        <nz-form-label nzSpan="24">Ghi chú</nz-form-label>
        <nz-form-control nzSpan="24">
          <textarea [disabled]="isViewDetail" rows="5" nz-input [(ngModel)]="data.note" name="note"></textarea>
        </nz-form-control>
      </nz-form-item>
    </nz-col>
  </nz-row>

  <div>
    <nz-collapse>
      <nz-collapse-panel *ngIf="!this.dataOffer?.isShipment" [nzHeader]="'Thông tin bảng giá'">
        <app-offer-price [bidid]="data.bidId" [isView]="data.isView" [supplierId]="data.supplierId" #offerPrice></app-offer-price>
      </nz-collapse-panel>
      <nz-collapse-panel *ngIf="!this.dataOffer?.isShipment" [nzHeader]="'Thông tin cơ cấu giá'">
        <app-offer-custom-price [bidid]="data.bidId" [isView]="data.isView" [supplierId]="data.supplierId" #offerCustomPrice></app-offer-custom-price>
      </nz-collapse-panel>
      <nz-collapse-panel *ngIf="dataOffer?.isShipment" [nzHeader]="'Thông tin các hạng mục chào giá shipment'">
        <nz-row *ngIf="dataOffer?.isShipment" class="mt-2">
          <nz-table nz-col nzSpan="24" nzBordered [nzData]="dataOffer?.tradeShipment" [nzShowPagination]="false">
            <thead>
              <tr>
                <th class="hidden">ID</th>
                <th>STT</th>
                <th>Condition type</th>
                <th>Mô tả</th>
                <th>Amount</th>
                <th>Crcy</th>
                <th>Per</th>
                <th>Condition value</th>
                <th>Curr</th>
                <th>NumCCo</th>
                <th>CConDe</th>
                <th>Giá trị</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of dataOffer?.tradeShipment; let i1 = index">
                <td>{{ i1 + 1 }}</td>
                <td class="text-left">{{ data.conditionType }}</td>
                <td class="text-left">{{ data.description }}</td>
                <td class="text-left">{{ data.amount }}</td>
                <td class="text-left">{{ data.crcy }}</td>
                <td class="text-left">{{ data.per }}</td>
                <td class="text-left">{{ data.conditionValue }}</td>
                <td class="text-left">{{ data.curr }}</td>
                <td class="text-left">{{ data.numCCo }}</td>
                <td class="text-left">{{ data.cConDe }}</td>
                <td class="text-left">
                  <input nz-input currencyMask [(ngModel)]="data.value" name="value" placeholder="" />
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-row>
      </nz-collapse-panel>
      <nz-collapse-panel *ngIf="!dataOffer?.isShipment" [nzHeader]="'Thông tin điều kiện thương mại'">
        <app-offer-trade
          *ngIf="!dataOffer?.isShipment"
          [bidid]="data.bidId"
          [offerSupplierId]="data.offerSupplierId"
          [isView]="data.isView"
          [supplierId]="data.supplierId"
          #offerTrade
        ></app-offer-trade>
      </nz-collapse-panel>
    </nz-collapse>
  </div>

  <div style="text-align: center; margin-top: 10px">
    <button *ngIf="!data.isView" nz-button nzType="primary" (click)="saveAllData()">
      {{ language_key?.BiddingItem_SubmitDocument || 'Nộp hồ sơ' }}
    </button>
    <button style="margin-left: 5px" class="mr-2" nz-button nzType="primary" (click)="closeDialog(0)">
      {{ 'Đóng' }}
    </button>
  </div>
</div>
