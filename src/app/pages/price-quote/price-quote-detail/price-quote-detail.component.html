<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      <!-- {{ 'Chi tiết chào giá nhanh' | uppercase }} -->
    </nz-col>
  </nz-row>

  <nz-tabset>
    <nz-tab nzTitle="Thông tin chung">
      <nz-collapse class="collapse-custom">
        <nz-collapse-panel [nzHeader]="'Thông tin chung'">
          <div nz-row [nzGutter]="16" style="padding: 0 8%" [nzGutter]="24">
            <!-- 1 -->
            <div nz-col class="gutter-row" [nzSpan]="4">
              <b>Công ty</b>
              <br />
              <p>{{ nowData?.sup?.name }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="5">
              <b>Email</b>
              <br />
              <p>{{ nowData?.sup?.email }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="4">
              <b>Đ<PERSON>a chỉ</b>
              <br />
              <p>{{ nowData?.sup?.address }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="5">
              <b>Số điện thoại</b>
              <br />
              <p>{{ nowData?.sup?.phone }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="5">
              <b>Fax</b>
              <br />
              <p>{{ nowData?.sup?.fax }}</p>
            </div>

            <!-- 2 -->
            <div nz-col class="gutter-row" [nzSpan]="4">
              <b>Ngày bắt đầu</b>
              <br />
              <p>{{ nowData?.effectiveDate | date : 'dd/MM/YYYY' }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="5">
              <b>Ngày hết hạn báo giá</b>
              <br />
              <p>{{ nowData?.endDate | date : 'dd/MM/YYYY' }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="4">
              <b>Giá đã bao gồm VAT</b>
              <br />
              <p>{{ nowData?.isHaveVat ? 'Có' : 'Không' }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="5">
              <b>Đơn vị tiền tệ</b>
              <br />
              <p>{{ nowData.currency }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="5">
              <b>Mô tả</b>
              <br />
              <p>{{ nowData.description }}</p>
            </div>
            <!-- 3 -->
            <div nz-col class="gutter-row" [nzSpan]="4">
              <b>File đính kèm</b>
              <br />
              <p>{{ 'File đính kèm' }}</p>
            </div>
            <div nz-col class="gutter-row" [nzSpan]="5">
              <b>Trạng thái</b>
              <br />
              <nz-tag
                [ngStyle]="{
                  color: nowData.statusColor,
                  border: '1px solid ' + nowData.statusBorderColor
                }"
                [nzColor]="nowData.statusBgColor"
                style="font-weight: 600; border-radius: 30px"
              >
                <div style="display: flex; align-items: center; justify-content: center">
                  <div
                    [ngStyle]="{
              background: nowData.statusColor,
              }"
                    class="dot"
                  ></div>
                  <span class="ml-1"> {{ nowData.statusName }}</span>
                </div>
              </nz-tag>
            </div>
          </div>
        </nz-collapse-panel>

        <div *ngFor="let item of nowData?.price?.listItem; let i = index" class="mt-3">
          <div *ngIf="!nowData.isShipment">
            <nz-collapse class="mt-3">
              <nz-collapse-panel nzHeader="Thông tin các hạng mục chào giá" class="ant-bg-lightblue">
                <nz-row class="mt-3">
                  <nz-table nz-col nzSpan="24" [nzData]="item.listPrice" [nzShowPagination]="false" nzBordered>
                    <thead>
                      <tr>
                        <th style="width: 120px">
                          {{ language_key?.NO || 'STT' }}
                        </th>
                        <th>Tên hạng mục</th>
                        <th *ngFor="let col of item.listPriceCol" class="dynamic-col-mpo">
                          {{ col.name }}
                        </th>
                        <th>Đơn vị tính</th>
                        <th>Đơn vị tiền tệ</th>
                        <th nz-tooltip nzTooltipTitle="Mã cột để làm công thức: [qty]">
                          {{ language_key?.QUANTITY || 'Số lượng' }}
                        </th>
                        <th>Bắt buộc?</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- level 1 -->
                      <ng-container *ngFor="let data1 of item.listPrice">
                        <tr>
                          <td>
                            {{ data1.sort > 0 ? data1.sort : '' }}
                          </td>
                          <td class="mw-25">{{ data1.name }}</td>
                          <td *ngFor="let col of item.listPriceCol">{{ data1[col.id] }}</td>
                          <td>
                            {{ data1.unit }}
                          </td>
                          <td>
                            {{ data1.currency }}
                          </td>
                          <td class="text-right">{{ data1.number | number }}</td>
                          <td>
                            {{ data1.isRequired ? 'Bắt buộc' : 'Không' }}
                          </td>
                        </tr>
                        <!-- level 2 -->
                        <ng-container *ngFor="let data2 of data1.__childs__">
                          <tr>
                            <td [nzIndentSize]="5">{{ data2.sort > 0 ? data2.sort : '' }}</td>
                            <td class="mw-25">
                              {{ data2.name }}
                            </td>
                            <td *ngFor="let col of item.listPriceCol">{{ data2[col.id] }}</td>
                            <td>
                              {{ data2.unit }}
                            </td>
                            <td>
                              {{ data2.currency }}
                            </td>
                            <td class="text-right">
                              {{ data2.number | number }}
                            </td>
                            <td>
                              {{ data2.isRequired ? 'Bắt buộc' : 'Không' }}
                            </td>
                          </tr>
                          <!-- level 3 -->
                          <ng-container *ngFor="let data3 of data2.__childs__">
                            <tr>
                              <td [nzIndentSize]="30">{{ data3.sort > 0 ? data3.sort : '' }}</td>
                              <td class="mw-25">
                                {{ data3.name }}
                              </td>
                              <td *ngFor="let col of item.listPriceCol">{{ data3[col.id] }}</td>
                              <td>
                                {{ data3.unit }}
                              </td>
                              <td>
                                {{ data3.currency }}
                              </td>
                              <td class="text-right">
                                {{ data3.number | number }}
                              </td>
                              <td>
                                {{ data3.isRequired ? 'Bắt buộc' : 'Không' }}
                              </td>
                            </tr>
                          </ng-container>
                        </ng-container>
                      </ng-container>
                    </tbody>
                  </nz-table>
                </nz-row>
              </nz-collapse-panel>
            </nz-collapse>

            <nz-collapse class="mt-3">
              <nz-collapse-panel nzHeader="Thông tin các hạng mục cơ cấu giá" class="ant-bg-lightblue">
                <nz-row class="mt-3">
                  <nz-table nz-col nzSpan="24" [nzData]="item.listCustomPrice" [nzShowPagination]="false" nzBordered>
                    <thead>
                      <tr>
                        <th>{{ language_key?.NO || 'STT' }}</th>
                        <th>Tên hạng mục</th>
                        <th *ngFor="let col of item.listPriceCol" class="dynamic-col-mpo">
                          {{ col.name }}
                        </th>
                        <th>Đơn vị tính</th>
                        <th>Đơn vị tiền tệ</th>
                        <th>{{ language_key?.QUANTITY || 'Số lượng' }}</th>
                        <th>Bắt buộc?</th>
                      </tr>
                    </thead>
                    <tbody>
                      <ng-container *ngFor="let dataRow of item.listCustomPrice">
                        <tr>
                          <td>
                            {{ dataRow.sort > 0 ? dataRow.sort : '' }}
                          </td>
                          <td class="mw-25">
                            {{ dataRow.name }}
                          </td>
                          <td *ngFor="let col of item.listPriceCol">{{ dataRow[col.id] }}</td>
                          <td>
                            {{ dataRow.unit }}
                          </td>
                          <td>
                            {{ dataRow.currency }}
                          </td>
                          <td class="text-right">
                            {{ dataRow.number | number }}
                          </td>
                          <td>
                            {{ dataRow.isRequired ? 'Bắt buộc' : 'Không' }}
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </nz-table>
                </nz-row>
              </nz-collapse-panel>
            </nz-collapse>
          </div>
        </div>

        <nz-collapse-panel *ngIf="nowData.isShipment" [nzHeader]="'Thông tin các hạng mục chào giá shipment'">
          <nz-row class="mt-2">
            <nz-table nz-col nzSpan="24" nzBordered [nzData]="nowData.tradeShipment" [nzShowPagination]="false">
              <thead>
                <tr>
                  <th class="hidden">ID</th>
                  <th>STT</th>
                  <th>Condition type</th>
                  <th>Mô tả</th>
                  <th>Amount</th>
                  <th>Crcy</th>
                  <th>Per</th>
                  <th>Condition value</th>
                  <th>Curr</th>
                  <th>NumCCo</th>
                  <th>CConDe</th>
                  <!-- <th>Giá trị</th> -->
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let data of nowData.tradeShipment; let i1 = index">
                  <td>{{ i1 + 1 }}</td>
                  <td class="text-left">{{ data.conditionType }}</td>
                  <td class="text-left">{{ data.description }}</td>
                  <td class="text-left">{{ data.amount }}</td>
                  <td class="text-left">{{ data.crcy }}</td>
                  <td class="text-left">{{ data.per }}</td>
                  <td class="text-left">{{ data.conditionValue }}</td>
                  <td class="text-left">{{ data.curr }}</td>
                  <td class="text-left">{{ data.numCCo }}</td>
                  <td class="text-left">{{ data.cConDe }}</td>
                  <!-- <td class="text-left">
                    <input nz-input currencyMask [(ngModel)]="data.value" name="value" placeholder="" />
                  </td> -->
                </tr>
              </tbody>
            </nz-table>
          </nz-row>
        </nz-collapse-panel>

        <nz-collapse-panel [nzHeader]="'Thông tin điều kiện thương mại'">
          <div *ngFor="let item of nowData?.trade.listItem; let i = index" class="mt-3">
            <div>
              <nz-row class="mt-3">
                <nz-table nz-col nzSpan="24" [nzData]="item.listTrade" [nzShowPagination]="false" nzBordered>
                  <thead>
                    <tr>
                      <th>{{ language_key?.NO || 'STT' }}</th>
                      <th>Tên điều kiện thương mại</th>
                      <th>Tỉ trọng(%)</th>
                      <th>Giá trị đạt</th>
                      <th>Kiểu dữ liệu</th>
                      <th>Bắt buộc?</th>
                      <th>{{ language_key?.OPTION || 'Tuỳ chọn' }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <ng-container *ngFor="let rowData of item.listTrade">
                      <tr>
                        <td>
                          {{ rowData.sort > 0 ? rowData.sort : '' }}
                        </td>
                        <td class="mw-25">{{ rowData.name }}</td>
                        <td class="text-right">{{ rowData.percent }}</td>
                        <td class="text-right">{{ rowData.percentRule | number }}</td>
                        <td>
                          {{ rowData.type }}
                        </td>
                        <td>
                          {{ rowData.isRequired ? 'Bắt buộc' : 'Không' }}
                        </td>
                      </tr>
                      <ng-container>
                        <td
                          [nzIndentSize]="10"
                          colspan="8"
                          scope="colgroup"
                          *ngIf="rowData.sumPercent < 100 && rowData.sumPercent >= 0"
                          class="text-orange"
                        >
                          Tổng tỉ trọng trong mục này đạt {{ rowData.sumPercent }}%, chưa đủ 100%
                        </td>
                        <tr *ngFor="let childData of rowData.__childs__">
                          <td [nzIndentSize]="10">{{ childData.sort > 0 ? childData.sort : '' }}</td>
                          <td class="mw-25">
                            {{ childData.name }}
                          </td>
                          <td class="text-right">{{ childData.percent }}</td>
                          <td class="text-right">{{ childData.percentRule | number }}</td>
                          <td>
                            {{ childData.type }}
                          </td>
                          <td>
                            {{ childData.isRequired ? 'Bắt buộc' : 'Không' }}
                          </td>
                        </tr>
                      </ng-container>
                    </ng-container>
                  </tbody>
                </nz-table>
              </nz-row>
            </div>
          </div>
        </nz-collapse-panel>
      </nz-collapse>
    </nz-tab>
    <!--  -->
    <nz-tab *ngIf="nowData.canSend" nzTitle="Báo giá">
      <button nzType="primary" nzGhost nz-button (click)="clickAdd()"><span nz-icon nzType="plus"></span> Tạo mới</button>

      <nz-row style="width: 100%" class="mt-3">
        <nz-col nzSpan="24">
          <nz-table nz-col nzSpan="24" [nzData]="nowData?.lstSend" [nzShowPagination]="false" nzBordered>
            <thead>
              <tr>
                <th>{{ 'Mã RFQ' }}</th>
                <th>{{ 'Thời gian báo giá' }}</th>
                <th>Trạng thái</th>
                <th>{{ language_key?.Column_Action || 'Tác vụ' }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let dataRow of nowData?.lstSend">
                <td>{{ nowData.code }}</td>
                <td>{{ dataRow.createdAt | date : 'dd/MM/yyyy HH:mm' }}</td>
                <td>
                  <nz-tag
                    [ngStyle]="{
                      color: dataRow.statusColor,
                      border: '1px solid ' + dataRow.statusBorderColor
                    }"
                    [nzColor]="dataRow.statusBgColor"
                    style="font-weight: 600; border-radius: 30px"
                  >
                    <div style="display: flex; align-items: center; justify-content: center">
                      <div
                        [ngStyle]="{
                              background: dataRow.statusColor,
                              }"
                        class="dot"
                      ></div>
                      <span class="ml-1"> {{ dataRow.statusName }}</span>
                    </div>
                  </nz-tag>
                </td>
                <td>
                  <button
                    class="mr-1"
                    nzShape="circle"
                    nzTrigger="click"
                    (click)="clickView(dataRow.id)"
                    nz-button
                    nzPlacement="bottomCenter"
                    nzTooltipTitle="Chi tiết"
                  >
                    <span nz-icon nzType="eye"></span>
                  </button>

                  <button
                    class="mr-1"
                    nzShape="circle"
                    nzTrigger="click"
                    (click)="send(dataRow.id)"
                    nz-button
                    nzPlacement="bottomCenter"
                    nzTooltipTitle="Gửi"
                  >
                    <span nz-icon nzType="check"></span>
                  </button>
                  <!-- data.isShowEdit || data.isShowCancel -->
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-col>
      </nz-row>
    </nz-tab>
  </nz-tabset>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center" style="margin-top: 15px">
      <button *ngIf="!nowData.canSend" (click)="onSave()" nz-button [disabled]="!frmAdd.form.valid" nzType="primary">
        {{ 'Tham gia' }}
      </button>
    </nz-col>

    <nz-col *ngIf="nowData.status === 'DangDamPhanGia'" nzSpan="24" class="text-center" style="margin-top: 15px">
      <button (click)="onOpenDeal()" nz-button [disabled]="!frmAdd.form.valid" nzType="primary">
        {{ 'Chuyển tới trang đàm phán' }}
      </button>
    </nz-col>
  </nz-row>
</form>
