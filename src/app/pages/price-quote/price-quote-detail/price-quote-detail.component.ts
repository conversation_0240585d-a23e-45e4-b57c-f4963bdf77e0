import { Component, OnInit, Optional, Inject } from '@angular/core'
import { FormBuilder, Validators } from '@angular/forms'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/base/enumData'
import { ActivatedRoute, Router } from '@angular/router'

@Component({
  selector: 'app-price-quote-detail',
  templateUrl: './price-quote-detail.component.html',
  styleUrls: ['./price-quote-detail.component.scss'],
})
export class PriceQuoteDetailComponent implements OnInit {
  modalTitle = 'Thêm mới chào giá nhanh'
  passwordVisible = false
  passwordVisible2 = false
  validateForm: any
  dataObject: any = {}
  dicActiveCollapseCustomPrice: any = {}
  isVisiblePopupChooseAddress = false
  isLoadCity = false
  dataCity: any[] = []
  dataDistrict: any[] = []
  dataWard: any[] = []
  dataEmployee: any
  supplier: any
  service: any
  cityId!: string
  districtId!: string
  listOfData: any
  nowData: any
  wardId!: string
  lstData: any
  address!: string
  dicActiveCollapsePrice: any = {}
  fieldCurrent: any
  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  action: any
  constructor(
    private fb: FormBuilder,
    private readonly apiService: ApiService,
    private readonly notifyService: NotifyService,
    public coreService: CoreService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly storageService: StorageService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.currentUser = x))
  }

  async ngOnInit() {
    this.dataObject = this.data
    const id = this.route.snapshot.paramMap.get('id')
    this.data = { id }
    if (this.currentUser) {
      this.apiService.post(this.apiService.OFFER.FIND, { id: this.data.id }).then((result) => {
        this.nowData = result
      })
    } else {
      this.apiService.post(this.apiService.OFFER.FIND_NO_TOKEN, { id: this.data.id }).then((result) => {
        this.nowData = result
      })
    }
    if (this.data && this.data.id) {
      this.modalTitle = 'Chỉnh sửa chào giá nhanh'
      this.dataObject = { ...this.data }
    }
    this.dataObject = this.data
  }
  clickAdd() {
    // bidId: string; supplierId: string; bidCode: string; itemName: string
    const dataTran: any = { bidId: this.nowData.id, supplierId: this.nowData.sup.id, bidCode: this.nowData.code, itemName: this.nowData.name }
    this.router.navigate(['/price-quote/add-new', { data: JSON.stringify(dataTran) }])
  }

  clickView(id: any) {
    const dataTran: any = {
      offerSupplierId: id,
      bidId: this.nowData.id,
      supplierId: id,
      bidCode: this.nowData.code,
      itemName: this.nowData.name,
      isView: true,
    }
    this.router.navigate(['/price-quote/add-new', { data: JSON.stringify(dataTran) }])
  }

  async send(id: any) {
    await this.apiService.post(this.apiService.OFFER.SEND_OFFER, { id: id }).then((result) => {
      this.notifyService.showloading()
      if (this.currentUser) {
        this.apiService.post(this.apiService.OFFER.FIND, { id: this.data.id }).then((result) => {
          this.nowData = result
          this.notifyService.hideloading()
        })
      } else {
        this.apiService.post(this.apiService.OFFER.FIND_NO_TOKEN, { id: this.data.id }).then((result) => {
          this.nowData = result
          this.notifyService.hideloading()
        })
      }
    })
  }

  async onOpenDeal() {
    /* tìm ra danh sách deal của offer đó */
    await this.apiService.post(this.apiService.OFFER.GET_OFFER_DEAL_ID, this.nowData).then((result) => {
      // chuyển hướng trang sang trang deal
      this.router.navigate(['/offer-deal'], { queryParams: { biddealid: result.id } })
    })
  }
  async onSave() {
    await this.apiService.post(this.apiService.OFFER.CREATE_CLIENT, this.nowData).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Create_Success)
        this.dataObject = this.data
        const id = this.route.snapshot.paramMap.get('id')
        this.data = { id }
        if (this.currentUser) {
          this.apiService.post(this.apiService.OFFER.FIND, { id: this.data.id }).then((result) => {
            this.nowData = result
          })
        } else {
          this.apiService.post(this.apiService.OFFER.FIND_NO_TOKEN, { id: this.data.id }).then((result) => {
            this.nowData = result
          })
        }
        if (this.data && this.data.id) {
          this.modalTitle = 'Chỉnh sửa chào giá nhanh'
          this.dataObject = { ...this.data }
        }
        this.dataObject = this.data
      }
    })
  }
  submitForm() {
    //#endregion
  }

  closeDialog(flag: any) {
    // this.dialogRef.close(flag)
  }

  handleFileInput(event: any, fieldName: string) {}

  loadCity() {}

  /** load district */
  async cityChange(cityId: string) {}

  /** load ward */
  async districtChange(districtId: string) {}

  onChooseAddress(field: any) {}

  handleOk() {}

  handleCancel() {}
}
