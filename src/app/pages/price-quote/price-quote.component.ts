import { Component, OnInit } from '@angular/core'

import { NzCascaderOption } from 'ng-zorro-antd/cascader'
import { MatDialog } from '@angular/material/dialog'

import { Subscription } from 'rxjs'
import * as XLSX from 'xlsx'
import { ApiService, AuthenticationService, CoreService, NotifyService, StorageService } from 'src/app/services'

import { enumData } from '../../base/enumData'
import { PriceQuoteDetailComponent } from './price-quote-detail/price-quote-detail.component'
import { ActivatedRoute, Router } from '@angular/router'

@Component({
  selector: 'app-price-quote',
  templateUrl: './price-quote.component.html',
  styleUrls: ['./price-quote.component.scss'],
})
export class PriceQuoteComponent implements OnInit {
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  pageSizeMax = enumData.Page.pageSizeMax
  total = enumData.Page.total
  loading = true
  dataService: any[] = []
  dataSearch: any = {}
  listOfData: any[] = []
  errorString!: string
  isExporting = false
  lstErrorImport: any[] = []
  isVisibleError = false
  isVisibleChangePw = false
  supplierChoose: any

  language_key: any
  subscriptions: Subscription = new Subscription()
  currentUser: any
  enumProject: any
  enumRole: any
  action: any

  constructor(
    private apiService: ApiService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private route: ActivatedRoute,
    private router: Router,
    private storageService: StorageService,
    public authenticationService: AuthenticationService
  ) {
    this.authenticationService.currentUser.subscribe((x) => (this.currentUser = x))
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumProject = x.enumProject))
  }

  ngOnInit() {
    // this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
    //   if (data) this.language_key = this.coreService.getLanguage()
    // })
    // this.language_key = this.coreService.getLanguage()
    // this.dataSearch.statusId = enumData.StatusFilter.Active.value
    // this.enumRole = this.enumProject.Features.SUPPLIER_001.code
    // this.action = this.enumProject.Action
    this.searchData()
  }

  async searchData(reset = false) {
    // this.loading = true
    if (reset) this.pageIndex = 1
    const where: any = { isDeleted: false }
    if (this.dataSearch.fromDate && this.dataSearch.fromDate !== '') {
      where.fromDate = this.dataSearch.fromDate
    }

    if (this.dataSearch.toDate && this.dataSearch.toDate !== '') {
      where.toDate = this.dataSearch.toDate
    }

    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }

    if (this.dataSearch.serviceChose && this.dataSearch.serviceChose.length > 0) {
      where.serviceId = this.dataSearch.serviceChose[this.dataSearch.serviceChose.length - 1]
    }

    if (this.dataSearch.supplierCode && this.dataSearch.supplierCode.length !== '') {
      where.supplierCode = this.dataSearch.supplierCode
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }

    // this.listOfData = [
    //   {
    //     code: 'CGN_COM_001',
    //     month: '04',
    //     day: '23',
    //     name: 'Gói thầu Gạo',
    //     service: 'Gạo, Ngũ cốc',
    //     timeApply: '23/04/2024',
    //     timeEnd: '23/05/2024',
    //     apply: 'Nhân viên phụ trách chào giá',
    //     statusName: 'Đã duyệt',
    //   },
    //   {
    //     code: 'CGN_SERVICE_001',
    //     name: 'Gói thầu Ghế',
    //     service: 'Ghế da, Ghế nhựa',
    //     month: '04',
    //     day: '24',
    //     timeApply: '24/04/2024',
    //     timeEnd: '24/05/2024',
    //     apply: 'Nhân viên phụ trách chào giá',
    //     statusName: 'Đã duyệt',
    //   },
    //   {
    //     code: 'CGN_SERVICE_001',
    //     name: 'Gói thầu Bàn',
    //     apply: 'Nhân viên phụ trách chào giá',
    //     timeApply: '25/04/2024',
    //     month: '04',
    //     day: '25',
    //     timeEnd: '25/05/2024',
    //     service: 'Bàn',
    //     statusName: 'Mới tạo',
    //   },
    //   {
    //     code: 'CGN_SERVICE_001',
    //     name: 'Gói thầu Ly',
    //     timeApply: '23/05/2024',
    //     timeEnd: '27/05/2024',
    //     month: '05',
    //     day: '23',
    //     apply: 'Nhân viên phụ trách chào giá',
    //     service: 'Ly',
    //     statusName: 'Mới tạo',
    //   },
    // ]
    // this.total = 4
    if (this.currentUser) {
      this.apiService.post(this.apiService.OFFER.PAGINATION_CLIENT, dataSearch).then((res) => {
        this.loading = false
        // this.total = res[1]
        this.listOfData = res
        for (const item of this.listOfData) {
          item.itemName = item.listSupplierService.map((c: any) => c.itemName).join()
        }
      })
    } else {
      this.apiService.post(this.apiService.OFFER.PAGINATION_CLIENT_NO_TOKEN, dataSearch).then((res) => {
        this.loading = false
        // this.total = res[1]
        this.listOfData = res
        for (const item of this.listOfData) {
          item.itemName = item.listSupplierService.map((c: any) => c.itemName).join()
        }
      })
    }
  }

  // loadDataService = (node: NzCascaderOption, index: number) => {
  //   return new Promise<void>(async (resolve) => {
  //     if (index < 0) {
  //       const data = await this.apiService.post(this.apiService.SERVICE.FIND, { level: 1 })
  //       node.children = data.map((v: any) => {
  //         return {
  //           value: v.id,
  //           label: v.code,
  //           isLast: v.isLast,
  //           isLeaf: !!v.isLast,
  //         }
  //       })
  //     } else {
  //       const data = await this.apiService.post(this.apiService.SERVICE.FIND, { parentId: node.value })
  //       node.children = data.map((v: any) => {
  //         return {
  //           value: v.id,
  //           label: v.code,
  //           isLast: v.isLast,
  //           isLeaf: !!v.isLast,
  //         }
  //       })
  //     }
  //     resolve()
  //   })
  // }

  clickExportExcel() {}

  clickAdd() {
    // this.dialog
    //   .open(AddPriceQuoteComponent, { disableClose: false })
    //   .afterClosed()
    //   .subscribe((res) => {
    //     if (res) this.searchData()
    //   })
  }

  viewDetail(object: any) {
    // this.dialog
    //   .open(PriceQuoteDetailComponent, { disableClose: false, data: object })
    //   .afterClosed()
    //   .subscribe((res) => {
    //     if (res) this.searchData()
    //   })

    this.router.navigate(['/price-quote/detail', object.id])
  }

  clickExportExcelTemplate() {}

  clickImportExcel(ev: any) {}

  clickShowErrorImport() {
    this.isVisibleError = true
  }

  closeModelError() {
    this.isVisibleError = false
  }
}
