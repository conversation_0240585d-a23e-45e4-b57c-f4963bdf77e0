<div *ngIf="!isErr">
  <h2 class="title mt-3">{{ language_key?.Detail_BiddingAnnoucement || 'THÔNG BÁO MỜI THẦU' }}</h2>

  <nz-collapse nzBordered="true">
    <nz-collapse-panel [nzHeader]="language_key?.Detail_BasicInformation || 'Thông tin chung'" class="ant-bg-antiquewhite" nzActive="true">
      <nz-descriptions [nzColumn]="1">
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingSide || 'Bên mời thầu'">
          {{ dataObject.companyInvite }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingSubmitAddress || 'Địa chỉ nộp hồ sơ thầu'">
          {{ dataObject.addressSubmit }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPlace || 'Các địa điểm thực hiện gói thầu'">
          {{ dataObject.listAddress }}
        </nz-descriptions-item>
      </nz-descriptions>
    </nz-collapse-panel>
  </nz-collapse>
  <nz-collapse nzBordered="true" class="mt-2">
    <nz-collapse-panel
      [nzHeader]="language_key?.Detail_BiddingPackageInformation || 'Thông tin gói thầu'"
      class="ant-bg-antiquewhite"
      nzActive="true"
    >
      <nz-descriptions [nzColumn]="1">
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageName || 'Tên gói thầu'">
          {{ dataObject.name }} ({{ language_key?.Detail_BiddingPackageCode || 'Mã' }} TBMT: {{ dataObject.code }})
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageStatus || 'Trạng thái'">
          {{ bidStatus }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageDescription || 'Mô tả nội dung mời thầu'">
          {{ dataObject.serviceInvite }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageMethod || 'Hình thức đấu thầu'">
          {{ dataObject.bidTypeName }}
        </nz-descriptions-item>
      </nz-descriptions>

      <hr class="hr-dashed" />

      <nz-descriptions [nzColumn]="1">
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackagePreserveMethod || 'Hình thức bảo lãnh dự thầu'">
          {{ dataObject.masterBidGuaranteeName }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageMoneyForMethod || 'Số tiền bảo lãnh dự thầu (VNĐ)'">
          {{ dataObject.moneyGuarantee | number }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageDeadlineForMethod || 'Thời hạn bảo lãnh dự thầu'">
          {{ dataObject.timeGuarantee | number }} {{ language_key?.Detail_Month || 'tháng' }}
        </nz-descriptions-item>
      </nz-descriptions>

      <hr class="hr-dashed" />

      <nz-descriptions [nzColumn]="1">
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageUploadDate || 'Ngày đăng tải gói thầu'">
          {{ dataObject.publicDate | date : 'dd/MM/yyyy HH:mm:ss' }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageDeadlineForConfirm || 'Ngày hết hạn xác nhận tham gia'">
          {{ dataObject.acceptEndDate | date : 'dd/MM/yyyy HH:mm:ss' }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageDeadlineForSubmit || 'Ngày hết hạn nộp hồ sơ thầu'">
          {{ dataObject.submitEndDate | date : 'dd/MM/yyyy HH:mm:ss' }}
        </nz-descriptions-item>
        <nz-descriptions-item [nzSpan]="1" [nzTitle]="language_key?.Detail_BiddingPackageValidPeriod || 'Hiệu lực hợp đồng'">
          {{ dataObject.timeserving | number }} {{ language_key?.Detail_Month || 'tháng' }}
        </nz-descriptions-item>
      </nz-descriptions>

      <hr class="hr-dashed" />

      <nz-descriptions [nzColumn]="1">
        <nz-descriptions-item
          [nzSpan]="1"
          [nzTitle]="language_key?.Detail_TechnicalSketchOrSampleImage || 'Bản vẽ kỹ thuật hoặc hình ảnh minh hoạ'"
          *ngIf="dataObject.fileDrawing && dataObject.fileDrawing.length > 0"
        >
          <a href="{{ dataObject.fileDrawing }}" target="_blank">{{ language_key?.Detail_ViewFile || 'Xem file' }}</a>
        </nz-descriptions-item>
        <nz-descriptions-item
          [nzSpan]="1"
          [nzTitle]="language_key?.Detail_ScopeOfWork || 'Phạm vi công việc'"
          *ngIf="dataObject.fileJD && dataObject.fileJD.length > 0"
        >
          <a href="{{ dataObject.fileJD }}" target="_blank">{{ language_key?.Detail_ViewFile || 'Xem file' }} </a>
        </nz-descriptions-item>
        <nz-descriptions-item
          [nzSpan]="1"
          [nzTitle]="language_key?.Detail_KPI_Criteria || 'Tiêu chuẩn đánh giá KPI'"
          *ngIf="dataObject.fileKPI && dataObject.fileKPI.length > 0"
        >
          <a href="{{ dataObject.fileKPI }}" target="_blank">{{ language_key?.Detail_ViewFile || 'Xem file' }} </a>
        </nz-descriptions-item>
        <nz-descriptions-item
          [nzSpan]="1"
          [nzTitle]="language_key?.Detail_Regulations || 'Các quy định về nội quy gói thầu: an toàn, an ninh, VSTP,...'"
          *ngIf="dataObject.fileRule && dataObject.fileRule.length > 0"
        >
          <a href="{{ dataObject.fileRule }}" target="_blank">{{ language_key?.Detail_ViewFile || 'Xem file' }} </a>
        </nz-descriptions-item>
        <nz-descriptions-item
          [nzSpan]="1"
          [nzTitle]="language_key?.Detail_SampleDocuments || 'Tài liệu mẫu (mẫu báo giá, mẫu hợp đồng,...)'"
          *ngIf="dataObject.fileDocument && dataObject.fileDocument.length > 0"
        >
          <a href="{{ dataObject.fileDocument }}" target="_blank">{{ language_key?.Detail_ViewFile || 'Xem file' }} </a>
        </nz-descriptions-item>
        <nz-descriptions-item
          [nzSpan]="1"
          [nzTitle]="language_key?.Detail_Other || 'Khác'"
          *ngIf="dataObject.fileAnother && dataObject.fileAnother.length"
        >
          <a href="{{ dataObject.fileAnother }}" target="_blank">{{ language_key?.Detail_ViewFile || 'Xem file' }} </a>
        </nz-descriptions-item>
      </nz-descriptions>
    </nz-collapse-panel>
  </nz-collapse>
  <nz-collapse nzBordered="true" class="mt-2">
    <nz-collapse-panel [nzHeader]="language_key?.Detail_ItemList || 'Danh sách Item'" class="ant-bg-antiquewhite" nzActive="true">
      <nz-row nzGutter="8" class="mt-2">
        <nz-table
          nz-col
          nzSpan="24"
          [nzFrontPagination]="false"
          [nzData]="['']"
          [nzScroll]="dataObject.listItem?.length > 10 ? { y: '400px' } : { y: null }"
          nzBordered
        >
          <thead>
            <tr>
              <th nzWidth="80px">{{ language_key?.Detail_Index || 'STT' }}</th>
              <th>Item</th>
              <th>{{ language_key?.Detail_BiddingNumberCreated || 'Số lượng tạo thầu' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of dataObject.listItem; let i = index">
              <td>{{ i + 1 }}</td>
              <td>{{ dataItem.itemName }}</td>
              <td class="text-right">{{ dataItem.quantityItem | number }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-row>
    </nz-collapse-panel>
  </nz-collapse>
  <div class="clear-fix"></div>

  <div *ngIf="!hadToken">
    <span class="warning-symbol">*</span>
    <span class="warning-title">{{ language_key?.Detail_Notice || 'Lưu ý: ' }}</span>
    <span class="warning-test"
      >{{ language_key?.Detail_ClickToJoin || 'Để tham gia gói thầu quý NCC vui lòng ấn vào link để đăng ký tham gia' }}
      <a href="{{ link }}">{{ language_key?.Detail_AtHere || ' tại đây' }}</a></span
    >
  </div>
  <div *ngIf="hadToken && isDisplayBtnAcceptBid">
    <span class="warning-symbol">*</span>
    <span class="warning-title">{{ language_key?.Detail_Notice || 'Lưu ý: ' }}</span>
    <span class="warning-test">{{ language_key?.Detail_PleaseReadBeforeConfirm || 'Vui lòng đọc thật kỹ các thông tin trước khi xác nhận' }}</span>
  </div>

  <div class="mt-2 text-center" *ngIf="hadToken">
    <button
      *ngIf="isDisplayBtnAcceptBid"
      nz-popconfirm
      nzType="primary"
      [nzPopconfirmTitle]="language_key?.Detail_DoYouWantToBid || 'Bạn có chắc chắn muốn tham gia đấu thầu?'"
      (nzOnConfirm)="confirmToJoinBidding()"
      nz-button
      class="mr-2"
    >
      {{ language_key?.Detail_Participated || 'Xác nhận tham gia' }}
    </button>

    <button
      *ngIf="isDisplayBtnAcceptBid"
      nz-popconfirm
      nzDanger
      [nzPopconfirmTitle]="language_key?.Detail_AreYouSureNotBidding || 'Bạn có chắc chắn muốn không tham gia đấu thầu?'"
      (nzOnConfirm)="confirmRejectBidding()"
      nz-button
      class="mr-2"
    >
      {{ language_key?.Detail_NotParticipate || 'Không tham gia' }}
    </button>

    <button *ngIf="isDisplayBtnBid" (click)="confirmToBidding()" nz-button nzType="primary" class="mr-2">
      {{ language_key?.Detail_MovingToSubmitBiddingDocument || 'Di chuyển đến trang nộp hồ sơ thầu' }}
    </button>
  </div>
</div>

<div *ngIf="isErr">
  <nz-result nzStatus="error" [nzTitle]="language_key?.Detail_DenyAccess || 'Từ chối truy cập'" [nzSubTitle]="errorText"> </nz-result>
</div>
