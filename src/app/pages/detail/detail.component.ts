import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Params, Router } from '@angular/router'
import { ApiService, AuthenticationService, NotifyService, CoreService, StorageService } from '../../services'
import { enumData } from '../../base/enumData'
import { Subscription } from 'rxjs'
import { LanguageService } from 'src/app/services/language.service'

@Component({
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
})
export class DetailComponent implements OnInit {
  bidid = ''
  dataObject: any = {}
  bidStatus = ''
  hadToken = false
  isDisplayBtnAcceptBid = false
  isDisplayBtnBid = false
  refresh!: number
  link = `${window.location.host}/login`
  isErr = false
  errorText = ''
  language_key: any = {}
  subscriptions: Subscription = new Subscription()

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private apiService: ApiService,
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    private coreService: CoreService,
    private languageService: LanguageService,
    private storageService: StorageService
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.refresh = +params['refresh'] || 0
      this.bidid = params['bidid']
      if (this.refresh > 0) {
        this.loadData()
      }
    })
  }

  ngOnInit() {
    // this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
    //   if (data) {
    //     this.language_key = this.languageService.getLang()
    //   }
    // })
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.loadData()
  }

  loadData = () => {
    this.notifyService.showloading()
    this.isDisplayBtnAcceptBid = false
    this.isDisplayBtnBid = false
    // Đang phát hành
    // Mặc định
    // Đã mở thầu
    const lstStatus1 = [
      enumData.BidStatus.DangDanhGia.code,
      enumData.BidStatus.DangDuyetDanhGia.code,
      enumData.BidStatus.HoanTatDanhGia.code,
      enumData.BidStatus.DangDamPhanGia.code,
      enumData.BidStatus.DongDamPhanGia.code,
      enumData.BidStatus.DangDauGia.code,
      enumData.BidStatus.DongDauGia.code,
      enumData.BidStatus.DongThau.code,
      enumData.BidStatus.DuyetNCCThangThau.code,
      enumData.BidStatus.DangDuyetKetThucThau.code,
    ]
    // Đóng thầu
    const lstStatus2 = [enumData.BidStatus.HoanTat.code, enumData.BidStatus.Huy.code]

    this.hadToken = !!this.authenticationService.currentUserValue
    if (!this.hadToken) {
      this.isErr = true
      this.errorText = this.language_key?.Detail_YouNotAllowToViewDetailPackage || 'Bạn không được xem chi tiết gói thầu, vui lòng đăng nhập trước!'
      return
    }

    Promise.all([
      this.apiService.post(this.apiService.CLIENT_WEB.BID_DETAIL_HAD_TOKEN, { bidId: this.bidid }),
      this.apiService.post(this.apiService.CLIENT_WEB.IS_DISPLAY_BTN_ACCEPT_BID, { bidId: this.bidid }),
      this.apiService.post(this.apiService.CLIENT_WEB.IS_DISPLAY_BTN_BID, { bidId: this.bidid }),
    ])
      .then((values) => {
        this.notifyService.hideloading()
        this.dataObject = values[0] || {}
        this.bidStatus = this.language_key?.Detail_Using || 'Đang phát hành'
        if (lstStatus1.includes(this.dataObject.status)) {
          this.bidStatus = this.language_key?.Detail_OpenBidding || 'Đã mở thầu'
        }
        if (lstStatus2.includes(this.dataObject.status)) {
          this.bidStatus = this.language_key?.Detail_CloseBidding || 'Đóng thầu'
        }
        this.isDisplayBtnAcceptBid = values[1]

        this.isDisplayBtnBid = values[2]
      })
      .catch((err) => {
        this.notifyService.hideloading()
        this.isErr = true
        if (err) this.errorText = err
        else this.errorText = this.language_key?.Detail_NoAccessGrantPackage || 'Bạn không có quyền truy cập gói thầu này!'
      })
  }

  reload() {
    this.router.navigate([`detail`], {
      queryParams: {
        bidid: this.bidid,
        refresh: ++this.refresh,
      },
    })
  }

  confirmToJoinBidding = () => {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CLIENT_WEB.ACCEPT_BID, { bidId: this.bidid }).then((res) => {
      this.notifyService.showSuccess(this.language_key?.Detail_CheckedParticipatedBidding || 'Đã xác nhận tham gia đấu thầu thành công!')
      this.reload()
    })
  }

  confirmRejectBidding = () => {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.CLIENT_WEB.REJECT_BID, { bidId: this.bidid }).then((res) => {
      this.notifyService.showSuccess(this.language_key?.Detail_CheckedNotParticipatedBidding || 'Đã xác nhận không tham gia đấu thầu thành công!')
      this.reload()
    })
  }

  confirmToBidding = () => {
    this.router.navigate([`bidding`], {
      queryParams: { bidid: this.bidid },
    })
  }
}
