import { Component, OnInit } from '@angular/core'
import { Params, ActivatedRoute, Router } from '@angular/router'
import { ApiService, NotifyService } from '../../services'
import { LanguageService } from 'src/app/services/language.service'

@Component({ templateUrl: './auction.component.html', styleUrls: ['./auction.component.scss'] })
export class AuctionComponent implements OnInit {
  auctionId = ''
  dataObject: any = {}

  language_key: any = {}

  constructor(
    private route: ActivatedRoute,
    private languageService: LanguageService,
    private router: Router,
    private apiService: ApiService,
    private notifyService: NotifyService
  ) {
    this.route.queryParams.subscribe((params: Params) => {
      this.auctionId = params['auctionid']
    })
  }

  ngOnInit() {
    this.getAuction()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
  }

  getAuction() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.AUCTION.GET_DETAIL, { id: this.auctionId }).then((res: any) => {
      this.notifyService.hideloading()
      this.dataObject = res
      this.getCD()
    })
  }

  submit() {
    this.notifyService.showloading()
    this.apiService
      .post(this.apiService.AUCTION.SUBMIT, { auctionId: this.auctionId, submitPrice: this.dataObject.submitPriceNew })
      .then((res: any) => {
        this.notifyService.showSuccess(res?.message || 'Đấu giá thành công!')
        this.getAuction()
      })
  }

  gotoHomePage() {
    this.router.navigate([`home`])
  }

  private getCD() {
    setInterval(() => {
      const currentDate = new Date()
      const dateStart = new Date(this.dataObject.dateStart)
      const dateEnd = new Date(this.dataObject.dateEnd)
      if (currentDate.getTime() < dateStart.getTime()) {
        this.dataObject.cdTime = 'Chưa bắt đầu'
        return
      }

      if (currentDate.getTime() > dateEnd.getTime()) {
        this.dataObject.cdTime = 'Đã kết thúc'
        return
      }

      const diffTime = dateEnd.getTime() - currentDate.getTime()
      let s = Math.floor(diffTime / 1000)
      let m = Math.floor(s / 60)
      s = s - m * 60
      let h = Math.floor(m / 60)
      m = m - h * 60
      this.dataObject.cdTime = `${('00' + h).slice(-2)}:${('00' + m).slice(-2)}:${('00' + s).slice(-2)}`
    }, 1000)
  }
}
