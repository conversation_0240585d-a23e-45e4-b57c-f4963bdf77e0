<ul nz-menu nzMode="inline" style="border: 1px solid #e8e8e8">
  <li nz-submenu [nzTitle]="language_key?.PageSideLeft_BiddingField || '<PERSON><PERSON><PERSON> vực đấu thầu'" nzIcon="bars" class="sider-left-menu" nzOpen>
    <ul>
      <ng-container *ngTemplateOutlet="menuTpl; context: { $implicit: services }"></ng-container>
      <ng-template #menuTpl let-services>
        <li nz-menu-divider></li>
        <ng-container *ngFor="let service of services">
          <li
            *ngIf="!service.__childs__?.length"
            nz-menu-item
            [nzPaddingLeft]="service.level * 24"
            class="sider-left-item sider-left-item-border-bottem"
            (click)="viewService(service, $event)"
          >
            <span>{{ service.name }}</span>
          </li>
          <li
            *ngIf="service.__childs__?.length"
            nz-submenu
            [nzPaddingLeft]="service.level * 24"
            [(nzOpen)]="service.open"
            [nzTitle]="service.name"
            class="sider-left-item-border-bottem"
            (click)="viewService(service, $event)"
          >
            <ul>
              <ng-container *ngTemplateOutlet="menuTpl; context: { $implicit: service.__childs__ }"> </ng-container>
            </ul>
          </li>
        </ng-container>
      </ng-template>
    </ul>
  </li>
</ul>
