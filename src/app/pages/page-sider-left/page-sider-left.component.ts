import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { Subscription } from 'rxjs'
import { ApiService, CoreService, StorageService } from 'src/app/services'
import { LanguageService } from '../../services/language.service'

@Component({
  selector: 'app-page-sider-left',
  templateUrl: './page-sider-left.component.html',
  styleUrls: ['./page-sider-left.component.scss'],
})
export class PageSiderLeftComponent implements OnInit {
  services: any[] = []
  language_key: any = {}
  subscriptions: Subscription = new Subscription()

  constructor(
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private router: Router,
    private languageService: LanguageService
  ) {}

  async ngOnInit(): Promise<void> {
    //   this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
    //     if (data) {
    //       //this.language_key = this.coreService.getLanguage()
    //       this.language_key = this.languageService.getLang()
    //     }
    //   })
    //  // this.language_key = this.coreService.getLanguage()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()

    this.services = await this.apiService.post(this.apiService.HOMEPAGE.GET_SERVICES)
  }

  viewService(service: any, event: any) {
    event.stopPropagation()
    let hasChild: boolean = false
    if (service.__childs__?.length > 0) {
      hasChild = true
    }
    if (!service.__childs__?.length || service.open) {
      this.router.navigate([`/home`], {
        queryParams: {
          serviceId: service.id,
          hasChild: hasChild,
        },
      })
    }
  }
}
