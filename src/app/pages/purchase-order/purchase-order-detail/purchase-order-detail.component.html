<nz-row matDialogTitle>
  <nz-col nzSpan="24" class="text-center">
    {{ modalTitle | uppercase }}
  </nz-col>
</nz-row>
<div matDialogContent>
  <nz-tabset>
    <nz-tab [nzTitle]="language_key?.PurchaseOrder_PurchaseOrderDetail_GeneralInformation || 'Thông tin chung'">
      <nz-row style="margin-left: 20px">
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_CodePO || 'Mã PO' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.code }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_TitlePO || 'Tiêu đề PO' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.title }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6" *ngIf="dataObject.contractCode">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_ContractCode || 'Mã Hợp đồng' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.contractCode }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6" *ngIf="dataObject.contractName">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_ContractName || 'Tên Hợp đồng' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.contractName }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6" *ngIf="dataObject.bidCode">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_BiddingPackageCode || 'Mã gói thầu' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.bidCode }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6" *ngIf="dataObject.bidName">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_BiddingPackageName || 'Tên gói thầu' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.bidName }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_DateCreated || 'Ngày tạo' }} </b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.createdAt | date : 'dd/MM/yyyy' }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_DeliveryDate || 'Ngày giao hàng' }} </b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.deliveryDate | date : 'dd/MM/yyyy' }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_ValuePO || 'Giá trị PO ' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.money | number }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Status || 'Trạng thái' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span
                ><nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag></span
              >
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_FormPO || 'Hình thức PO' }} </b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.paymentName }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_CurrencyUnit || 'Đơn vị tiền tệ' }} </b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.currency }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Email || 'Email' }} </b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.email }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Buyer || 'Bên mua hàng' }} </b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.companyName }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
        <nz-col nzSpan="6">
          <nz-form-item>
            <nz-form-label nzSpan="24" class="text-left"
              ><b>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_PhoneNumber || 'Số điện thoại ' }}</b></nz-form-label
            >
            <nz-form-control nzSpan="24">
              <span>{{ dataObject.phone }}</span>
            </nz-form-control>
          </nz-form-item>
        </nz-col>
      </nz-row>
    </nz-tab>
    <nz-tab
      class="ant-bg-antiquewhite"
      [nzTitle]="language_key?.PurchaseOrder_PurchaseOrderDetail_HistoryChanges || 'Lịch sử thay đổi'"
      *ngIf="dataObject?.lstHistory?.length > 0"
    >
      <nz-col nzSpan="24">
        <nz-table nz-col nzSpan="24" *ngIf="dataObject.lstHistory" [nzData]="['']" nzBordered [nzShowPagination]="false">
          <thead>
            <tr class="text-nowrap">
              <th></th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Time || 'Thời gian' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Implementer || 'Người thực hiện' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_OldState || 'Trạng thái cũ' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_CurrentState || 'Trạng thái hiện tại' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_ContentChanges || 'Nội dung thay đổi' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataH of dataObject.lstHistory; let i = index">
              <td class="text-nowrap">{{ i + 1 }}</td>
              <td class="text-nowrap">{{ dataH.createdAt | date : 'dd.MM.yyy HH:mm' }}</td>
              <td>{{ dataH.createdByName }}</td>
              <td class="text-nowrap">{{ dataH.statusCurrentName }}</td>
              <td class="text-nowrap">
                <b>{{ dataH.statusConvertName }}</b>
              </td>
              <td>{{ dataH.description }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-col>
    </nz-tab>
    <nz-tab
      class="ant-bg-antiquewhite"
      [nzTitle]="language_key?.PurchaseOrder_PurchaseOrderDetail_ListProducts || 'Danh sách sản phẩm'"
      *ngIf="dataObject?.lstProducts?.length > 0"
    >
      <nz-col nzSpan="24">
        <nz-table nz-col nzSpan="24" *ngIf="dataObject.lstProducts" [nzData]="['']" nzBordered [nzShowPagination]="false">
          <thead>
            <tr class="text-nowrap">
              <th></th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Supplies || 'Vật tư' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_GoodsName || 'Tên hàng hóa' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Unit || 'Đơn vị tính' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Quantity || 'Số lượng' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Price || 'Giá' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_IntoMoney || 'Thành tiền' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_DescriptionGoods || 'Mô tả hàng hóa' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_Note || 'Ghi chú' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataH of dataObject.lstProducts; let i = index">
              <td class="text-nowrap">{{ i + 1 }}</td>
              <td>{{ dataH.serviceName }}</td>
              <td>{{ dataH.name }}</td>
              <td>{{ dataH.unit }}</td>
              <td class="text-right">{{ dataH.quantity | number }}</td>
              <td class="text-right">{{ dataH.price | number }}</td>
              <td class="text-right">{{ dataH.money | number }}</td>
              <td>{{ dataH.description }}</td>
              <td>{{ dataH.note }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-col>
    </nz-tab>
    <nz-tab
      class="ant-bg-antiquewhite"
      [nzTitle]="language_key?.PurchaseOrder_PurchaseOrderDetail_PaymentProgress || 'Tiến độ thanh toán'"
      *ngIf="dataObject?.lstPaymentPlan?.length > 0"
    >
      <nz-col nzSpan="24">
        <nz-table nz-col nzSpan="24" *ngIf="dataObject.lstPaymentPlan" [nzData]="['']" nzBordered [nzShowPagination]="false">
          <thead>
            <tr class="text-nowrap">
              <th></th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_ProgressName || 'Tên tiến độ' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_ProgressPercent || 'Phần trăm tiến độ' }}</th>
              <th>{{ language_key?.PurchaseOrder_PurchaseOrderDetail_PaymentStatus || 'Trạng thái thanh toán' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataH of dataObject.lstPaymentPlan; let i = index">
              <td class="text-nowrap">{{ i + 1 }}</td>
              <td>{{ dataH.name }}</td>
              <td class="text-right">{{ dataH.percent | number }}</td>
              <td>{{ dataH.paymentName }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-col>
    </nz-tab>

    <!-- TO DO -->
    <nz-tab class="ant-bg-antiquewhite" [nzTitle]="'Danh sách hóa đơn'" *ngIf="dataObject?.lstFile?.length > 0">
      <nz-col nzSpan="24">
        <nz-table nz-col nzSpan="24" *ngIf="dataObject.lstFile" [nzData]="['']" nzBordered [nzShowPagination]="false">
          <thead>
            <tr class="text-nowrap">
              <th>{{ 'Tên file' }}</th>
              <th>{{ 'Tiên tiến độ' }}</th>
              <th>{{ 'URL' }}</th>
              <th>{{ 'Số hóa đơn' }}</th>
              <th>{{ 'Ngày gửi hóa đơn' }}</th>
              <th>{{ 'File hóa đơn' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataH of dataObject.lstFile; let i = index">
              <td class="text-nowrap">{{ dataH.fileName }}</td>
              <td class="text-nowrap">{{ dataH.progressName }}</td>
              <td class="text-nowrap">{{ dataH.url }}</td>
              <td class="text-nowrap">{{ dataH.invoiceNo }}</td>
              <td>{{ dataH.createdAt | date : 'dd/MM/yyyy' }}</td>
              <td class="text-center">
                <a [href]="dataH.fileUrl" download>{{ dataH.fileName }}</a>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-col>
    </nz-tab>
  </nz-tabset>
</div>

<thead></thead>
