<form nz-form #frmAdd="ngForm">
  <nz-row matDialogTitle>
    <nz-col nzSpan="24" class="text-center">
      {{ modalTitle | uppercase }}
    </nz-col>
  </nz-row>

  <div matDialogContent>
    <nz-row nzGutter="8">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Chọn tiến độ thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn tiến độ thanh toán ">
            <nz-select
              *ngIf="!dataObject.id"
              nzShowSearch
              nzAllowClear
              nzPlaceHolder="Chọn tiến độ thanh toán"
              [(ngModel)]="dataObject.paymentPlanId"
              name="paymentPlanId"
              required
              (ngModelChange)="changePaymentPlan($event)"
            >
              <nz-option *ngFor="let item of dataPaymentPlan" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
            </nz-select>
            <input *ngIf="!!dataObject.id" nz-input disabled [(ngModel)]="dataObject.paymentPlanName" name="paymentPlanName" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nz-tooltip nzTooltipTitle="Lấy theo tiến độ thanh toán"> Số tiền cần thanh toán </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.poMoney" name="poMoney" required currencyMask [options]="{ align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nz-tooltip nzTooltipTitle="Lấy theo tiến độ thanh toán"> Số tiền đã ĐNTT </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input nz-input disabled [(ngModel)]="dataObject.suggestPaid" name="suggestPaid" required currencyMask [options]="{ align: 'left' }" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired> Số tiền ĐNTT </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input
              nz-input
              [(ngModel)]="dataObject.money"
              name="money"
              required
              pattern=".{1,250}"
              currencyMask
              [options]="{ prefix: '', precision: 0, allowNegative: false, align: 'left' }"
            />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Số hóa đơn</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Số hóa đơn">
            <input nz-input placeholder="Nhập Số hóa đơn" [(ngModel)]="dataObject.invoiceNo" name="invoiceNo" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Đường link hoá đơn điện tử</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nhập đường link hoá đơn điện tử">
            <input nz-input placeholder="Nhập Đường link hoá đơn điện tử" [(ngModel)]="dataObject.url" name="url" />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <!-- <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Chiết khấu</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <input
              nz-input
              placeholder="Nhập Chiết khấu"
              [(ngModel)]="dataObject.discount"
              name="discount"
              currencyMask
              [options]="{ precision: 2, allowNegative: false, align: 'left' }"
            />
          </nz-form-control>
        </nz-form-item>
      </nz-col> -->

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired> Thời gian quyết toán </nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng chọn Thời gian quyết toán">
            <nz-date-picker nzFormat="dd-MM-yyyy" [(ngModel)]="dataObject.invoiceDate" name="invoiceDate" required> </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Đơn vị hưởng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Đơn vị hưởng">
            <input nz-input placeholder="Nhập Đơn vị hưởng" [(ngModel)]="dataObject.beneficiaryUnit" name="beneficiaryUnit" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Ngân hàng</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Ngân hàng">
            <input nz-input placeholder="Nhập Ngân hàng" [(ngModel)]="dataObject.bankName" name="bankName" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Số tài khoản</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng Số tài khoản">
            <input nz-input placeholder="Nhập Số tài khoản" [(ngModel)]="dataObject.bankAccountNo" name="bankAccountNo" required />
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>File liên quan</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24">
            <nz-upload
              [nzAction]="apiService.UploadUrl"
              nzListType="picture-card"
              [nzHeaders]="{ authorization: 'authorization-text' }"
              [(nzFileList)]="dataObject.fileList"
              [nzPreview]="handlePreview"
              (nzChange)="onChangeImage($event)"
            >
              <div>
                <span nz-icon nzType="plus"></span>
                <div style="margin-top: 8px">Upload</div>
              </div>
            </nz-upload>
            <nz-modal [nzVisible]="previewVisible" [nzContent]="modalContent" [nzFooter]="null" (nzOnCancel)="previewVisible = false">
              <ng-template #modalContent>
                <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
              </ng-template>
            </nz-modal>
          </nz-form-control>
        </nz-form-item>
      </nz-col>

      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="6" [nzXs]="24">Nội dung thanh toán</nz-form-label>
          <nz-form-control [nzSm]="14" [nzXs]="24" nzErrorTip="Vui lòng nội dung thanh toán">
            <textarea rows="4" nz-input [(ngModel)]="dataObject.description" name="description"></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </div>

  <nz-row matDialogActions>
    <nz-col nzSpan="24" class="text-center">
      <button nz-button *ngIf="!dataObject.id" [disabled]="!frmAdd.form.valid" nzType="primary" (click)="onSave()" class="mr-2">
        Tạo đề nghị thanh toán
      </button>
    </nz-col>
  </nz-row>
</form>
