import { Component, Inject, Optional } from '@angular/core'
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { NotifyService, CoreService, StorageService, ApiService, AuthenticationService } from 'src/app/services'
import { LanguageService } from '../../../services/language.service'
import { enumData } from 'src/app/base/enumData'

@Component({ templateUrl: './purchase-order-detail.component.html' })
export class PurchaseOrderDetailComponent {
  modalTitle = 'Chi Tiết PO'
  dataObject: any = {}
  dataHistoryParent: any
  language_key: any = {}
  enumData: any
  maxSizeUpload = enumData.maxSizeUpload
  subscriptions: Subscription = new Subscription()
  currentStatus: any = {}
  constructor(
    private authenticationService: AuthenticationService,
    private notifyService: NotifyService,
    public coreService: CoreService,
    private storageService: StorageService,
    private apiService: ApiService,
    private languageService: LanguageService,
    private dialogRef: MatDialogRef<PurchaseOrderDetailComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    // this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
    //   if (data)
    //   //this.language_key = this.coreService.getLanguage()
    //   this.language_key = this.languageService.getLang()
    // })
    // //this.language_key = this.coreService.getLanguage()
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.dataObject = this.data
    this.loadDetail(this.data)
  }

  async handleFileInput(event: any, field: string, item: any) {
    const fileToUpload = event.target.files[0]
    if (fileToUpload && fileToUpload.size > this.maxSizeUpload * 1024 * 1024) {
      this.notifyService.showWarning(
        (this.language_key?.BidDeal_FileExceedMaxSize || 'Vượt qua kích thước tối đa.') +
          `Kích thước tối đa để upload là ${this.maxSizeUpload}MB, vui lòng chọn file khác`
      )
      return
    }

    if (fileToUpload) {
      const formData: FormData = new FormData()
      formData.append('file', fileToUpload, fileToUpload.name)
      await this.apiService.post(this.apiService.UPLOAD_FILE.UPLOAD_SINGLE, formData).then((res) => {
        if (res && res.length) item[field] = res[0]
        else item[field] = ''
        this.notifyService.showSuccess(res?.message || 'Gửi thành công!')
      })
    }
    //TO DO
  }

  async loadDetail(data: any) {
    this.notifyService.showloading()
    await this.apiService.post(this.apiService.PURCHASE_ORDER.FIND_DETAIL, data).then(async (result) => {
      this.notifyService.hideloading()
      this.dataObject = result
    })
    await this.apiService.post(this.apiService.PURCHASE_ORDER.FIND_DETAIL_FILE, { id: data.id }).then(async (result) => {
      this.dataObject.lstFile = result
    })
  }

  closeDialog(flag: any) {
    this.dialogRef.close(flag)
  }
}
