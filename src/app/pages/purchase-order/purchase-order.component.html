<nz-row class="mt-3">
  <h2>{{ language_key?.PurchaseOrder_POList || 'DANH SÁCH PO' }}</h2>
</nz-row>

<nz-row class="mt-3" nzGutter="8">
  <nz-col nzSpan="8">
    <input nz-input [(ngModel)]="dataSearch.code" [placeholder]="language_key?.PurchaseOrder_POSearchCode || 'Tìm theo mã PO'" class="input-enter" />
  </nz-col>
  <nz-col nzSpan="8">
    <input
      nz-input
      [(ngModel)]="dataSearch.title"
      [placeholder]="language_key?.PurchaseOrder_POSearchName || 'Tìm theo tên PO'"
      class="input-enter"
    />
  </nz-col>
  <nz-col nzSpan="8">
    <nz-select
      nzShowSearch
      nzAllowClear
      [(ngModel)]="dataSearch.status"
      [nzPlaceHolder]="language_key?.PurchaseOrder_ChooseStatus || 'Chọn trạng thái'"
    >
      <nz-option *ngFor="let item of dataStatus" [nzLabel]="item.name" [nzValue]="item.code"></nz-option>
    </nz-select>
  </nz-col>
</nz-row>
<nz-row nzGutter="8" class="mt-2">
  <nz-col nzSpan="24" class="text-center">
    <button nz-button (click)="searchData(true)">
      <span nz-icon nzType="search"></span>
      {{ language_key?.PurchaseOrder_Search || 'Tìm kiếm' }}
    </button>
  </nz-col>
</nz-row>

<nz-row class="mt-3">
  <nz-table
    nz-col
    nzSpan="24"
    class="mb-3"
    [nzData]="listOfData"
    [(nzPageSize)]="pageSize"
    [nzLoading]="loading"
    [nzShowPagination]="false"
    nzBordered
    [nzScroll]="{ x: '2000px' }"
    nzTableLayout="fixed"
  >
    <thead>
      <tr>
        <th nzLeft>{{ language_key?.PurchaseOrder_POCode || 'Mã PO' }}</th>
        <th>{{ language_key?.PurchaseOrder_POName || 'Tên PO' }}</th>
        <th>{{ language_key?.PurchaseOrder_ContractCode || 'Mã Hợp đồng' }}</th>
        <th>{{ language_key?.PurchaseOrder_BiddingPackageName || 'Tên Gói Thầu' }}</th>
        <th>{{ language_key?.PurchaseOrder_CreatedDate || 'Ngày tạo' }}</th>
        <th>{{ language_key?.PurchaseOrder_POValue || 'Giá trị PO' }}</th>
        <th>{{ language_key?.PurchaseOrder_DateDelivery || 'Ngày giao hàng' }}</th>
        <th>{{ language_key?.PurchaseOrder_POStatus || 'Trạng thái PO' }}</th>
        <th>{{ language_key?.PurchaseOrder_POOrderStatus || 'Trạng thái đơn hàng' }}</th>
        <th nzRight>{{ language_key?.PurchaseOrder_Action || 'Tác vụ' }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listOfData">
        <td nzLeft>{{ data.code }}</td>
        <td>{{ data.title }}</td>
        <td>{{ data.contractCode }}</td>
        <td>{{ data.bidName }}</td>
        <td>{{ data.createdAt | date : 'dd/MM/yyyy' }}</td>
        <td>{{ data.money | number }}</td>
        <td>{{ data.deliveryDate | date : 'dd/MM/yyyy' }}</td>
        <td>
          <nz-tag class="tag-status" [nzColor]="data.statusColor"> {{ data.statusName }}</nz-tag>
        </td>
        <td>
          <nz-tag class="tag-status" [nzColor]="data.statusOrderColor"> {{ data.statusOrderName }}</nz-tag>
        </td>
        <td nzRight>
          <button
            nz-tooltip
            (click)="showDetail(data)"
            [nzTooltipTitle]="language_key?.PurchaseOrder_ViewDetail || 'Xem chi tiết'"
            class="ant-btn-blue ml-2 mb-2 mt-2 mr-2"
            nz-button
          >
            <span nz-icon nzType="eye"></span>
          </button>

          <button
            *ngIf="data.status === enumStatus?.Approved?.code"
            nz-tooltip
            [nzTooltipTitle]="language_key?.PurchaseOrder_POConfirm || 'Xác nhận PO'"
            nz-popconfirm
            [nzPopconfirmTitle]="language_key?.PurchaseOrder_SurePOConfirm || 'Bạn có chắc muốn Xác nhận PO ?'"
            (nzOnConfirm)="confirmPO(data)"
            nzPopconfirmPlacement="bottom"
            class="ant-btn-success ml-2 mb-2 mt-2 mr-2"
            nz-button
          >
            <span nz-icon nzType="check"></span>
          </button>

          <button
            *ngIf="data.status !== enumStatus?.Cancel?.code"
            nz-tooltip
            nzTooltipTitle="Chỉnh ngày dự kiến giao hàng"
            (click)="showForwardCancel(data)"
            class="ant-btn-success ml-2 mb-2 mt-2 mr-2"
            nz-button
            nzType="dashed"
          >
            <span nz-icon nzType="edit"></span>
          </button>
          <button
            nz-tooltip
            nzTooltipTitle="Cập nhật trạng thái đơn"
            (click)="showForwardCancel2(data)"
            class="ant-btn-success ml-2 mb-2 mt-2 mr-2"
            nz-button
            nzType="dashed"
          >
            <span nz-icon nzType="form"></span>
          </button>

          <button
            for="fileTech"
            nz-tooltip
            [nzTooltipTitle]="language_key?.PurchaseOrder_ConfirmShip || 'Cập nhật hóa đơn'"
            nzPopconfirmPlacement="bottom"
            class="ant-btn-success ml-2 mb-2 mt-2 mr-2"
            nz-button
            (click)="clickAdd(data)"
          >
            <span nz-icon nzType="upload" nzTheme="outline"></span>
          </button>

          <button
            *ngIf="data.status === enumStatus?.Approved?.code || data.status === enumStatus?.Confirm?.code"
            nz-tooltip
            [nzTooltipTitle]="language_key?.PurchaseOrder_RejectShip || 'Từ Chối Giao Hàng'"
            nz-popconfirm
            (click)="clickRefusePO(data)"
            nzPopconfirmPlacement="bottom"
            class="ant-btn-danger ml-2 mb-2 mt-2 mr-2"
            nz-button
          >
            <span nz-icon nzType="delete"></span>
          </button>

          <button
            *ngIf="data.status === enumStatus.Confirm.code"
            nz-tooltip
            [nzTooltipTitle]="language_key?.PurchaseOrder_ConfirmShip || 'Xác nhận giao hàng'"
            nz-popconfirm
            [nzPopconfirmTitle]="language_key?.PurchaseOrder_SureConfirmShip || 'Bạn có chắc muốn Xác nhận giao hàng ?'"
            (nzOnConfirm)="confirmDelivery(data)"
            nzPopconfirmPlacement="bottom"
            class="ant-btn-success ml-2 mb-2 mt-2 mr-2"
            nz-button
          >
            <span nz-icon nzType="delivered-procedure"></span>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-pagination
    [nzTotal]="total"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    (nzPageIndexChange)="searchData()"
    (nzPageSizeChange)="searchData(true)"
    [nzShowTotal]="rangeTemplate"
    nzShowSizeChanger
  >
  </nz-pagination>
  <ng-template #rangeTemplate let-range="range" let-total> {{ range[0] }}-{{ range[1] }} of {{ total }} items </ng-template>
</nz-row>

<nz-modal
  [(nzVisible)]="isVisibleRefuse"
  [nzTitle]="language_key?.PurchaseOrder_EnterReasonReject || 'Nhập lý do từ chối'"
  (nzOnCancel)="handleCancel()"
  [nzWidth]="'60vw'"
>
  <ng-container *nzModalContent>
    <nz-row class="mt-3">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired class="text-left">{{ language_key?.PurchaseOrder_Reason || 'Lý do' }}</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24" [nzErrorTip]="language_key?.PurchaseOrder_EnterName || 'Vui lòng nhập tên (1-250 kí tự)!'">
            <textarea
              nz-input
              rows="5"
              auto
              [placeholder]="language_key?.PurchaseOrder_EnterReason || 'Nhập lý do'"
              [(ngModel)]="dataRefuse.reason"
            ></textarea>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter class="text-center">
    <button [disabled]="!dataRefuse.reason || dataRefuse.reason === ''" nzDanger (click)="confirmRefuse()" nz-button>
      <span nz-icon nzType="delete"></span>{{ language_key?.PurchaseOrder_Cancel || 'Hủy' }}
    </button>
    <button (click)="isVisibleRefuse = false" nz-button>
      <span nz-icon nzType="close"></span> {{ language_key?.PurchaseOrder_Close || 'Đóng' }}
    </button>
  </div>
</nz-modal>

<nz-modal [(nzVisible)]="isEditDate" nzTitle="Cập nhật ngày dự kiến giao hàng" (nzOnCancel)="isEditDate = false">
  <ng-container *nzModalContent>
    <nz-row style="text-align: center; justify-content: center; display: flex">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label class="text-left" [nzSm]="24" [nzXs]="24">ngày dự kiến giao hàng</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <nz-date-picker
              nzPlaceHolder="Chọn ngày dự kiến giao hàng"
              nzFormat="dd/MM/yyyy"
              [(ngModel)]="dataObject.deliveryDate"
              name="deliveryDate"
            >
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter>
    <!-- <button
      *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
      [disabled]="!dataObject.reason || dataObject.reason === ''"
      nzType="primary"
      (click)="onSaveDate()"
      nz-button
    > -->
    <button [disabled]="!dataObject.deliveryDate || dataObject.deliveryDate === ''" nzType="primary" (click)="onSaveDate()" nz-button>
      <span nz-icon nzType="save" nzTheme="outline"></span>Lưu
    </button>
    <button (click)="isEditDate = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>

<nz-modal [(nzVisible)]="isEditStatus" nzTitle="Cập nhật trạng thái đơn hàng" (nzOnCancel)="isEditStatus = false">
  <ng-container *nzModalContent>
    <nz-row style="text-align: center; justify-content: center; display: flex">
      <nz-col nzSpan="24">
        <nz-form-item nzFlex>
          <nz-form-label class="text-left" [nzSm]="24" [nzXs]="24">Trạng thái đơn</nz-form-label>
          <nz-form-control [nzSm]="24" [nzXs]="24">
            <nz-select nzShowSearch [(ngModel)]="dataObjectUpdate.orderStatus" [ngModelOptions]="{ standalone: true }" nzPlaceHolder="Trạng thái đơn">
              <nz-option *ngFor="let item of dataOrderStatus" [nzLabel]="item.name" [nzValue]="item.code"> </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </nz-col>
    </nz-row>
  </ng-container>
  <div *nzModalFooter>
    <!-- <button
      *ngIf="authenticationService.checkPermission([enumRole], action.Delete.code)"
      [disabled]="!dataObject.reason || dataObject.reason === ''"
      nzType="primary"
      (click)="onSaveDate()"
      nz-button
    > -->
    <button [disabled]="!dataObjectUpdate.orderStatus" nzType="primary" (click)="onSaveStatus()" nz-button>
      <span nz-icon nzType="save" nzTheme="outline"></span>Lưu
    </button>
    <button (click)="isEditStatus = false" nz-button><span nz-icon nzType="close"></span> Đóng</button>
  </div>
</nz-modal>
