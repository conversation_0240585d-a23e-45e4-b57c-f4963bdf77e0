import { Component, OnInit } from '@angular/core'
import { MatDialog } from '@angular/material/dialog'
import { Subscription } from 'rxjs'
import { enumData } from 'src/app/base/enumData'
import { AuthenticationService, ApiService, CoreService, StorageService, NotifyService } from 'src/app/services'
import { PurchaseOrderDetailComponent } from './purchase-order-detail/purchase-order-detail.component'
import { LanguageService } from '../../services/language.service'
import $ from 'jquery'
import { UpdateOrderComponent } from './purchase-order-detail/update-order/update-order.component'

@Component({ templateUrl: './purchase-order.component.html' })
export class PurchaseOrderComponent implements OnInit {
  language_key: any = {}
  loading = false
  listOfData: any[] = []
  isVisibleRefuse = false
  enumData: any
  maxSizeUpload = enumData.maxSizeUpload
  subscriptions: Subscription = new Subscription()
  dataSearch: any = {}
  isEditDate = false
  isEditStatus = false
  modalTitle = enumData.Constants.Model_Add
  pageIndex = enumData.Page.pageIndex
  pageSize = enumData.Page.pageSize
  total = enumData.Page.total
  enumStatus: any
  dataRefuse: any
  dataOrderStatus: any
  dataObject: any
  dataObjectUpdate: any
  dataStatus: any = []
  constructor(
    private authenticationService: AuthenticationService,
    private apiService: ApiService,
    public coreService: CoreService,
    private storageService: StorageService,
    private dialog: MatDialog,
    private notifyService: NotifyService,
    private languageService: LanguageService
  ) {
    this.authenticationService.currentUser.subscribe((x: any) => (this.enumData = x.enumData))
  }

  ngOnInit() {
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.subscriptions = this.storageService.watchStorage().subscribe((data: string) => {
      if (data) {
        this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Client.BiddingHistory.data.History.code)
      }
    })
    this.language_key = this.coreService.getLanguageByComponent(this.enumData.Component_Client.BiddingHistory.data.History.code)
    this.dataOrderStatus = this.coreService.convertObjToArray(this.enumData.PoOrder)
    this.dataSearch = new Object()
    this.dataObject = new Object()
    this.dataRefuse = new Object()
    this.enumStatus = this.enumData.PurchaseOrderStatus
    this.dataStatus = this.coreService.convertObjToArray(this.enumData.PurchaseOrderStatus)
    this.searchData()
  }
  ngAfterViewInit() {
    $('.input-enter').on('keyup', (e) => {
      if (e.key === 'Enter' || e.keyCode === 13) {
        this.searchData()
      }
    })
  }

  async searchData(reset = false) {
    if (reset) this.pageIndex = 1
    this.loading = true

    const where: any = {}
    if (this.dataSearch.title && this.dataSearch.title !== '') {
      where.title = this.dataSearch.title
    }
    if (this.dataSearch.code && this.dataSearch.code !== '') {
      where.code = this.dataSearch.code
    }
    if (this.dataSearch.status && this.dataSearch.status !== '') {
      where.status = this.dataSearch.status
    }
    const dataSearch: any = {
      where,
      skip: (this.pageIndex - 1) * this.pageSize,
      take: this.pageSize,
    }
    this.apiService.post(this.apiService.PURCHASE_ORDER.PAGINATION, dataSearch).then((data) => {
      if (data) {
        this.loading = false
        this.total = data[1]
        this.listOfData = data[0]
      }
    })
  }

  clickAdd(object: any) {
    this.dialog
      .open(UpdateOrderComponent, { disableClose: false, data: object })
      .afterClosed()
      .subscribe(() => {
        this.searchData()
      })
  }

  showDetail(data: any) {
    this.dialog
      .open(PurchaseOrderDetailComponent, { disableClose: false, data })
      .afterClosed()
      .subscribe((res) => {
        this.searchData()
      })
  }

  confirmDelivery(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_ORDER.UPDATE_DELIVERY, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  confirmPO(data: any) {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_ORDER.UPDATE_CONFIRM, { id: data.id }).then((res) => {
      this.notifyService.showSuccess(res.message)
      this.searchData()
    })
  }

  clickRefusePO(data: any) {
    this.isVisibleRefuse = true
    this.dataObject = data
  }

  confirmRefuse() {
    const data = this.dataObject
    if (this.dataRefuse.reason == null || this.dataRefuse.reason.trim().length === 0) {
      this.notifyService.showError(`${this.language_key?.PurchaseOrder_EnterReason || 'Vui lòng nhập lý do'}'`)
      return
    }
    data.reason = this.dataRefuse.reason
    this.apiService.post(this.apiService.PURCHASE_ORDER.UPDATE_REFUSE, data).then(() => {
      this.notifyService.showSuccess(`${this.language_key?.PurchaseOrder_RejectShipSuccess || 'Gửi xác nhận từ chối giao hàng thành công.'}`)
      this.isVisibleRefuse = false
      this.searchData()
    })
  }

  handleCancel() {
    this.isVisibleRefuse = false
  }
  showForwardCancel(data: any) {
    this.dataObject = { ...data }
    this.dataObject.isSupplier = true
    this.isEditDate = true
  }
  showForwardCancel2(data: any) {
    this.dataObjectUpdate = { ...data }
    this.dataObjectUpdate.isSupplier = true
    this.isEditStatus = true
  }

  onSaveDate() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_ORDER.UPDATE_DELIVERY_DATE, this.dataObject).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
        this.isEditDate = false
      }
    })
  }

  onSaveStatus() {
    this.notifyService.showloading()
    this.apiService.post(this.apiService.PURCHASE_ORDER.UPDATE_STATUS, this.dataObjectUpdate).then((result) => {
      if (result) {
        this.notifyService.showSuccess(enumData.Constants.Message_Update_Success)
        this.searchData(true)
        this.isEditStatus = false
      }
    })
  }
}
