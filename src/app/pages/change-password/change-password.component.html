<form nz-form [formGroup]="validateForm" (ngSubmit)="submitForm()">
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="password" nzRequired>{{
      this.language_key?.ChangePassword_CurrentPassword || 'Mật khẩu hiện tại'
    }}</nz-form-label>
    <nz-form-control
      [nzSm]="24"
      [nzXs]="24"
      [nzErrorTip]="this.language_key?.ChangePassword_PleaseInputCurrentPassword || 'Vui lòng nhập mật khẩu hiện tại!'"
    >
      <input nz-input type="password" id="password" formControlName="password" (ngModelChange)="updateConfirmValidator()" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="password" nzRequired>{{
      this.language_key?.ChangePassword_NewPassword || 'Mật khẩu mới'
    }}</nz-form-label>
    <nz-form-control [nzSm]="24" [nzXs]="24" [nzErrorTip]="this.language_key?.ChangePassword_PleaseInputNewPassword || 'Vui lòng nhập mật khẩu mới!'">
      <input nz-input type="password" id="newPassword" formControlName="newPassword" (ngModelChange)="updateConfirmValidator()" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="confirmNewPassword" nzRequired>{{
      this.language_key?.ChangePassword_PleaseInputNewPassword || 'Nhập lại mật khẩu mới'
    }}</nz-form-label>
    <nz-form-control [nzSm]="24" [nzXs]="24" [nzErrorTip]="errorTpl">
      <input nz-input type="password" formControlName="confirmNewPassword" id="confirmNewPassword" />
      <ng-template #errorTpl let-control>
        <ng-container *ngIf="control.hasError('required')"
          >{{ this.language_key?.ChangePassword_PleaseReinputNewPassword || 'Vui lòng nhập lại mật khẩu mới!' }}
        </ng-container>
        <ng-container *ngIf="control.hasError('confirm')">{{
          this.language_key?.ChangePassword_PasswordNotMatch || 'Mật khẩu không khớp!'
        }}</ng-container>
      </ng-template>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item nz-row class="mt-2">
    <nz-form-control>
      <button nz-button nzType="primary" [disabled]="validateForm.invalid">
        {{ this.language_key?.ChangePassword_ChangePassword || 'Đổi mật khẩu' }}
      </button>
    </nz-form-control>
  </nz-form-item>
</form>
