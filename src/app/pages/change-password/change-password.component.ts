import { Component, OnInit } from '@angular/core'
import { AuthenticationService, NotifyService } from 'src/app/services'
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms'
import { LanguageService } from 'src/app/services/language.service'

@Component({ templateUrl: './change-password.component.html' })
export class ChangePasswordComponent implements OnInit {
  validateForm: any = FormGroup
  language_key: any = {}
  constructor(
    private fb: FormBuilder,
    private languageService: LanguageService,
    private authService: AuthenticationService,
    private notifyService: NotifyService
  ) {}

  ngOnInit() {
    this.notifyService.showloading()
    this.validateForm = this.fb.group({
      password: [null, [Validators.required]],
      newPassword: [null, [Validators.required]],
      confirmNewPassword: [null, [Validators.required, this.confirmationValidator]],
    })
    this.language_key = this.languageService.getLang()
    this.languageService.getData()
    this.notifyService.hideloading()
  }

  submitForm() {
    for (const i in this.validateForm.controls) {
      if (this.validateForm.controls[i]) {
        this.validateForm.controls[i].markAsDirty()
        this.validateForm.controls[i].updateValueAndValidity()
      }
    }
    if (this.validateForm.valid) {
      this.notifyService.showloading()
      this.authService
        .updatePassword(this.validateForm.value.password, this.validateForm.value.newPassword, this.validateForm.value.confirmNewPassword)
        .toPromise()
        .then((res: any) => {
          this.notifyService.showSuccess(res.message)
        })
    }
  }

  updateConfirmValidator() {
    /** wait for refresh value */
    Promise.resolve().then(() => this.validateForm.controls.confirmNewPassword.updateValueAndValidity())
  }

  confirmationValidator = (control: FormControl) => {
    if (!control.value) {
      return { required: true }
    } else if (control.value !== this.validateForm.controls.newPassword.value) {
      return { confirm: true, error: true }
    }
    return {}
  }
}
