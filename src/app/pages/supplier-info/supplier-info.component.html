<h2>{{ language_key?.SupplierInfor_AccountInformation || 'Thông tin tài khoản' }}</h2>

<nz-tabset>
  <nz-tab [nzTitle]="language_key?.SupplierInfor_GeneralInformation || 'Thông tin chung'">
    <form nz-form [formGroup]="validateForm">
      <nz-collapse nzBordered="false">
        <nz-collapse-panel
          [nzHeader]="language_key?.SupplierInfor_GeneralInformation || 'Thông tin chung'"
          class="ant-bg-antiquewhite"
          nzActive="true"
        >
          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="name" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_CompanyName || 'Tên doanh nghiệp' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterCompanyName || 'Vui lòng nhập tên doanh nghiệp (1-250 kí tự)!'"
                >
                  <input nz-input formControlName="name" id="name" [(ngModel)]="dataObject.name" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="dealName" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_TradingName || 'Tên giao dịch' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterTradingName || 'Vui lòng nhập tên giao dịch (1-250 kí tự)!'"
                >
                  <input nz-input formControlName="dealName" id="dealName" [(ngModel)]="dataObject.dealName" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="createYear" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_CompanyCreateYear || 'Năm thành lập công ty' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterCompanyCreateYear || 'Vui lòng nhập năm thành lập công ty!'"
                >
                  <input
                    class="input-rtl"
                    nz-input
                    formControlName="createYear"
                    id="createYear"
                    [(ngModel)]="dataObject.createYear"
                    type="number"
                    min="0"
                    required
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="code" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_BusinessCode || 'Mã số doanh nghiệp' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterBusinessCode || 'Vui lòng nhập mã số doanh nghiệp (1-50 kí tự)!'"
                >
                  <input nz-input formControlName="code" id="code" [(ngModel)]="dataObject.code" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="address" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_OfficeAddress || 'Địa chỉ trụ sở' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterOfficeAddress || 'Vui lòng chọn địa chỉ trụ sở (1-250 kí tự)!'"
                >
                  <nz-input-group nzSearch>
                    <input
                      nz-input
                      [placeholder]="language_key?.SupplierInfor_ChooseOfficeAddress || 'Chọn địa chỉ trụ sở'"
                      [(ngModel)]="dataObject.address"
                      formControlName="address"
                      id="address"
                      required
                    />
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="dealAddress" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_TradingAddress || 'Địa chỉ giao dịch' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_PleaseChooseTradingAddress || 'Vui lòng chọn địa chỉ giao dịch (1-250 kí tự)!'"
                >
                  <nz-input-group nzSearch>
                    <input
                      nz-input
                      [placeholder]="language_key?.SupplierInfor_ChooseTradingAddress || 'Chọn địa chỉ giao dịch'"
                      [(ngModel)]="dataObject.dealAddress"
                      formControlName="dealAddress"
                      id="'dealAddress'"
                      required
                    />
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="represen" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_LegalRepresentative || 'Người đại diện pháp luật' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterLegalRepresentative || 'Vui lòng nhập người đại diện pháp luật (1-50 kí tự)!'"
                >
                  <input nz-input formControlName="represen" id="represen" [(ngModel)]="dataObject.represen" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="chief" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_DirectorName || 'Tên giám đốc' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterDirectorName || 'Vui lòng nhập tên giám đốc (1-50 kí tự)!'"
                >
                  <input nz-input formControlName="chief" id="chief" [(ngModel)]="dataObject.chief" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="capital" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_CharterCapital || 'Vốn điều lệ (tỷ đồng)' }}
                </nz-form-label>
                <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.SupplierInfor_EnterCharterCapital || 'Vui lòng nhập vốn điều lệ (tỷ đồng)!'">
                  <input nz-input currencyMask formControlName="capital" id="capital" [(ngModel)]="dataObject.capital" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="assets" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_FixedAssets || 'Tài sản cố định (tỷ đồng)' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterFixedAssets || 'Vui lòng nhập tài sản cố định (tỷ đồng)!'"
                >
                  <input nz-input currencyMask formControlName="assets" id="assets" [(ngModel)]="dataObject.assets" required />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="fileBill" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_SampleInvoiceReceiptFile || 'File hóa đơn mẫu/phiếu thu/biên lai ' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterSampleInvoiceReceiptFile || 'Vui lòng upload file hóa đơn mẫu/phiếu thu/biên lai'"
                >
                  <label for="fileBill" class="custom-file-upload">
                    <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                  </label>
                  <input class="hidden" type="file" id="fileBill" formControlName="fileBill" (change)="handleFileInput($event, 'fileBill')" />
                  <div class="tooltip" *ngIf="dataObject.fileBill && dataObject.fileBill.length > 0">
                    <a href="{{ dataObject.fileBill }}" target="_blank"> {{ language_key?.SupplierInfor_ViewFile || 'Xem file' }} </a>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="fileInfoBill" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_InvoiceIssuanceInforFile || 'File thông tin phát hành hóa đơn ' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_UpInvoiceIssuanceInforFile || 'Vui lòng upload file thông tin phát hành hóa đơn'"
                >
                  <label for="fileInfoBill" class="custom-file-upload">
                    <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                  </label>
                  <input
                    class="hidden"
                    type="file"
                    id="fileInfoBill"
                    formControlName="fileInfoBill"
                    (change)="handleFileInput($event, 'fileInfoBill')"
                  />
                  <div class="tooltip" *ngIf="dataObject.fileInfoBill && dataObject.fileInfoBill.length > 0">
                    <a href="{{ dataObject.fileInfoBill }}" target="_blank"> {{ language_key?.SupplierInfor_ViewFile || 'Xem file' }} </a>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="fileMST" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_BusinessLicenseTaxCode || 'Giấy phép kinh doanh/Mã số thuế' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterBusinessLicenseTaxCode || 'Vui lòng upload giấy phép kinh doanh/Mã số thuế'"
                >
                  <label for="fileMST" class="custom-file-upload">
                    <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                  </label>
                  <input class="hidden" type="file" id="fileMST" formControlName="fileMST" (change)="handleFileInput($event, 'fileMST')" />
                  <div class="tooltip" *ngIf="dataObject.fileMST && dataObject.fileMST.length > 0">
                    <a href="{{ dataObject.fileMST }}" target="_blank"> {{ language_key?.SupplierInfor_ViewFile || 'Xem file' }} </a>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="24">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="description" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_DescriptionBusiness || 'Mô tả về doanh nghiệp' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterDescriptionBusiness || 'Vui lòng nhập mô tả về doanh nghiệp (1-250 kí tự)!'"
                >
                  <textarea nz-input formControlName="description" id="description" [(ngModel)]="dataObject.description" rows="5" auto></textarea>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>

      <nz-collapse nzBordered="false" class="mt-2">
        <nz-collapse-panel
          [nzHeader]="language_key?.SupplierInfor_AccountInformation || 'Thông tin tài khoản'"
          class="ant-bg-antiquewhite"
          nzActive="true"
        >
          <nz-row nzGutter="8">
            <nz-col nzSpan="24">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="bankname" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_BankName || 'Tên ngân hàng' }}
                </nz-form-label>
                <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.SupplierInfor_EnterBankName || 'Vui lòng nhập tên ngân hàng (1-250 kí tự)!'">
                  <input nz-input formControlName="bankname" id="bankname" [(ngModel)]="dataObject.bankname" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="bankNumber" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_BankNumber || 'Số tài khoản ngân hàng' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterBankNumber || 'Vui lòng nhập số tài khoản ngân hàng (1-50 kí tự)!'"
                >
                  <input nz-input formControlName="bankNumber" id="bankNumber" [(ngModel)]="dataObject.bankNumber" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="bankBrand" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_BankBranch || 'Tên chi nhánh ngân hàng' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_BankBranch || 'Vui lòng nhập tên chi nhánh ngân hàng (1-250 kí tự)!'"
                >
                  <input nz-input formControlName="bankBrand" id="bankBrand" [(ngModel)]="dataObject.bankBrand" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="fileAccount" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_FileNoticeOpenAccount || 'File thông báo mở tài khoản/mẫu 08' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_UpFileNoticeOpenAccount || 'Vui lòng upload file thông báo mở tài khoản/mẫu 08'"
                >
                  <label for="fileAccount" class="custom-file-upload">
                    <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                  </label>
                  <input
                    class="hidden"
                    type="file"
                    id="fileAccount"
                    formControlName="fileAccount"
                    (change)="handleFileInput($event, 'fileAccount')"
                  />
                  <div class="tooltip" *ngIf="dataObject.fileAccount && dataObject.fileAccount.length > 0">
                    <a href="{{ dataObject.fileAccount }}" target="_blank"> {{ language_key?.SupplierInfor_ViewFile || 'Xem file' }} </a>
                  </div>
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="contactName" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_ContactName || 'Người liên hệ' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterContactName || 'Vui lòng nhập người liên hệ (1-50 kí tự)!'"
                >
                  <input nz-input formControlName="contactName" id="contactName" [(ngModel)]="dataObject.contactName" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row nzGutter="8">
            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="phone" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_PhoneNumber || 'Số điện thoại' }}
                </nz-form-label>
                <nz-form-control
                  nzSpan="24"
                  [nzErrorTip]="language_key?.SupplierInfor_EnterPhoneNumber || 'Vui lòng nhập số điện thoại (1-50 kí tự)!'"
                >
                  <input nz-input formControlName="phone" id="phone" [(ngModel)]="dataObject.phone" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzSpan="24" nzFor="email" nzRequired class="text-left">
                  {{ language_key?.SupplierInfor_Email || 'Email' }}
                </nz-form-label>
                <nz-form-control nzSpan="24" [nzErrorTip]="language_key?.SupplierInfor_EnterEmail || 'Vui lòng nhập email (1-50 kí tự)!'">
                  <input nz-input formControlName="email" id="email" [(ngModel)]="dataObject.email" />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
        </nz-collapse-panel>
      </nz-collapse>
      <nz-row class="mt-2">
        <nz-col nzSpan="24" class="text-center">
          <button nz-button nzType="primary" class="mr-2" (click)="saveLaw()">{{ language_key?.SupplierInfor_Save || 'Lưu' }}</button>
          <button nz-button class="ant-btn-success" (click)="addCapacities()">
            {{ language_key?.SupplierInfor_AddCapacity || 'Thêm năng lực' }}
          </button>
        </nz-col>
      </nz-row>
    </form>
  </nz-tab>

  <nz-tab *ngFor="let supplierService of lstSupplierService; index as i" [nzTitle]="supplierService.serviceName">
    <div *ngIf="supplierService.status !== statusNew">
      <nz-row class="mt-2">
        <button class="mr-2" nz-button (click)="clickExportExcel(supplierService)">
          <span nz-icon nzType="download"></span>{{ language_key?.SupplierInfor_ExportExcel || 'Xuất excel' }}
        </button>
        <input
          class="hidden"
          type="file"
          [id]="'fileCapacity' + i"
          (change)="clickImportExcel($event, supplierService)"
          onclick="this.value=null"
          accept=".csv,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        />
        <label nz-button [for]="'fileCapacity' + i" class="lable-custom-file">
          <span nz-icon nzType="upload"></span>{{ language_key?.SupplierInfor_ImportExcel || 'Nhập excel' }}
        </label>
      </nz-row>

      <nz-row class="mt-2">
        <nz-table nz-col nzSpan="24" [nzData]="supplierService.lstCapacity" [(nzPageSize)]="pageSize" [nzShowPagination]="false" nzBordered>
          <thead>
            <tr>
              <th>{{ language_key?.SupplierInfor_Criteria || 'Tiêu chí' }}</th>
              <th>{{ language_key?.SupplierInfor_DataType || 'Kiểu dữ liệu' }}</th>
              <th>{{ language_key?.SupplierInfor_Value || 'Giá trị' }}</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let data1 of supplierService.lstCapacity">
              <tr>
                <td class="w-50">
                  <span *ngIf="data1.isRequired" class="text-danger">*</span>
                  <span nz-tooltip [nzTooltipTitle]="data1.name">{{ data1.name }}</span>
                </td>
                <td>{{ data1.type }}</td>
                <td (click)="resetError(data1)">
                  <div *ngIf="data1.__childs__.length == 0">
                    <div *ngIf="!data1.isChangeByYear">
                      <div *ngIf="data1.type === dataType.String.code">
                        <input nz-input [(ngModel)]="data1.value" name="value" />
                      </div>
                      <div *ngIf="data1.type === dataType.Number.code">
                        <input nz-input currencyMask [(ngModel)]="data1.value" name="value" />
                      </div>
                      <div *ngIf="data1.type === dataType.Date.code">
                        <nz-date-picker [(ngModel)]="data1.value" name="value"> </nz-date-picker>
                      </div>
                      <div *ngIf="data1.type === dataType.List.code">
                        <nz-select nzShowSearch nzAllowClear [(ngModel)]="data1.value" name="value">
                          <nz-option
                            *ngFor="let itemList of data1.__supplierCapacityListDetails__"
                            [nzLabel]="itemList.name"
                            [nzValue]="itemList.id"
                          ></nz-option>
                        </nz-select>
                      </div>
                      <div *ngIf="data1.type === dataType.File.code">
                        <nz-input-group nzSearch>
                          <input class="passenger-input" nz-input [(ngModel)]="data1.value" name="value" disabled />
                          <button nz-button nzType="primary" (click)="handleClearFile(data1)">
                            <span nz-icon nzType="delete"></span>
                          </button>
                        </nz-input-group>
                        <label [for]="'zen' + data1.id" class="custom-file-upload">
                          <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                        </label>
                        <input class="hidden" type="file" [id]="'zen' + data1.id" (change)="handleFileInput($event, data1)" />
                      </div>
                    </div>
                    <div *ngIf="data1.isChangeByYear">
                      <div *ngFor="let detailYear of data1.listDetailYear; index as i1" nz-col nzSpan="24" class="form-item">
                        <nz-input-group nzCompact class="change-by-year-item">
                          <input
                            nz-input
                            [placeholder]="language_key?.SupplierInfor_Year || 'Năm'"
                            [(ngModel)]="detailYear.year"
                            [ngModelOptions]="{ standalone: true }"
                            class="year"
                          />
                          <input
                            *ngIf="data1.type === dataType.String.code"
                            nz-input
                            [(ngModel)]="detailYear.value"
                            [ngModelOptions]="{ standalone: true }"
                            class="value"
                            [placeholder]="language_key?.SupplierInfor_Value || 'Giá trị'"
                          />
                          <input
                            *ngIf="data1.type === dataType.Number.code"
                            nz-input
                            currencyMask
                            [placeholder]="language_key?.SupplierInfor_Value || 'Giá trị'"
                            [(ngModel)]="detailYear.value"
                            [ngModelOptions]="{ standalone: true }"
                            class="value"
                          />

                          <input
                            *ngIf="data1.type === dataType.File.code"
                            nz-input
                            [placeholder]="language_key?.SupplierInfor_ChooseFile || 'Chọn file'"
                            disabled
                            [ngModelOptions]="{ standalone: true }"
                            class="value"
                            [(ngModel)]="detailYear.value"
                          />
                          <button nz-button type="button" nzType="primary" (click)="deleteChangeByYear(data1, i1)">
                            <span nz-icon nzType="minus"></span>
                          </button>
                          <label *ngIf="data1.type === dataType.File.code" [for]="'zen' + data1.id + i1" class="custom-file-upload">
                            <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                          </label>
                          <input
                            *ngIf="data1.type === dataType.File.code"
                            class="hidden"
                            type="file"
                            [id]="'zen' + data1.id + i1"
                            (change)="handleFileInput($event, detailYear)"
                          />
                        </nz-input-group>
                      </div>
                      <nz-col nzSpan="24" class="form-item">
                        <button nz-button type="button" nzType="dashed" class="add-change-by-year" (click)="addChangeByYear(data1)">
                          <span nz-icon nzType="plus"></span>
                          {{ language_key?.SupplierInfor_AddValue || 'Thêm giá trị' }}
                        </button>
                      </nz-col>
                    </div>
                  </div>
                  <span *ngIf="data1.isError" class="text-danger">{{ data1.errorText }}</span>
                </td>
              </tr>
              <ng-container>
                <tr *ngFor="let data2 of data1.__childs__">
                  <td [nzIndentSize]="20" class="w-50">
                    <span *ngIf="data2.isRequired" class="text-danger">*</span>
                    <span nz-tooltip [nzTooltipTitle]="data2.name">{{ data2.name }}</span>
                  </td>
                  <td>{{ data2.type }}</td>
                  <td (click)="resetError(data2)">
                    <div *ngIf="!data2.isChangeByYear">
                      <div *ngIf="data2.type === dataType.String.code">
                        <input nz-input [(ngModel)]="data2.value" name="value" />
                      </div>
                      <div *ngIf="data2.type === dataType.Number.code">
                        <input nz-input currencyMask [(ngModel)]="data2.value" name="value" />
                      </div>
                      <div *ngIf="data2.type === dataType.Date.code">
                        <nz-date-picker [(ngModel)]="data2.value" name="value"> </nz-date-picker>
                      </div>
                      <div *ngIf="data2.type === dataType.List.code">
                        <nz-select nzShowSearch nzAllowClear [(ngModel)]="data2.value" name="value">
                          <nz-option
                            *ngFor="let itemList of data2.__supplierCapacityListDetails__"
                            [nzLabel]="itemList.name"
                            [nzValue]="itemList.id"
                          ></nz-option>
                        </nz-select>
                      </div>
                      <div *ngIf="data2.type === dataType.File.code">
                        <nz-input-group nzSearch>
                          <input class="passenger-input" nz-input [(ngModel)]="data2.value" name="value" disabled />
                          <button nz-button nzType="primary" (click)="handleClearFile(data2)">
                            <span nz-icon nzType="delete"></span>
                          </button>
                        </nz-input-group>
                        <label [for]="'zen' + data2.id" class="custom-file-upload">
                          <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                        </label>
                        <input class="hidden" type="file" [id]="'zen' + data2.id" (change)="handleFileInput($event, data2)" />
                      </div>
                    </div>
                    <div *ngIf="data2.isChangeByYear">
                      <nz-col nzSpan="24" *ngFor="let detailYear of data2.listDetailYear; index as i2" class="form-item">
                        <nz-input-group nzCompact class="change-by-year-item">
                          <input
                            nz-input
                            [placeholder]="language_key?.SupplierInfor_Year || 'Năm'"
                            [(ngModel)]="detailYear.year"
                            [ngModelOptions]="{ standalone: true }"
                            class="year"
                          />
                          <input
                            *ngIf="data2.type === dataType.String.code"
                            nz-input
                            [(ngModel)]="detailYear.value"
                            [ngModelOptions]="{ standalone: true }"
                            class="value"
                            [placeholder]="language_key?.SupplierInfor_Value || 'Giá trị'"
                          />
                          <input
                            *ngIf="data2.type === dataType.Number.code"
                            nz-input
                            currencyMask
                            [placeholder]="language_key?.SupplierInfor_Value || 'Giá trị'"
                            [(ngModel)]="detailYear.value"
                            [ngModelOptions]="{ standalone: true }"
                            class="value"
                          />

                          <input
                            *ngIf="data2.type === dataType.File.code"
                            nz-input
                            [placeholder]="language_key?.SupplierInfor_ChooseFile || 'Chọn file'"
                            disabled
                            [ngModelOptions]="{ standalone: true }"
                            class="value"
                            [(ngModel)]="detailYear.value"
                          />
                          <button nz-button type="button" nzType="primary" (click)="deleteChangeByYear(data2, i2)">
                            <span nz-icon nzType="minus"></span>
                          </button>
                          <label *ngIf="data2.type === dataType.File.code" [for]="'zen' + data2.id + i2" class="custom-file-upload">
                            <span nz-icon nzType="upload"></span> {{ language_key?.SupplierInfor_UploadFile || 'Upload File' }}
                          </label>
                          <input
                            *ngIf="data2.type === dataType.File.code"
                            class="hidden"
                            type="file"
                            [id]="'zen' + data2.id + i2"
                            (change)="handleFileInput($event, detailYear)"
                          />
                        </nz-input-group>
                      </nz-col>
                      <nz-col nzSpan="24" class="form-item">
                        <button nz-button type="button" nzType="dashed" class="add-change-by-year" (click)="addChangeByYear(data2)">
                          <span nz-icon nzType="plus"></span>
                          {{ language_key?.SupplierInfor_AddValue || 'Thêm giá trị' }}
                        </button>
                      </nz-col>
                    </div>
                    <span *ngIf="data2.isError" class="text-danger">{{ data2.errorText }}</span>
                  </td>
                </tr>
              </ng-container>
            </ng-container>
          </tbody>
        </nz-table>
      </nz-row>
      <nz-row class="mt-2">
        <nz-col nzSpan="24" class="text-center">
          <button nz-button nzType="primary" (click)="saveCapacity(supplierService)">{{ language_key?.SupplierInfor_Save || 'Lưu' }}</button>
        </nz-col>
      </nz-row>
    </div>
    <div *ngIf="supplierService.status === statusNew">
      <app-supplier-additional-capacity [service]="supplierService"></app-supplier-additional-capacity>
    </div>
  </nz-tab>
</nz-tabset>
