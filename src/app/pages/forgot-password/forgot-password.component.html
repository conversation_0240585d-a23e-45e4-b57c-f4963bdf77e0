<form nz-form [formGroup]="validateForm" (ngSubmit)="submitForm()">
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzRequired nzFor="email">E-mail</nz-form-label>
    <nz-form-control [nzSm]="24" [nzXs]="24" [nzErrorTip]="language_key?.ForgetPassword_EmailNotValid || 'Email không hợp lệ!'">
      <input nz-input formControlName="email" id="email" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item nz-row class="forgot-password-area">
    <nz-form-control>
      <button nz-button nzType="primary" type="button" [disabled]="validateForm.controls.email.invalid" (click)="sendConfirmCode()">
        <span nz-icon nzType="redo"></span>
        {{ language_key?.ForgetPassword_SendConfirmCode || '<PERSON><PERSON>i mã xác nhận tới <PERSON>' }}
      </button>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="confirmCode" nzRequired>{{
      language_key?.ForgetPassword_ConfirmCode || 'Mã xác nhận'
    }}</nz-form-label>
    <nz-form-control [nzSm]="24" [nzXs]="24" [nzErrorTip]="language_key?.ForgetPassword_PleaseInputConfirmCode || 'Vui lòng nhập mã xác nhận!'">
      <input nz-input id="confirmCode" formControlName="confirmCode" [placeholder]="language_key?.ForgetPassword_ConfirmCode || 'Mã xác nhận'" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="password" nzRequired>{{
      language_key?.ForgetPassword_NewPassword || 'Mật khẩu mới'
    }}</nz-form-label>
    <nz-form-control [nzSm]="24" [nzXs]="24" [nzErrorTip]="language_key?.ForgetPassword_PleaseInputNewPassword || 'Vui lòng nhập mật khẩu mới!'">
      <input nz-input type="password" id="newPassword" formControlName="newPassword" (ngModelChange)="updateConfirmValidator()" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="24" [nzXs]="24" nzFor="confirmNewPassword" nzRequired>{{
      language_key?.ForgetPassword_InputNewPassword || 'Nhập lại mật khẩu mới'
    }}</nz-form-label>
    <nz-form-control [nzSm]="24" [nzXs]="24" [nzErrorTip]="errorTpl">
      <input nz-input type="password" formControlName="confirmNewPassword" id="confirmNewPassword" />
      <ng-template #errorTpl let-control>
        <ng-container *ngIf="control.hasError('required')">
          {{ language_key?.ForgetPassword_PleaseReInputNewPassword || 'Vui lòng nhập lại mật khẩu mới!' }}
        </ng-container>
        <ng-container *ngIf="control.hasError('confirm')">
          {{ language_key?.ForgetPassword_PasswordUnmatched || 'Mật khẩu không khớp!' }}
        </ng-container>
      </ng-template>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item nz-row class="text-center">
    <nz-form-control>
      <button nz-button nzType="primary" [disabled]="validateForm.invalid">
        <span nz-icon nzType="save"></span>
        {{ language_key?.ForgetPassword_ChangePassword || 'Đổi mật khẩu' }}
      </button>
    </nz-form-control>
  </nz-form-item>
</form>
