import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ApiService, NotifyService, CoreService } from '../../services'

@Component({ templateUrl: './faq-category.component.html' })
export class FaqCategoryComponent implements OnInit {
  listOfData: any[] = []
  constructor(private apiService: ApiService, public coreService: CoreService, private notifyService: NotifyService, private router: Router) {}

  ngOnInit() {
    this.loadFaqCategory()
  }

  loadFaqCategory() {
    this.notifyService.showloading()
    this.apiService.get(this.apiService.CLIENT_WEB.GET_FAQ_CATEGORY, {}).then((res) => {
      this.notifyService.hideloading()
      this.listOfData = res || []
    })
  }

  viewFAQ(object: any) {
    this.router.navigate([`faq`], {
      queryParams: {
        id: object.id,
      },
    })
  }
}
