import { Injectable } from '@angular/core'
import { Router, CanActivate } from '@angular/router'
import { AuthenticationService } from '../services'
import * as $ from 'jquery'

@Injectable({ providedIn: 'root' })
export class BaseGuard implements CanActivate {
  constructor(private router: Router, private authenticationService: AuthenticationService) {}

  async canActivate() {
    const currentUser = this.authenticationService.currentUserValue
    if (currentUser) {
      if ($('#custom-header')) {
        $('#custom-header').trigger('click')
      }
      return true
    }
    if (localStorage.getItem('currentUser')) {
      await this.authenticationService.authorization()
    }
    return true
  }
}
