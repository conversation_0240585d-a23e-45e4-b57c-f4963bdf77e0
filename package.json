{"name": "ape-bidding-client", "version": "0.0.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration=production", "watch": "ng build --watch --configuration production", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.12", "@angular/cdk": "17.3.10", "@angular/common": "^17.3.12", "@angular/compiler": "^17.3.12", "@angular/core": "^17.3.12", "@angular/forms": "^17.3.12", "@angular/material": "17.3.10", "@angular/platform-browser": "^17.3.12", "@angular/platform-browser-dynamic": "^17.3.12", "@angular/router": "^17.3.12", "@ctrl/tinycolor": "^4.1.0", "css-loader": "^7.1.2", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "jquery": "^3.6.3", "mathjs": "^11.6.0", "moment": "^2.29.4", "ng-zorro-antd": "^17.3.0", "ng2-currency-mask": "^13.0.3", "ng2-pdf-viewer": "^9.1.4", "protractor": "^7.0.0", "rxjs": "~7.8.0", "ts-node": "^10.9.1", "tslib": "^2.6.3", "tslint": "^6.1.3", "uuid": "^9.0.0", "xlsx": "^0.18.5", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.12", "@angular/cli": "~17.3.12", "@angular/compiler-cli": "^17.3.12", "@types/file-saver": "^2.0.5", "@types/jquery": "^3.5.16", "@types/uuid": "^9.0.1", "browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001701", "jasmine-core": "~4.5.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.1", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.4.5"}}