{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ape-bidding-client": {"projectType": "application", "schematics": {"@schematics/angular:component": {"inlineTemplate": false, "style": "scss", "skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["core-js", "@babel/runtime/helpers/slicedToArray", "@babel/runtime/helpers/get", "@babel/runtime/helpers/toConsumableArray", "@babel/runtime/helpers/inherits", "@babel/runtime/helpers/possibleConstructorReturn", "@babel/runtime/regenerator", "seedrandom", "decimal.js", "raf", "xlsx", "clone-deep", "file-saver", "fraction.js", "complex.js", "pdfmake/build/pdfmake.js", "typed-function", "j<PERSON>y", "file-saver", "exceljs"], "outputPath": "dist/ape-bidding-client", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"]}, "configurations": {"production": {"optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}}, "budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"port": 3402, "open": true, "buildTarget": "ape-bidding-client:build"}, "configurations": {"production": {"buildTarget": "ape-bidding-client:build:production"}, "development": {"buildTarget": "ape-bidding-client:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "ape-bidding-client:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "6d54df23-5fa4-4a8f-9d4e-6d996ee40491"}}